<?php

use App\Livewire\Auth\Login;
use App\Livewire\Blog\CategoryPosts;
use App\Livewire\Blog\Index as BlogIndex;
use App\Livewire\Blog\Show as BlogShow;
use App\Livewire\Faq\FaqList;
use App\Livewire\Review\Index as ReviewIndex;
use App\Livewire\Review\Show as ReviewShow;
use Illuminate\Support\Facades\Route;

Route::get('/',\App\Livewire\Home::class)->name('home');
Route::get('/coach/{slug?}-{id?}',\App\Livewire\Profile::class)->name('profile');

Route::get('/login',Login::class)->name('login');
Route::get('/signup',\App\Livewire\Auth\Signup::class)->name('signup');
Route::get('/coach-login', function() {
    return redirect()->route('filament.coach.auth.login');
})->name('coach.login');

//Route::get('/apply-coach', function() {
//    return redirect()->route('filament.public.pages.apply-coach');
//})->name('apply.coach');


Route::middleware('customer.auth')->group(function () {
    Route::get('/checkout',\App\Livewire\Checkout::class)->name('checkout');

});

Route::post('/logout', function() {
    auth('customer')->logout();
    session()->invalidate();
    session()->regenerateToken();
    return redirect()->route('home');
})->name('logout');

// Blog Routes
Route::prefix('blog')->group(function () {
    Route::get('/', BlogIndex::class)->name('blog.index');
    Route::get('/category/{slug}', CategoryPosts::class)->name('blog.category');
    Route::get('/{slug}', BlogShow::class)->name('blog.show');
});

// FAQ Routes
Route::get('/faq/{type?}', FaqList::class)->name('faq');

// Review Routes
Route::prefix('reviews')->group(function () {
    Route::get('/', ReviewIndex::class)->name('reviews.index');
    Route::get('/detail/{slug}', ReviewShow::class)->name('reviews.show');
});

Route::get('/{slug?}',\App\Livewire\Category::class)->name('service');
