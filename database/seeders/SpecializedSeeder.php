<?php

namespace Database\Seeders;

use App\Models\Specialized;
use Illuminate\Database\Seeder;

class SpecializedSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $specializations = [
            ['name' => 'Software Development'],
            ['name' => 'Data Science'],
            ['name' => 'Product Management'],
            ['name' => 'UX/UI Design'],
            ['name' => 'DevOps'],
            ['name' => 'Cloud Computing'],
            ['name' => 'Cybersecurity'],
            ['name' => 'Artificial Intelligence'],
            ['name' => 'Machine Learning'],
            ['name' => 'Blockchain'],
            ['name' => 'Digital Marketing'],
            ['name' => 'Content Strategy'],
            ['name' => 'Business Analysis'],
            ['name' => 'Project Management'],
            ['name' => 'Financial Analysis'],
            ['name' => 'Human Resources'],
            ['name' => 'Operations Management'],
            ['name' => 'Supply Chain Management'],
            ['name' => 'Sales Strategy'],
            ['name' => 'Customer Success'],
        ];

        foreach ($specializations as $specialization) {
            Specialized::create($specialization);
        }
    }
}
