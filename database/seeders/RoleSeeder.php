<?php

namespace Database\Seeders;

use App\Models\Role;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            ['name' => 'Software Engineer'],
            ['name' => 'Product Manager'],
            ['name' => 'Data Scientist'],
            ['name' => 'UX Designer'],
            ['name' => 'DevOps Engineer'],
            ['name' => 'Project Manager'],
            ['name' => 'Business Analyst'],
            ['name' => 'Marketing Manager'],
            ['name' => 'Sales Manager'],
            ['name' => 'Financial Analyst'],
            ['name' => 'HR Manager'],
            ['name' => 'Operations Manager'],
            ['name' => 'Customer Success Manager'],
            ['name' => 'Technical Writer'],
            ['name' => 'Quality Assurance Engineer'],
            ['name' => 'Frontend Developer'],
            ['name' => 'Backend Developer'],
            ['name' => 'Full Stack Developer'],
            ['name' => 'Mobile Developer'],
            ['name' => 'Machine Learning Engineer'],
        ];

        foreach ($roles as $role) {
            Role::create($role);
        }
    }
}
