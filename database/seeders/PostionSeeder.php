<?php

namespace Database\Seeders;

use App\Models\Postion;
use Illuminate\Database\Seeder;

class PostionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $positions = [
            ['name' => 'Junior'],
            ['name' => 'Mid-level'],
            ['name' => 'Senior'],
            ['name' => 'Lead'],
            ['name' => 'Manager'],
            ['name' => 'Director'],
            ['name' => 'VP'],
            ['name' => 'C-level'],
            ['name' => 'Founder'],
            ['name' => 'Intern'],
            ['name' => 'Associate'],
            ['name' => 'Principal'],
            ['name' => 'Consultant'],
            ['name' => 'Architect'],
            ['name' => 'Head of Department'],
            ['name' => 'Team Lead'],
            ['name' => 'Specialist'],
            ['name' => 'Analyst'],
            ['name' => 'Coordinator'],
            ['name' => 'Executive'],
        ];

        foreach ($positions as $position) {
            Postion::create($position);
        }
    }
}
