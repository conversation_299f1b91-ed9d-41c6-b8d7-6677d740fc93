<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user
//        User::factory()->create([
//            'name' => 'Test User',
//            'email' => '<EMAIL>',
//        ]);

        // Run all seeders in the correct order
        $this->call([
            // First, seed the basic data models
            CompanySeeder::class,
            RoleSeeder::class,
            ServiceSeeder::class,
            SpecializedSeeder::class,
            PostionSeeder::class,
            FaqTypeSeeder::class,

            // Then, seed models that depend on the basic data
            CoacheSeeder::class,
            CustomerSeeder::class,

            // Finally, seed models that depend on coaches and other models

            // Seed coaching platform models
            PackageSeeder::class,
            PriceConfigurationSeeder::class,
            OrderSeeder::class,
        ]);
    }
}
