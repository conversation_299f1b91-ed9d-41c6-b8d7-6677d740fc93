<?php

namespace Database\Seeders;

use App\Models\Coache;
use App\Models\CoachingSession;
use App\Models\Customer;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Package;
use App\Models\Payment;
use App\Models\PriceConfiguration;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all customers, coaches, and packages
        $customers = Customer::all();
        $coaches = Coache::all();
        $packages = Package::all();
        
        // Create orders
        foreach ($customers as $index => $customer) {
            // Each customer has 1-3 orders
            $numOrders = rand(1, 3);
            
            for ($i = 0; $i < $numOrders; $i++) {
                // Create order
                $order = Order::create([
                    'customer_id' => $customer->id,
                    'total_amount' => 0, // Will be updated after adding order items
                    'status' => Order::STATUS_PAID,
                    'payment_method' => rand(0, 1) ? 'credit_card' : 'paypal',
                    'notes' => 'Order created via seeder',
                ]);
                
                // Add 1-2 order items per order
                $numOrderItems = rand(1, 2);
                $totalAmount = 0;
                
                for ($j = 0; $j < $numOrderItems; $j++) {
                    // Random coach and package
                    $coach = $coaches->random();
                    $package = $packages->random();
                    
                    // Get price configuration for this coach and package
                    $priceConfig = PriceConfiguration::where('coach_id', $coach->id)
                        ->where('package_id', $package->id)
                        ->first();
                    
                    // If no specific price config exists, use the default one
                    if (!$priceConfig) {
                        $priceConfig = PriceConfiguration::whereNull('coach_id')
                            ->where('package_id', $package->id)
                            ->first();
                    }
                    
                    $pricePerHour = $priceConfig ? $priceConfig->price_per_hour : 100;
                    $subtotal = $pricePerHour * $package->hours;
                    $totalAmount += $subtotal;
                    
                    // Create order item
                    $orderItem = OrderItem::create([
                        'order_id' => $order->id,
                        'coach_id' => $coach->id,
                        'package_id' => $package->id,
                        'hours_purchased' => $package->hours,
                        'hours_used' => rand(0, $package->hours), // Some hours might be used already
                        'price_per_hour' => $pricePerHour,
                        'subtotal' => $subtotal,
                        'status' => OrderItem::STATUS_ACTIVE,
                        'expiry_date' => Carbon::now()->addMonths(3), // Expires in 3 months
                    ]);
                    
                    // Create 0-3 coaching sessions for this order item
                    $hoursUsed = 0;
                    $numSessions = rand(0, 3);
                    
                    for ($k = 0; $k < $numSessions && $hoursUsed < $orderItem->hours_purchased; $k++) {
                        // Session duration (in minutes)
                        $duration = rand(1, 2) * 60; // 1 or 2 hours
                        $hoursUsed += $duration / 60;
                        
                        // Don't exceed purchased hours
                        if ($hoursUsed > $orderItem->hours_purchased) {
                            $duration = ($orderItem->hours_purchased - ($hoursUsed - ($duration / 60))) * 60;
                            $hoursUsed = $orderItem->hours_purchased;
                        }
                        
                        // Session start time (random time in the past month)
                        $startTime = Carbon::now()->subDays(rand(1, 30))->setHour(rand(9, 17))->setMinute(0)->setSecond(0);
                        $endTime = (clone $startTime)->addMinutes($duration);
                        
                        // Session status
                        $status = $startTime->isPast() ? CoachingSession::STATUS_COMPLETED : CoachingSession::STATUS_SCHEDULED;
                        
                        // Create session
                        CoachingSession::create([
                            'order_item_id' => $orderItem->id,
                            'coach_id' => $coach->id,
                            'customer_id' => $customer->id,
                            'start_time' => $startTime,
                            'end_time' => $endTime,
                            'duration' => $duration,
                            'status' => $status,
                            'notes' => 'Session created via seeder',
                        ]);
                    }
                    
                    // Update hours used
                    $orderItem->update(['hours_used' => $hoursUsed]);
                }
                
                // Update order total
                $order->update(['total_amount' => $totalAmount]);
                
                // Create payment
                Payment::create([
                    'order_id' => $order->id,
                    'amount' => $totalAmount,
                    'payment_method' => $order->payment_method,
                    'transaction_id' => 'TRANS' . str_pad(rand(1, 999999), 6, '0', STR_PAD_LEFT),
                    'status' => Payment::STATUS_COMPLETED,
                    'payment_date' => $order->created_at,
                ]);
            }
        }
    }
}
