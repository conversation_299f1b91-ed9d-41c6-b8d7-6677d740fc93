<?php

namespace Database\Seeders;

use App\Models\Company;
use Illuminate\Database\Seeder;

class CompanySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $companies = [
            ['name' => 'Google'],
            ['name' => 'Microsoft'],
            ['name' => 'Amazon'],
            ['name' => 'Apple'],
            ['name' => 'Facebook'],
            ['name' => 'Netflix'],
            ['name' => 'Tesla'],
            ['name' => 'IBM'],
            ['name' => 'Oracle'],
            ['name' => 'Intel'],
            ['name' => 'Samsung'],
            ['name' => 'Sony'],
            ['name' => 'Deloitte'],
            ['name' => 'McKinsey'],
            ['name' => 'Boston Consulting Group'],
            ['name' => 'Bain & Company'],
            ['name' => 'Goldman Sachs'],
            ['name' => 'JPMorgan Chase'],
            ['name' => 'Morgan Stanley'],
            ['name' => 'Bank of America'],
        ];

        foreach ($companies as $company) {
            Company::create($company);
        }
    }
}
