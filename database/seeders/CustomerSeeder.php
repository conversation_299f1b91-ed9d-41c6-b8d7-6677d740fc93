<?php

namespace Database\Seeders;

use App\Models\Customer;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class CustomerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $customers = [
            [
                'full_name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
            ],
            [
                'full_name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
            ],
            [
                'full_name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
            ],
            [
                'full_name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
            ],
            [
                'full_name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
            ],
            [
                'full_name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
            ],
            [
                'full_name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
            ],
            [
                'full_name' => 'Jennifer <PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
            ],
            [
                'full_name' => '<PERSON> <PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
            ],
            [
                'full_name' => 'Jessica Thomas',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
            ],
            [
                'full_name' => 'Matthew Jackson',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
            ],
            [
                'full_name' => 'Amanda White',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
            ],
            [
                'full_name' => 'Daniel Harris',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
            ],
            [
                'full_name' => 'Melissa Martin',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
            ],
            [
                'full_name' => 'Andrew Thompson',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
            ],
            [
                'full_name' => 'Stephanie Garcia',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
            ],
            [
                'full_name' => 'Kevin Martinez',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
            ],
            [
                'full_name' => 'Nicole Robinson',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
            ],
            [
                'full_name' => 'Joshua Clark',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
            ],
            [
                'full_name' => 'Rebecca Rodriguez',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
            ],
        ];

        foreach ($customers as $customer) {
            Customer::create($customer);
        }
    }
}
