<?php

namespace Database\Seeders;

use App\Models\FaqType;
use Illuminate\Database\Seeder;

class FaqTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faqTypes = [
            [
                'name' => 'Home',
                'slug' => 'home',
                'description' => 'Frequently asked questions about our services',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Apply Coach',
                'slug' => 'apply-coach',
                'description' => 'Frequently asked questions about applying to be a coach',
                'is_active' => true,
                'sort_order' => 2,
            ],
        ];

        foreach ($faqTypes as $faqType) {
            FaqType::updateOrCreate(
                ['slug' => $faqType['slug']],
                $faqType
            );
        }
    }
}
