<?php

namespace Database\Seeders;

use App\Models\Coache;
use App\Models\Package;
use App\Models\PriceConfiguration;
use Illuminate\Database\Seeder;

class PriceConfigurationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all packages
        $packages = Package::all();
        
        // Create default pricing for all packages
        foreach ($packages as $package) {
            // Base price per hour (higher for smaller packages, discount for larger packages)
            $basePrice = 100;
            
            // Apply discount for larger packages
            if ($package->hours <= 3) {
                $pricePerHour = $basePrice;
            } elseif ($package->hours <= 7) {
                $pricePerHour = $basePrice * 0.95; // 5% discount
            } elseif ($package->hours <= 15) {
                $pricePerHour = $basePrice * 0.9; // 10% discount
            } else {
                $pricePerHour = $basePrice * 0.85; // 15% discount
            }
            
            // Create default price configuration (no coach_id means default pricing)
            PriceConfiguration::create([
                'coach_id' => null,
                'package_id' => $package->id,
                'price_per_hour' => $pricePerHour,
                'is_active' => true,
            ]);
        }
        
        // Get all coaches
        $coaches = Coache::all();
        
        // Create custom pricing for some coaches
        foreach ($coaches as $index => $coach) {
            // Only create custom pricing for some coaches (every third coach)
            if ($index % 3 === 0) {
                foreach ($packages as $package) {
                    // Base price varies by coach experience/reputation
                    $coachBasePrice = 100 + ($index * 10); // More experienced coaches charge more
                    
                    // Apply discount for larger packages
                    if ($package->hours <= 3) {
                        $pricePerHour = $coachBasePrice;
                    } elseif ($package->hours <= 7) {
                        $pricePerHour = $coachBasePrice * 0.95; // 5% discount
                    } elseif ($package->hours <= 15) {
                        $pricePerHour = $coachBasePrice * 0.9; // 10% discount
                    } else {
                        $pricePerHour = $coachBasePrice * 0.85; // 15% discount
                    }
                    
                    // Create coach-specific price configuration
                    PriceConfiguration::create([
                        'coach_id' => $coach->id,
                        'package_id' => $package->id,
                        'price_per_hour' => $pricePerHour,
                        'is_active' => true,
                    ]);
                }
            }
        }
    }
}
