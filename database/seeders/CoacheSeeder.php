<?php

namespace Database\Seeders;

use App\Models\Coache;
use App\Models\Company;
use App\Models\Role;
use App\Models\Service;
use App\Models\Specialized;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class CoacheSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get IDs for relationships
        $companyIds = Company::pluck('id')->toArray();
        $roleIds = Role::pluck('id')->toArray();
        $serviceIds = Service::pluck('id')->toArray();
        $specializedIds = Specialized::pluck('id')->toArray();

        $coaches = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '************',
                'short_description' => 'Experienced career coach with 10+ years in tech industry',
                'about' => '<p><PERSON> is a seasoned career coach with over 10 years of experience in the tech industry. He has helped hundreds of professionals advance their careers at top companies like Google, Microsoft, and Amazon.</p><p><PERSON> specializes in career transitions, interview preparation, and leadership development. His coaching approach combines practical industry insights with personalized guidance to help clients achieve their professional goals.</p>',
                'specialties' => [
                    'company' => 'Tech Career Advisors',
                    'location' => 'San Francisco, CA'
                ]
            ],
            [
                'name' => '<PERSON> <PERSON>',
                'email' => '<EMAIL>',
                'phone' => '************',
                'short_description' => 'Former HR director specializing in resume optimization and interview coaching',
                'about' => '<p>Sarah <PERSON> is a former HR director with 15 years of experience in talent acquisition and development. She has worked at Fortune 500 companies and has reviewed thousands of resumes and conducted countless interviews.</p><p>Sarah now helps job seekers optimize their resumes, prepare for interviews, and navigate the hiring process. Her insider knowledge of HR practices gives clients a competitive edge in their job search.</p>',
                'specialties' => [
                    'company' => 'Career Success Partners',
                    'location' => 'New York, NY'
                ]
            ],
            [
                'name' => 'Michael Chen',
                'email' => '<EMAIL>',
                'phone' => '************',
                'short_description' => 'Tech industry veteran specializing in software engineering career paths',
                'about' => '<p>Michael Chen is a tech industry veteran with 20 years of experience in software engineering and leadership roles. He has worked at startups and major tech companies, leading teams and mentoring junior engineers.</p><p>Michael specializes in helping software engineers advance their careers, transition to leadership roles, and navigate the unique challenges of the tech industry. His coaching combines technical expertise with career development strategies.</p>',
                'specialties' => [
                    'company' => 'Tech Career Mastery',
                    'location' => 'Seattle, WA'
                ]
            ],
            [
                'name' => 'Emily Rodriguez',
                'email' => '<EMAIL>',
                'phone' => '************',
                'short_description' => 'Executive coach specializing in leadership development and career transitions',
                'about' => '<p>Emily Rodriguez is an executive coach with a background in organizational psychology. She has coached executives and leaders at Fortune 100 companies, helping them develop their leadership skills and navigate career transitions.</p><p>Emily specializes in leadership development, executive presence, and strategic career planning. Her coaching approach is based on evidence-based practices and tailored to each client\'s unique goals and challenges.</p>',
                'specialties' => [
                    'company' => 'Executive Edge Coaching',
                    'location' => 'Chicago, IL'
                ]
            ],
            [
                'name' => 'David Kim',
                'email' => '<EMAIL>',
                'phone' => '************',
                'short_description' => 'Product management expert with experience at top tech companies',
                'about' => '<p>David Kim is a product management expert with experience at top tech companies including Google, Facebook, and Amazon. He has led product teams and launched successful products used by millions of users.</p><p>David specializes in helping product managers and aspiring PMs advance their careers, develop their product sense, and prepare for PM interviews. His coaching combines product expertise with practical career advice.</p>',
                'specialties' => [
                    'company' => 'Product Career Lab',
                    'location' => 'San Jose, CA'
                ]
            ],
            [
                'name' => 'Lisa Patel',
                'email' => '<EMAIL>',
                'phone' => '************',
                'short_description' => 'Data science career coach with background in analytics and machine learning',
                'about' => '<p>Lisa Patel is a data science career coach with a background in analytics and machine learning. She has worked at tech companies and consulting firms, applying data science to solve business problems.</p><p>Lisa specializes in helping data scientists, analysts, and aspiring data professionals advance their careers, develop their technical skills, and prepare for interviews. Her coaching combines technical expertise with career development strategies.</p>',
                'specialties' => [
                    'company' => 'Data Career Accelerator',
                    'location' => 'Boston, MA'
                ]
            ],
            [
                'name' => 'James Wilson',
                'email' => '<EMAIL>',
                'phone' => '************',
                'short_description' => 'Finance industry veteran specializing in career transitions and interview preparation',
                'about' => '<p>James Wilson is a finance industry veteran with 25 years of experience in investment banking, private equity, and corporate finance. He has worked at top financial institutions and has extensive experience in hiring and developing talent.</p><p>James specializes in helping finance professionals advance their careers, transition to new roles, and prepare for interviews. His coaching combines industry expertise with practical career advice.</p>',
                'specialties' => [
                    'company' => 'Finance Career Advisors',
                    'location' => 'New York, NY'
                ]
            ],
            [
                'name' => 'Sophia Lee',
                'email' => '<EMAIL>',
                'phone' => '************',
                'short_description' => 'UX design career coach with background in product design',
                'about' => '<p>Sophia Lee is a UX design career coach with a background in product design. She has worked at tech companies and design agencies, creating user-centered digital experiences.</p><p>Sophia specializes in helping UX designers, UI designers, and aspiring design professionals advance their careers, develop their portfolios, and prepare for interviews. Her coaching combines design expertise with career development strategies.</p>',
                'specialties' => [
                    'company' => 'Design Career Lab',
                    'location' => 'San Francisco, CA'
                ]
            ],
            [
                'name' => 'Robert Taylor',
                'email' => '<EMAIL>',
                'phone' => '************',
                'short_description' => 'Marketing executive specializing in career development for marketing professionals',
                'about' => '<p>Robert Taylor is a marketing executive with 20 years of experience in brand management, digital marketing, and marketing strategy. He has worked at consumer goods companies and tech firms, leading marketing teams and campaigns.</p><p>Robert specializes in helping marketing professionals advance their careers, develop their skills, and navigate the evolving marketing landscape. His coaching combines marketing expertise with practical career advice.</p>',
                'specialties' => [
                    'company' => 'Marketing Career Strategies',
                    'location' => 'Los Angeles, CA'
                ]
            ],
            [
                'name' => 'Jennifer Martinez',
                'email' => '<EMAIL>',
                'phone' => '************',
                'short_description' => 'HR professional specializing in career development and job search strategies',
                'about' => '<p>Jennifer Martinez is an HR professional with 15 years of experience in talent acquisition, employee development, and HR management. She has worked at companies across various industries, developing and implementing HR strategies.</p><p>Jennifer specializes in helping professionals develop effective job search strategies, prepare for interviews, and navigate career transitions. Her coaching combines HR expertise with practical career advice.</p>',
                'specialties' => [
                    'company' => 'Career Development Partners',
                    'location' => 'Miami, FL'
                ]
            ],
        ];

        foreach ($coaches as $coachData) {
            // Create the coach
            $coach = Coache::create([
                'name' => $coachData['name'],
                'email' => $coachData['email'],
                'password' => Hash::make('password123'), // Default password
                'phone' => $coachData['phone'],
                'short_description' => $coachData['short_description'],
                'about' => $coachData['about'],
                'specialties' => $coachData['specialties'],
            ]);

            // Attach random companies (2-4)
            $randomCompanyIds = array_rand(array_flip($companyIds), rand(2, 4));
            $coach->companies()->attach($randomCompanyIds);

            // Attach random roles (2-4)
            $randomRoleIds = array_rand(array_flip($roleIds), rand(2, 4));
            $coach->roles()->attach($randomRoleIds);

            // Attach random services (3-6)
            $randomServiceIds = array_rand(array_flip($serviceIds), rand(3, 6));
            $coach->services()->attach($randomServiceIds);

            // Attach random specializations (2-5)
            $randomSpecializedIds = array_rand(array_flip($specializedIds), rand(2, 5));
            $coach->specialized()->attach($randomSpecializedIds);
        }
    }
}
