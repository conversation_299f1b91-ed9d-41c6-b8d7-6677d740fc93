<?php

namespace Database\Seeders;

use App\Models\Service;
use Illuminate\Database\Seeder;

class ServiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $services = [
            [
                'name' => 'Career Coaching',
                'content' => 'Get personalized guidance to advance your career and achieve your professional goals.',
                'title' => 'Expert Career Coaching Services',
                'status' => 1
            ],
            [
                'name' => 'Resume Review',
                'content' => 'Have your resume professionally reviewed and optimized to stand out to employers.',
                'title' => 'Professional Resume Review Services',
                'status' => 1
            ],
            [
                'name' => 'Interview Preparation',
                'content' => 'Prepare for your upcoming interviews with mock interviews and personalized feedback.',
                'title' => 'Comprehensive Interview Preparation',
                'status' => 1
            ],
            [
                'name' => 'LinkedIn Profile Optimization',
                'content' => 'Optimize your LinkedIn profile to attract recruiters and showcase your professional brand.',
                'title' => 'LinkedIn Profile Enhancement Services',
                'status' => 1
            ],
            [
                'name' => 'Job Search Strategy',
                'content' => 'Develop a targeted job search strategy to find opportunities that match your skills and goals.',
                'title' => 'Effective Job Search Strategy Services',
                'status' => 1
            ],
            [
                'name' => 'Salary Negotiation',
                'content' => 'Learn effective salary negotiation techniques to maximize your compensation package.',
                'title' => 'Expert Salary Negotiation Coaching',
                'status' => 1
            ],
            [
                'name' => 'Career Transition',
                'content' => 'Get guidance on transitioning to a new career path or industry.',
                'title' => 'Career Transition Coaching Services',
                'status' => 1
            ],
            [
                'name' => 'Leadership Development',
                'content' => 'Develop your leadership skills to advance to management and executive positions.',
                'title' => 'Leadership Development Coaching',
                'status' => 1
            ],
            [
                'name' => 'Personal Branding',
                'content' => 'Build a strong personal brand to stand out in your industry and attract opportunities.',
                'title' => 'Personal Branding Strategy Services',
                'status' => 1
            ],
            [
                'name' => 'Networking Strategy',
                'content' => 'Learn effective networking strategies to build professional relationships and find opportunities.',
                'title' => 'Professional Networking Strategy Coaching',
                'status' => 1
            ],
        ];

        foreach ($services as $service) {
            Service::create($service);
        }
    }
}
