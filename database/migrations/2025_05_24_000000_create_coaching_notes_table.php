<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('coaching_notes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('coaching_session_id')->constrained('coaching_sessions')->onDelete('cascade');
            $table->text('content');
            $table->foreignId('created_by')->constrained('coaches')->onDelete('cascade');
            $table->boolean('is_private')->default(false);
            $table->timestamps();
            
            // Add index for faster queries
            $table->index(['coaching_session_id', 'created_by']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('coaching_notes');
    }
};
