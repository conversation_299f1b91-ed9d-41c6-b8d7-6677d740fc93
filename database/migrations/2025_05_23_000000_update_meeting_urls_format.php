<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get all coaching sessions with meeting URLs
        $sessions = DB::table('coaching_sessions')
            ->whereNotNull('meeting_url')
            ->where('meeting_url', '<>', '')
            ->get();
            
        foreach ($sessions as $session) {
            $url = $session->meeting_url;
            
            // Ensure URL has https:// prefix
            if (!str_starts_with($url, 'http://') && !str_starts_with($url, 'https://')) {
                $url = 'https://' . $url;
                
                // Update the record
                DB::table('coaching_sessions')
                    ->where('id', $session->id)
                    ->update(['meeting_url' => $url]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration cannot be reversed as we don't know which URLs originally had the prefix
    }
};
