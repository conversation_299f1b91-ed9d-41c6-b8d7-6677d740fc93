<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get the current enum values
        $tableInfo = DB::select("SHOW COLUMNS FROM coaching_sessions WHERE Field = 'status'");
        $enumStr = $tableInfo[0]->Type;
        
        // Extract the enum values
        preg_match('/^enum\((.*)\)$/', $enumStr, $matches);
        $enumValues = str_getcsv($matches[1], ',', "'");
        
        // Check if 'in-progress' is already in the enum
        if (!in_array('in-progress', $enumValues)) {
            // Add 'in-progress' to the enum values
            $enumValues[] = 'in-progress';
            
            // Create the new enum string
            $newEnumStr = "enum('" . implode("','", $enumValues) . "')";
            
            // Update the column type
            DB::statement("ALTER TABLE coaching_sessions MODIFY COLUMN status $newEnumStr");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Get the current enum values
        $tableInfo = DB::select("SHOW COLUMNS FROM coaching_sessions WHERE Field = 'status'");
        $enumStr = $tableInfo[0]->Type;
        
        // Extract the enum values
        preg_match('/^enum\((.*)\)$/', $enumStr, $matches);
        $enumValues = str_getcsv($matches[1], ',', "'");
        
        // Remove 'in-progress' from the enum values
        $enumValues = array_filter($enumValues, function($value) {
            return $value !== 'in-progress';
        });
        
        // Create the new enum string
        $newEnumStr = "enum('" . implode("','", $enumValues) . "')";
        
        // Update the column type
        DB::statement("ALTER TABLE coaching_sessions MODIFY COLUMN status $newEnumStr");
        
        // Update any 'in-progress' sessions to 'scheduled'
        DB::table('coaching_sessions')
            ->where('status', 'in-progress')
            ->update(['status' => 'scheduled']);
    }
};
