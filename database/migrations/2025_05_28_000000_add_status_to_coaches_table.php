<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Coache;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('coaches', function (Blueprint $table) {
            $table->string('status')->default(Coache::STATUS_APPROVED)->after('specialties');
            $table->string('password')->nullable()->change();
        });

        // Update existing coaches to approved status
        DB::table('coaches')->update(['status' => Coache::STATUS_APPROVED]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('coaches', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
};
