<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // No schema changes needed, just adding a new status value
        // The status column already exists and can accept the new value
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Convert any 'in-progress' sessions back to 'scheduled'
        DB::table('coaching_sessions')
            ->where('status', 'in-progress')
            ->update(['status' => 'scheduled']);
    }
};
