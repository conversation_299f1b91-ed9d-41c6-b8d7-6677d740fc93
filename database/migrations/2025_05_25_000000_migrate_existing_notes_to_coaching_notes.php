<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\CoachingSession;
use App\Models\CoachingNote;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get all coaching sessions with notes
        $sessions = DB::table('coaching_sessions')
            ->whereNotNull('notes')
            ->where('notes', '<>', '')
            ->get();
            
        foreach ($sessions as $session) {
            // Create a new coaching note for each session with notes
            if (!empty($session->notes)) {
                DB::table('coaching_notes')->insert([
                    'coaching_session_id' => $session->id,
                    'content' => $session->notes,
                    'created_by' => $session->coach_id,
                    'is_private' => false,
                    'created_at' => $session->updated_at,
                    'updated_at' => $session->updated_at,
                ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration cannot be reversed as it would be difficult to determine
        // which notes were created from the migration and which were added later
    }
};
