<?php

namespace App\Filament\Coach\Resources;

use App\Filament\Coach\Resources\AvailabilityResource\Pages;
use App\Models\CoachAvailability;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class AvailabilityResource extends Resource
{
    protected static ?string $model = CoachAvailability::class;
    protected static ?string $navigationIcon = 'heroicon-o-clock';
    protected static ?string $navigationLabel = 'Lịch rảnh của tôi';
    protected static ?int $navigationSort = 30;
    protected static ?string $modelLabel = 'Lịch rảnh';
    protected static ?string $pluralModelLabel = 'Lịch rảnh';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Hidden::make('coach_id')
                    ->default(fn () => auth('coach')->id()),

                Forms\Components\Select::make('day_of_week')
                    ->label('Ngày trong tuần')
                    ->options(function () {
                        $options = [];
                        $today = now();

                        // Vietnamese day names
                        $vietnameseDays = [
                            0 => 'Chủ Nhật',
                            1 => 'Thứ Hai',
                            2 => 'Thứ Ba',
                            3 => 'Thứ Tư',
                            4 => 'Thứ Năm',
                            5 => 'Thứ Sáu',
                            6 => 'Thứ Bảy',
                        ];
                        // Generate options for the next 4 weeks
                        for ($i = 0; $i < 7; $i++) {
                            $date = $today->copy()->addDays($i);
                            $dayOfWeek = $date->dayOfWeek;
                            $formattedDate = $date->format('d/m/Y'); // Vietnamese date format

                            // Use day of week as key, but show date in the option with Vietnamese day name
                            $options[$dayOfWeek] = $vietnameseDays[$dayOfWeek] . ' (' . $formattedDate . ')';
                        }
                        return $options;
                    })
                    ->searchable()
                    ->required(),

                Forms\Components\TimePicker::make('start_time')
                    ->label('Thời gian bắt đầu')
                    ->seconds(false)
                    ->native(false)
                    ->required(),

                Forms\Components\TimePicker::make('end_time')
                    ->label('Thời gian kết thúc')
                    ->seconds(false)
                    ->required()
                    ->native(false)
                    ->after('start_time'),

                Forms\Components\Toggle::make('is_available')
                    ->label('Khả dụng')
                    ->default(true)
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('day_of_week')
                    ->label('Ngày')
                    ->formatStateUsing(function (int $state) {
                        $dayNames = [
                            0 => 'Chủ Nhật',
                            1 => 'Thứ Hai',
                            2 => 'Thứ Ba',
                            3 => 'Thứ Tư',
                            4 => 'Thứ Năm',
                            5 => 'Thứ Sáu',
                            6 => 'Thứ Bảy',
                        ];

                        // Get the next occurrence of this day
                        $today = now();
                        $daysToAdd = ($state - $today->dayOfWeek + 7) % 7;
                        if ($daysToAdd === 0 && $today->format('H') >= 12) {
                            $daysToAdd = 7; // If it's already past noon, show next week
                        }

                        $nextOccurrence = $today->copy()->addDays($daysToAdd);

                        return $dayNames[$state] . ' (' . $nextOccurrence->format('d/m') . ')';
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('start_time')
                    ->label('Thời gian bắt đầu')
                    ->time()
                    ->sortable(),

                Tables\Columns\TextColumn::make('end_time')
                    ->label('Thời gian kết thúc')
                    ->time()
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_available')
                    ->boolean()
                    ->label('Khả dụng'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('day_of_week')
                    ->label('Ngày trong tuần')
                    ->options(function () {
                        $options = [];
                        $today = now();

                        // Vietnamese day names
                        $dayNames = [
                            0 => 'Chủ Nhật',
                            1 => 'Thứ Hai',
                            2 => 'Thứ Ba',
                            3 => 'Thứ Tư',
                            4 => 'Thứ Năm',
                            5 => 'Thứ Sáu',
                            6 => 'Thứ Bảy',
                        ];

                        // Add next occurrence date for each day
                        foreach ($dayNames as $dayNum => $dayName) {
                            $daysToAdd = ($dayNum - $today->dayOfWeek + 7) % 7;
                            if ($daysToAdd === 0 && $today->format('H') >= 12) {
                                $daysToAdd = 7; // If it's already past noon, show next week
                            }

                            $nextOccurrence = $today->copy()->addDays($daysToAdd);
                            $options[$dayNum] = $dayName . ' (' . $nextOccurrence->format('d/m') . ')';
                        }

                        return $options;
                    }),

                Tables\Filters\Filter::make('is_available')
                    ->query(fn (Builder $query): Builder => $query->where('is_available', true))
                    ->label('Chỉ hiện khả dụng')
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('day_of_week')
            ->modifyQueryUsing(function (Builder $query) {
                return $query->where('coach_id', auth('coach')->id());
            });
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAvailabilities::route('/'),
            'create' => Pages\CreateAvailability::route('/create'),
            'edit' => Pages\EditAvailability::route('/{record}/edit'),
        ];
    }
}
