<?php

namespace App\Livewire;

use App\Models\Coache;
use App\Models\CoachAvailability;
use App\Models\CoachingSession;
use Carbon\Carbon;
use Livewire\Component;
use Illuminate\Support\Collection;

class CoachAvailabilitySearch extends Component
{
    public $start_date;
    public $end_date;
    public $start_time;
    public $end_time;
    public $coaches = [];
    public $searchPerformed = false;

    public function mount()
    {
        // Set default date range (today to next week)
        $this->start_date = Carbon::today()->format('Y-m-d');
        $this->end_date = Carbon::today()->addWeek()->format('Y-m-d');
        $this->start_time = '09:00';
        $this->end_time = '17:00';
    }

    public function searchAvailableCoaches()
    {
        $this->validate([
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after_or_equal:start_date',
            'start_time' => 'required',
            'end_time' => 'required|after:start_time',
        ]);

        $startDate = Carbon::parse($this->start_date);
        $endDate = Carbon::parse($this->end_date);
        $startTime = $this->start_time;
        $endTime = $this->end_time;

        $this->coaches = $this->findAvailableCoaches($startDate, $endDate, $startTime, $endTime);
        $this->searchPerformed = true;
    }

    private function findAvailableCoaches($startDate, $endDate, $startTime, $endTime): Collection
    {
        $availableCoaches = collect();

        // Get all approved coaches
        $coaches = Coache::where('status', Coache::STATUS_APPROVED)->get();

        foreach ($coaches as $coach) {
            $coachAvailability = $this->getCoachAvailabilityInRange($coach, $startDate, $endDate, $startTime, $endTime);
            
            if ($coachAvailability->isNotEmpty()) {
                $availableCoaches->push([
                    'coach' => $coach,
                    'available_slots' => $coachAvailability
                ]);
            }
        }

        return $availableCoaches;
    }

    private function getCoachAvailabilityInRange($coach, $startDate, $endDate, $startTime, $endTime): Collection
    {
        $availableSlots = collect();
        $current = $startDate->copy();

        while ($current->lte($endDate)) {
            $dayOfWeek = $current->dayOfWeek;
            
            // Get coach's availability for this day of week
            $availability = CoachAvailability::where('coach_id', $coach->id)
                ->where('day_of_week', $dayOfWeek)
                ->where('is_available', true)
                ->where('start_time', '<=', $endTime)
                ->where('end_time', '>=', $startTime)
                ->first();

            if ($availability) {
                // Check if there are any existing sessions that conflict
                $conflictingSessions = CoachingSession::where('coach_id', $coach->id)
                    ->whereDate('start_time', $current->format('Y-m-d'))
                    ->where(function ($query) use ($startTime, $endTime) {
                        $query->where(function ($q) use ($startTime, $endTime) {
                            $q->whereTime('start_time', '<', $endTime)
                              ->whereTime('end_time', '>', $startTime);
                        });
                    })
                    ->whereIn('status', [
                        CoachingSession::STATUS_SCHEDULED,
                        CoachingSession::STATUS_IN_PROGRESS
                    ])
                    ->exists();

                if (!$conflictingSessions) {
                    // Calculate the actual available time window
                    $availableStart = max($availability->start_time, $startTime);
                    $availableEnd = min($availability->end_time, $endTime);
                    
                    if ($availableStart < $availableEnd) {
                        $availableSlots->push([
                            'date' => $current->format('Y-m-d'),
                            'day_name' => $current->format('l'),
                            'day_name_vi' => $this->getVietnameseDayName($current->dayOfWeek),
                            'start_time' => $availableStart,
                            'end_time' => $availableEnd,
                            'formatted_date' => $current->format('d/m/Y'),
                            'formatted_time' => $availableStart . ' - ' . $availableEnd,
                        ]);
                    }
                }
            }

            $current->addDay();
        }

        return $availableSlots;
    }

    private function getVietnameseDayName($dayOfWeek): string
    {
        $vietnameseDays = [
            0 => 'Chủ Nhật',
            1 => 'Thứ Hai',
            2 => 'Thứ Ba',
            3 => 'Thứ Tư',
            4 => 'Thứ Năm',
            5 => 'Thứ Sáu',
            6 => 'Thứ Bảy',
        ];

        return $vietnameseDays[$dayOfWeek] ?? '';
    }

    public function resetSearch()
    {
        $this->start_date = Carbon::today()->format('Y-m-d');
        $this->end_date = Carbon::today()->addWeek()->format('Y-m-d');
        $this->start_time = '09:00';
        $this->end_time = '17:00';
        $this->coaches = [];
        $this->searchPerformed = false;
    }

    public function render()
    {
        return view('livewire.coach-availability-search');
    }
}
