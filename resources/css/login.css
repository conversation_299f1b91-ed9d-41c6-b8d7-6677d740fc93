main {
    min-height: calc(100vh - 70px);
    padding-bottom: 80px;
    flex-grow: 1;
}
@media (max-width: 960px) {
    main {
        padding-bottom: 48px;
    }
}

body {
    display: flex;
    flex-direction: column;
    background-color: #fbfbfb;
}.mobile-navigation-accordion {
     font-size: 16px;
     line-height: 1.5;
     margin-top: 20px;
     width: 100%;
     display: flex;
     flex-direction: column;
     color: #000;
 }
.accordion-item-head,
.accordion-item-description {
    transition: all 0.2s linear;
}
.accordion-item {
    border-top: 1px solid #eeeeee;
    padding: 10px 0;
}
.accordion-item--open .accordion-item-description {
    margin-top: 8px;
    margin-bottom: 16px;
}
.accordion-item-head {
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #000;
}
.accordion-item-head.accordion-item-head--active {
    color: #0567a7;
}
.accordion-item-description {
    font-size: 14px;
    line-height: 1.57;
    overflow: hidden;
    height: 0px;
    display: flex;
    flex-direction: column;
    font-weight: bold;
}
.group-header {
    font-size: 12px;
    line-height: 1.67;
    font-weight: bold;
    color: #aaaaaa;
    margin: 12px 0;
    text-transform: uppercase;
}
.group-header:first-child {
    margin-top: 0;
}
.nav-item {
    border-left: 1px solid #eeeeee;
    padding: 8px 0 8px 14px;
    display: block;
    color: #000;
    opacity: 0.8;
}
.nav-item > span {
    position: relative;
}
.nav-item--first {
    padding-top: 0;
}
.nav-item--last {
    padding-bottom: 0;
}
.nav-item--active {
    color: #0567a7;
}
.new-badge {
    position: absolute;
    padding: 0 6px;
    border-radius: 2px;
    background-color: #15835c;
    font-weight: bold;
    line-height: 1.57;
    color: #fff;
    text-transform: uppercase;
    font-size: 0.875rem;
    right: 0;
    top: 0;
    transform: translate(calc(100% + 8px), -4px);
}
.new-badge--small {
    font-size: 0.625rem;
    transform: translate(calc(100% + 4px), -4px);
}
.new-badge--child {
    top: 0;
    right: -4px;
}
.new-badge--not-fit {
    right: 0;
    top: 4px;
    transform: translate(10px, -100%);
}
@media (min-width: 960px) {
    .new-badge--not-fit {
        right: 0;
        top: 0;
        transform: translate(calc(100% + 8px), -4px);
    }
}
.new-badge--inline {
    right: 0;
    top: 5px;
}
@media (min-width: 960px) {
    .new-badge--inline {
        right: 2px;
        top: 5px;
        transform: translate(calc(100% + 8px), -4px);
    }
}
.new-badge--inline-text {
    right: -3px;
    top: 7px;
}
@media (min-width: 960px) {
    .new-badge--inline-text {
        right: 2px;
        top: 5px;
        transform: translate(calc(100% + 8px), -4px);
    }
}.nav-item {
     font-size: 16px;
     line-height: 1.5;
     padding: 4px 20px;
     font-weight: bold;
     white-space: nowrap;
     position: relative;
     display: none;
 }
@media (min-width: 1200px) {
    .nav-item {
        display: block;
    }
}
.nav-item .active {
    color: #0567a7;
}
.nav-item.active > a {
    color: #0567a7;
}
.nav-item a {
    color: #333333;
}
.nav-item a:hover {
    opacity: 0.7;
}
.nav-item a img {
    margin: 0 2px;
}
.nav-item:hover .child-items {
    display: flex;
    margin-top: 0;
}
.child-items {
    position: absolute;
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.05);
    border: solid 2px #eeeeee;
    right: auto;
    left: 50%;
    transform: translate(-50%, 0);
    top: calc(100% + 4px);
    z-index: 1000;
    display: none;
    float: none;
    min-width: 254px;
    max-height: calc(100vh - 80px);
    padding: 0.5rem 0;
    margin: 0.125rem 0 0;
    font-size: 1rem;
    text-align: left;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
}
.empty-space {
    width: 100%;
    height: 10px;
    position: absolute;
    top: -11px;
}
.menu-triangle {
    width: 16px;
    height: 16px;
    border-top: 2px solid #eeeeee;
    border-left: 2px solid #eeeeee;
    transform: rotate(45deg);
    position: absolute;
    top: -10px;
    left: 45%;
    z-index: 1001;
    opacity: 1;
    pointer-events: none;
    background-color: #fff;
}
.items-container {
    display: flex;
    flex-direction: column;
    overflow: auto;
}
.menu-divider {
    height: 0;
    margin: 0.5rem 0;
    overflow: hidden;
    border-top: 1px solid #eeeeee;
}
.menu-group-title {
    font-size: 12px;
    line-height: 1.67;
    font-size: 12px;
    font-weight: bold;
    color: #aaaaaa;
    cursor: default;
    text-transform: uppercase;
    padding: 0.5rem 1.5rem;
}
.menu-item {
    font-size: 14px;
    line-height: 1.57;
    box-sizing: border-box;
    white-space: normal;
    color: #242424;
    font-weight: bold;
    cursor: pointer;
    padding: 9px 24px;
    white-space: nowrap;
}
.menu-item > span {
    position: relative;
}
.menu-item:hover {
    opacity: 0.8;
}
.new-badge {
    position: absolute;
    padding: 0 6px;
    border-radius: 2px;
    background-color: #15835c;
    font-weight: bold;
    line-height: 1.57;
    color: #fff;
    text-transform: uppercase;
    font-size: 0.875rem;
    right: 0;
    top: 0;
    transform: translate(calc(100% + 8px), -4px);
}
.new-badge--small {
    font-size: 0.625rem;
    transform: translate(calc(100% + 4px), -4px);
}
.new-badge--child {
    position: relative;
    top: 0;
    right: -4px;
}
.new-badge--not-fit {
    right: 0;
    top: 4px;
    transform: translate(10px, -100%);
}
@media (min-width: 960px) {
    .new-badge--not-fit {
        right: 0;
        top: 0;
        transform: translate(calc(100% + 8px), -4px);
    }
}
.new-badge--inline {
    right: 0;
    top: 5px;
}
@media (min-width: 960px) {
    .new-badge--inline {
        right: 2px;
        top: 5px;
        transform: translate(calc(100% + 8px), -4px);
    }
}
.new-badge--inline-text {
    right: -3px;
    top: 7px;
}
@media (min-width: 960px) {
    .new-badge--inline-text {
        right: 2px;
        top: 5px;
        transform: translate(calc(100% + 8px), -4px);
    }
}
