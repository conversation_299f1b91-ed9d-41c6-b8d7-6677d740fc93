<div class="p-4 bg-white rounded-xl shadow">
    @if($record && $record->isReadyToJoin() && $record->hasMeetingUrl())
        <div class="text-center">
            <h3 class="text-lg font-medium mb-4">Ready to join your session</h3>
            
            <div class="mb-4">
                <p class="text-sm text-gray-600 mb-2">Meeting URL:</p>
                <a href="{{ $record->meeting_url }}" target="_blank" class="text-primary-600 hover:underline break-all">
                    {{ $record->meeting_url }}
                </a>
            </div>
            
            @if($record->meeting_password)
                <div class="mb-4">
                    <p class="text-sm text-gray-600 mb-1">Meeting Password:</p>
                    <p class="font-medium">{{ $record->meeting_password }}</p>
                </div>
            @endif
            
            <a href="{{ $record->meeting_url }}" target="_blank" class="inline-flex items-center justify-center px-4 py-2 bg-success-600 border border-transparent rounded-lg font-medium text-white hover:bg-success-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-success-500 w-full">
                <span class="mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z" />
                    </svg>
                </span>
                Join Meeting Now
            </a>
        </div>
    @endif
</div>
