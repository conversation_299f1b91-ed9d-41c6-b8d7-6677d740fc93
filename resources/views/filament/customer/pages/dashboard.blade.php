<x-filament-panels::page>
    <!-- Hidden logout form -->
    <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
        @csrf
    </form>

    <div class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div class="bg-white rounded-xl shadow p-6">
                <h2 class="text-xl font-bold mb-4">Số giờ của bạn còn {{auth()->guard('customer')->user()->hours_available}}h</h2>
                <div class="text-sm">
                    <p>Hãy sử dụng để thuê huấn luyện viên.</p>
                </div>
            </div>
            <div class="bg-white rounded-xl shadow p-6">
                <h2 class="text-xl font-bold mb-4">Đơn hàng của tôi</h2>
                <div class="text-sm">
                    <p>Xem và quản lý đơn hàng huấn luyện của bạn.</p>
                    <div class="mt-4">
                        <a href="{{ route('filament.customer.resources.orders.index') }}" class="text-primary-600 hover:text-primary-500 font-medium">
                            Xem đơn hàng →
                        </a>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow p-6">
                <h2 class="text-xl font-bold mb-4">My Reviews</h2>
                <div class="text-sm">
                    <p>View and manage your reviews for coaches.</p>
                    <div class="mt-4">
                        <a href="{{ route('filament.customer.resources.reviews.index') }}" class="text-primary-600 hover:text-primary-500 font-medium">
                            View Reviews →
                        </a>
                    </div>
                </div>
            </div>

        </div>

        <div class="mt-6">
            <div class="bg-white rounded-xl shadow p-6">
                <h2 class="text-xl font-bold mb-4">Upcoming Sessions</h2>
                <div class="text-sm">
                    <p>Your upcoming coaching sessions.</p>
                    <div class="mt-4">
                        <a href="{{ route('filament.customer.resources.coaching-sessions.index') }}" class="text-primary-600 hover:text-primary-500 font-medium">
                            View All Sessions →
                        </a>
                    </div>
                </div>
            </div>
        </div>

    </div>
</x-filament-panels::page>
