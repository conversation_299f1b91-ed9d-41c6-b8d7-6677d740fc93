<x-filament-panels::page>
    <div class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">


            <div class="bg-white rounded-xl shadow p-6">
                <h2 class="text-xl font-bold mb-4">My Sessions</h2>
                <div class="text-sm">
                    <p>View and manage your coaching sessions.</p>
                    <div class="mt-4">
                        <a href="{{ route('filament.coach.resources.coaching-sessions.index') }}" class="text-primary-600 hover:text-primary-500 font-medium">
                            View Sessions →
                        </a>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow p-6">
                <h2 class="text-xl font-bold mb-4">My Reviews</h2>
                <div class="text-sm">
                    <p>View reviews from your students.</p>
                    <div class="mt-4">
                        <a href="{{ route('filament.coach.resources.session-reviews.index') }}" class="text-primary-600 hover:text-primary-500 font-medium">
                            View Reviews →
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-6">
            <div class="bg-white rounded-xl shadow p-6">
                <h2 class="text-xl font-bold mb-4">Upcoming Sessions</h2>
                <div class="text-sm">
                    <p>Your upcoming coaching sessions.</p>
                    <div class="mt-4">
                        <a href="{{ route('filament.coach.resources.coaching-sessions.index') }}?filter[upcoming]=true" class="text-primary-600 hover:text-primary-500 font-medium">
                            View Upcoming Sessions →
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
