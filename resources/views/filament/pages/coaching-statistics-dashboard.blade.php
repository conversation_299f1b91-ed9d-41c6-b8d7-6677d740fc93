<x-filament-panels::page>
    <form wire:submit="filter" class="space-y-6">
        {{ $this->form }}

        <div class="flex items-center justify-end gap-x-3">
            <x-filament::button type="button" color="gray" wire:click="resetFilter">
                Đặt lại
            </x-filament::button>

            <x-filament::button type="submit">
                Áp dụng
            </x-filament::button>
        </div>
    </form>

    <div class="mt-8 grid grid-cols-1 gap-6 md:grid-cols-3">
        <!-- Hours Statistics -->
        <x-filament::section>
            <x-slot name="heading">Thống kê giờ</x-slot>

            <dl class="grid grid-cols-1 gap-5 sm:grid-cols-2">
                <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
                    <dt class="truncate text-sm font-medium text-gray-500">Tổng số giờ đã học</dt>
                    <dd class="mt-1 text-3xl font-semibold tracking-tight text-success-600">{{ number_format($totalHoursLearned, 1) }}</dd>
                </div>

                <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6 mt-2">
                    <dt class="truncate text-sm font-medium text-gray-500">Giờ chờ đợi</dt>
                    <dd class="mt-1 text-3xl font-semibold tracking-tight text-warning-600">{{ number_format($totalHoursWaiting, 1) }}</dd>
                </div>

                <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6 mt-2">
                    <dt class="truncate text-sm font-medium text-gray-500">Giờ đang tiến hành</dt>
                    <dd class="mt-1 text-3xl font-semibold tracking-tight text-primary-600">{{ number_format($totalHoursInProgress, 1) }}</dd>
                </div>

                <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6 mt-2">
                    <dt class="truncate text-sm font-medium text-gray-500">Tổng số giờ</dt>
                    <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">{{ number_format($totalHours, 1) }}</dd>
                </div>
            </dl>
        </x-filament::section>

        <!-- Sessions Statistics -->
        <x-filament::section>
            <x-slot name="heading">Thống kê phiên</x-slot>

            <dl class="grid grid-cols-1 gap-5 sm:grid-cols-2">
                <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
                    <dt class="truncate text-sm font-medium text-gray-500">Phiên đã hoàn thành</dt>
                    <dd class="mt-1 text-3xl font-semibold tracking-tight text-success-600">{{ $totalCompletedSessions }}</dd>
                </div>

                <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6 mt-2">
                    <dt class="truncate text-sm font-medium text-gray-500">Phiên họp theo lịch trình</dt>
                    <dd class="mt-1 text-3xl font-semibold tracking-tight text-warning-600">{{ $totalScheduledSessions }}</dd>
                </div>

                <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6 mt-2">
                    <dt class="truncate text-sm font-medium text-gray-500">Phiên đang tiến hành</dt>
                    <dd class="mt-1 text-3xl font-semibold tracking-tight text-primary-600">{{ $totalInProgressSessions }}</dd>
                </div>

                <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6 mt-2">
                    <dt class="truncate text-sm font-medium text-gray-500">Tổng số phiên</dt>
                    <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">{{ $totalSessions }}</dd>
                </div>
            </dl>
        </x-filament::section>

        <!-- Additional Statistics -->
        <x-filament::section>
            <x-slot name="heading">Thống kê bổ sung</x-slot>

            <dl class="grid grid-cols-1 gap-5 sm:grid-cols-2">
                <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
                    <dt class="truncate text-sm font-medium text-gray-500">Phiên đã hủy</dt>
                    <dd class="mt-1 text-3xl font-semibold tracking-tight text-danger-600">{{ $totalCancelledSessions }}</dd>
                </div>

            </dl>
        </x-filament::section>
    </div>

    <!-- Top Coaches -->
    <x-filament::section class="mt-6">
        <x-slot name="heading">Top 5 huấn luyện viên hàng đầu theo giờ dạy</x-slot>

        @if(count($topCoaches) > 0)
            <div class="overflow-hidden">
                <div class="filament-tables-container rounded-xl border border-gray-300 bg-white shadow-sm">
                    <table class="filament-tables-table w-full table-auto divide-y divide-gray-200 text-center">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-600 filament-tables-header-cell">
                                    <span>Tên huấn luyện viên</span>
                                </th>
                                <th class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-600 filament-tables-header-cell">
                                    <span>Giờ giảng dạy</span>
                                </th>
                                <th class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-600 filament-tables-header-cell">
                                    <span>Phiên đã hoàn thành</span>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200 bg-white">
                            @foreach($topCoaches as $coach)
                                <tr class="filament-tables-row transition hover:bg-gray-50">
                                    <td class="filament-tables-cell px-4 py-3 text-sm text-gray-600">{{ $coach['coach_name'] }}</td>
                                    <td class="filament-tables-cell px-4 py-3 text-sm text-gray-600">{{ $coach['total_hours'] }}</td>
                                    <td class="filament-tables-cell px-4 py-3 text-sm text-gray-600">{{ $coach['total_sessions'] }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        @else
            <div class="filament-tables-empty-state flex flex-1 flex-col items-center justify-center p-6 text-center">
                <div class="filament-tables-empty-state-icon mb-4 rounded-full bg-primary-50 p-3 text-primary-500">
                    <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </div>
                <h2 class="filament-tables-empty-state-heading text-xl font-bold tracking-tight">
                    Không tìm thấy dữ liệu
                </h2>
                <p class="filament-tables-empty-state-description mt-1 text-sm font-medium text-gray-500">
                    Không có dữ liệu nào có sẵn cho khoảng thời gian đã chọn.
                </p>
            </div>
        @endif
    </x-filament::section>
</x-filament-panels::page>
