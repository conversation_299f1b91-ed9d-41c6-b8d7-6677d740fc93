<style>
    h1[data-astro-cid-2k7izlee].coach-details-page-heading {
        font-size: 24px;
        line-height: 1.42;
        margin-bottom: 24px;
        text-align: left
    }

    .coach-details-page-section-wrapper[data-astro-cid-2k7izlee] {
        margin-top: 0 !important
    }

    .buy-sessions-btn[data-astro-cid-2k7izlee] {
        width: 100%
    }

    .section-wrapper[data-astro-cid-2k7izlee] {
        max-width: 1080px;
        margin: auto;
        padding: 0 16px;
        text-align: center;
        margin-top: 48px
    }

    @media (min-width: 960px) {
        .section-wrapper[data-astro-cid-2k7izlee] {
            margin-top: 80px
        }
    }

    footer[data-astro-cid-mtxgg6pp] {
        padding: 48px 0 48px;
        background: #242424;
        box-shadow: 0 50vh 0 50vh #242424;
        color: #ffffff;
        font-size: 16px
    }

    @media (max-width: 960px) {
        footer[data-astro-cid-mtxgg6pp] {
            font-size: 14px;
            padding-bottom: 32px
        }
    }

    footer[data-astro-cid-mtxgg6pp] a[data-astro-cid-mtxgg6pp] {
        color: inherit
    }

    .links-container[data-astro-cid-mtxgg6pp] {
        max-width: 995px;
        padding: 0 20px;
        display: grid;
        grid-template-columns: 1fr 1fr auto;
        row-gap: 32px;
        margin: auto
    }

    @media (max-width: 960px) {
        .links-container[data-astro-cid-mtxgg6pp] {
            grid-template-columns:1fr;
            row-gap: 16px
        }
    }

    .footer-group[data-astro-cid-mtxgg6pp] {
        display: flex;
        flex-direction: column
    }

    .footer-heading[data-astro-cid-mtxgg6pp] {
        text-transform: uppercase;
        font-weight: 600;
        margin-bottom: 16px
    }

    @media (max-width: 960px) {
        .footer-heading[data-astro-cid-mtxgg6pp] {
            margin-top: 12px
        }
    }

    .footer-link[data-astro-cid-mtxgg6pp] {
        margin-bottom: 12px;
        line-height: 26px;
        opacity: 0.8
    }

    .footer-link[data-astro-cid-mtxgg6pp]:hover {
        opacity: 0.5
    }

    .footer-copyright[data-astro-cid-mtxgg6pp] {
        margin: auto;
        max-width: 995px;
        padding: 0 20px;
        margin-top: 36px;
        font-size: 12px;
        opacity: 0.8
    }

    @media (max-width: 960px) {
        .footer-copyright[data-astro-cid-mtxgg6pp] {
            margin-top: 20px
        }
    }

    .footer-copyright[data-astro-cid-mtxgg6pp] a[data-astro-cid-mtxgg6pp]:hover {
        opacity: 0.8
    }
</style>
<style>
    nav[data-astro-cid-d77rco4z].landing-page-sub-navigation {
        width: 100%;
        border-bottom: 2px solid #eeeeee;
        height: 60px;
        padding: 0 64px;
        align-items: center;
        background-color: #fff;
        display: none
    }

    @media (min-width: 1200px) {
        nav[data-astro-cid-d77rco4z].landing-page-sub-navigation {
            display: flex
        }
    }

    nav[data-astro-cid-d77rco4z].landing-page-sub-navigation .nav-item, nav[data-astro-cid-d77rco4z].landing-page-sub-navigation .menu-item {
        font-weight: 600;
        color: #4a4a4a
    }

    nav[data-astro-cid-d77rco4z].landing-page-sub-navigation .nav-item.active, nav[data-astro-cid-d77rco4z].landing-page-sub-navigation .menu-item.active {
        color: #0567a7
    }

    @font-face {
        font-family: "Open Sans";
        font-style: italic;
        font-weight: 400;
        font-stretch: 100%;
        font-display: swap;
        src: url(/fonts/open-sans-400-italic.woff2) format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD
    }

    @font-face {
        font-family: "Open Sans";
        font-style: normal;
        font-weight: 400;
        font-stretch: 100%;
        font-display: swap;
        src: url(/fonts/open-sans-400.woff2) format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD
    }

    @font-face {
        font-family: "Open Sans";
        font-style: normal;
        font-weight: 600;
        font-stretch: 100%;
        font-display: swap;
        src: url(/fonts/open-sans-600.woff2) format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD
    }

    @font-face {
        font-family: "Open Sans";
        font-style: normal;
        font-weight: 700;
        font-stretch: 100%;
        font-display: swap;
        src: url(/fonts/open-sans-700.woff2) format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD
    }

    body {
        font-size: 18px;
        line-height: 1.56;
        font-family: "Open Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
        font-weight: 400;
        color: #4a4a4a;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.004)
    }
</style>
<style>
    .account-buttons[data-astro-cid-hmtt24ci] {
        display: flex;
        flex-direction: column;
        width: 100%
    }

    .account-buttons[data-astro-cid-hmtt24ci] a.btn:not(:last-child) {
        margin-bottom: 12px
    }

    .mobile-navigation-container[data-astro-cid-hmtt24ci] {
        display: none;
        position: fixed;
        top: 0;
        width: 100vw;
        left: 0;
        flex-direction: column;
        align-items: start;
        background: #ffffff;
        z-index: 400;
        padding: 86px 24px 156px;
        height: 100vh;
        overflow: auto
    }

    .mobile-navigation-container[data-astro-cid-hmtt24ci].open {
        display: flex
    }

    nav[data-astro-cid-2xhbaixd] {
        flex-grow: 1;
        display: flex
    }

    .fake-header[data-astro-cid-xbstl6g3] {
        display: flex;
        min-height: 70px;
        width: 100%
    }

    .mobile-sub-navigation[data-astro-cid-xbstl6g3] {
        height: 58px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fff;
        border-bottom: 2px solid #eeeeee;
        color: #4a4a4a
    }

    @media (min-width: 1200px) {
        .mobile-sub-navigation[data-astro-cid-xbstl6g3] {
            display: none
        }
    }

    .mobile-sub-navigation[data-astro-cid-xbstl6g3] a[data-astro-cid-xbstl6g3].navigation-link {
        position: relative;
        box-sizing: border-box;
        text-decoration: none;
        font-weight: 600;
        cursor: pointer;
        color: #000;
        white-space: nowrap;
        font-size: 14px
    }

    .mobile-sub-navigation[data-astro-cid-xbstl6g3] a[data-astro-cid-xbstl6g3].navigation-link.active {
        color: #0567a7
    }

    .mobile-sub-navigation[data-astro-cid-xbstl6g3] a[data-astro-cid-xbstl6g3].navigation-link:not(:last-child) {
        margin-right: 16px
    }

    header[data-astro-cid-xbstl6g3] {
        position: fixed;
        width: 100%;
        box-sizing: border-box;
        height: 70px;
        border-bottom: 6px solid #eeeeee;
        background-color: #ffffff;
        padding: 10px 24px;
        display: flex;
        z-index: 500
    }

    @media (min-width: 1200px) {
        header[data-astro-cid-xbstl6g3] {
            padding: 10px 64px
        }
    }

    header[data-astro-cid-xbstl6g3] .header-container[data-astro-cid-xbstl6g3] {
        display: flex;
        width: 100%;
        align-items: center;
        margin: auto
    }

    header[data-astro-cid-xbstl6g3] .logo[data-astro-cid-xbstl6g3] {
        display: flex
    }

    header[data-astro-cid-xbstl6g3] .logo[data-astro-cid-xbstl6g3] > a[data-astro-cid-xbstl6g3] {
        display: flex
    }

    .igao-link[data-v-bb39e5c0] {
        font-weight: 600;
        font-stretch: normal;
        font-style: normal;
        letter-spacing: normal;
        cursor: pointer;
    }

    .igao-link[disabled=true][data-v-bb39e5c0] {
        opacity: 0.56;
        pointer-events: none;
    }

    a.igao-link--primary[data-v-bb39e5c0] {
        color: #0567a7;
    }

    a.igao-link--primary[data-v-bb39e5c0]:hover {
        color: #0567a7;
    }

    a.igao-link--secondary[data-v-bb39e5c0] {
        color: #aaaaaa;
        text-decoration: underline;
    }

    a.igao-link--secondary[data-v-bb39e5c0]:hover, a.igao-link--secondary[data-v-bb39e5c0]:active {
        color: #4a4a4a;
    }

    .coach-details-back-link-container[data-v-e9b99ea4] {
        text-align: left;
        margin: 24px 0;
    }

    .coach-details-back-link-container a[data-v-e9b99ea4] {
        font-size: 14px;
        display: flex;
        align-items: center;
    }

    .coach-details-back-link-container a img[data-v-e9b99ea4] {
        margin-right: 8px;
    }

    @media (min-width: 960px) {
        .coach-details-wrapper--with-sidebar[data-v-900fb83f] {
            display: grid;
            grid-template-columns: 1fr 328px;
            gap: 32px;
        }
    }

    .coach-details-wrapper--with-sidebar[data-v-900fb83f] .coach-details-card-role {
        max-width: 100% !important;
    }

    @media (max-width: 960px) {
        .coach-details-sidebar-container[data-v-900fb83f] {
            display: none;
        }
    }

    .coach-details-sidebar[data-v-900fb83f] {
        display: flex;
        flex-direction: column;
        border: 1px solid #eeeeee;
        background: #fff;
        padding: 32px;
        gap: 32px;
        position: sticky;
        top: 94px;
    }

    .coach-details-sidebar-ctas[data-v-900fb83f] {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .coach-details-wrapper-price[data-v-900fb83f] {
        justify-content: center;
    }

    .coach-details-sidebar-sessions-left[data-v-900fb83f] {
        display: flex;
        flex-direction: column;
        color: #878787;
        font-size: 12px;
        line-height: 20px;
        /* 166.667% */
        gap: 4px;
    }

    .coach-details-sidebar-sessions-left > span[data-v-900fb83f] {
        font-style: italic;
    }

    .coach-details-sidebar-reassurance[data-v-900fb83f] {
        margin-top: 32px;
        font-size: 14px;
        line-height: 22px;
    }

    .coach-details-ctas-mobile[data-v-900fb83f] {
        position: fixed;
        z-index: 100;
        bottom: 0;
        left: 0;
        width: 100vw;
        display: flex;
        padding: 16px;
        gap: 16px;
        border-top: 1px solid #eee;
        background: rgba(255, 255, 255, 0.95);
        box-shadow: 0px -1px 4px 0px rgba(170, 170, 170, 0.25);
        -webkit-backdrop-filter: blur(1.5px);
        backdrop-filter: blur(1.5px);
    }

    .coach-details-ctas-mobile[data-v-900fb83f] .btn:last-child {
        width: 100%;
    }

    @media (min-width: 960px) {
        .coach-details-ctas-mobile[data-v-900fb83f] {
            display: none;
        }
    }

    @media (max-width: 960px) {
        .BeaconFabButtonFrame {
            bottom: 82px !important;
        }

        body {
            padding-bottom: 73px;
        }
    }

    .coach-badge-container[data-v-92fb9e4b] {
        display: inline-flex;
        justify-content: flex-start;
        align-items: center;
        gap: 4px;
        flex-wrap: wrap;
    }

    @media (min-width: 960px) {
        .coach-badge-container[data-v-92fb9e4b] {
            gap: 8px;
        }
    }

    .coach-badge[data-v-92fb9e4b] {
        text-transform: uppercase;
        color: #fff;
        text-align: center;
        font-size: 10px;
        font-style: normal;
        font-weight: 700;
        line-height: 14px;
        /* 140% */
        letter-spacing: -0.1px;
        display: inline-flex;
        padding: 3px 8px;
        justify-content: center;
        align-items: center;
    }

    .coach-badge--bogof[data-v-92fb9e4b] {
        background: #15835c;
    }

    .coach-badge--supercoach[data-v-92fb9e4b] {
        background: #9e6a1b;
    }

    .coach-badge--certified[data-v-92fb9e4b] {
        background: #666666;
    }
</style>
<link rel="stylesheet" href="{{asset('css/app/index.CMsmmUru.css')}}">
<link rel="stylesheet" href="{{asset('css/app/index.Bp6vNROH.css')}}">
<style>
    .tag-like-link[data-v-d93fe582] {
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 3px 8px;
        background-color: #fff;
        border: 1px solid #eeeeee;
        color: #333333;
        white-space: nowrap;
        transition-property: background;
        transition-duration: 200ms;
        transition-timing-function: cubic-bezier(0, 0, 1, 1);
    }

    .tag-like-link--s[data-v-d93fe582] {
        font-size: 12px;
        line-height: 1.67;
    }

    .tag-like-link--m[data-v-d93fe582] {
        font-size: 16px;
        line-height: 1.5;
    }

    .tag-like-link--l[data-v-d93fe582] {
        font-size: 18px;
        line-height: 1.56;
    }

    .tag-like-link--not-interactive[data-v-d93fe582] {
        pointer-events: none;
    }

    .tag-like-link[data-v-d93fe582]:hover {
        cursor: pointer;
        background-color: #fbfbfb;
    }

    .content-wrapper[data-v-e2113d11] {
        font-size: 12px;
        line-height: 1.67;
        z-index: 500;
        display: none;
        font-weight: 600;
        font-stretch: normal;
        font-style: normal;
        letter-spacing: normal;
        color: #333;
        box-shadow: 0 2px 4px 0 rgba(51, 51, 51, 0.12);
        padding: 11px 12px;
        max-width: 282px;
        white-space: normal;
        background-color: #fff;
        border: 1px solid #eee;
    }

    .content-wrapper .popover-arrow[data-v-e2113d11], .content-wrapper .popover-arrow[data-v-e2113d11]::before {
        position: absolute;
        width: 8px;
        height: 8px;
        background: inherit;
    }

    .content-wrapper .popover-arrow[data-v-e2113d11] {
        visibility: hidden;
    }

    .content-wrapper .popover-arrow[data-v-e2113d11]::before {
        visibility: visible;
        content: "";
        transform: rotate(45deg);
    }

    .content-wrapper[data-show][data-v-e2113d11] {
        display: block;
    }

    .content-wrapper[data-popper-placement^=top] > .popover-arrow[data-v-e2113d11] {
        bottom: -4px;
    }

    .content-wrapper[data-popper-placement^=top] > .popover-arrow[data-v-e2113d11]::before {
        border-right: 1px solid #eee;
        border-bottom: 1px solid #eee;
    }

    .content-wrapper[data-popper-placement^=left] > .popover-arrow[data-v-e2113d11]::before {
        border-right: 1px solid #eee;
        border-top: 1px solid #eee;
    }

    .content-wrapper[data-popper-placement^=right] > .popover-arrow[data-v-e2113d11]::before {
        border-left: 1px solid #eee;
        border-bottom: 1px solid #eee;
    }

    .content-wrapper[data-popper-placement^=bottom] > .popover-arrow[data-v-e2113d11] {
        top: -4px;
    }

    .content-wrapper[data-popper-placement^=bottom] > .popover-arrow[data-v-e2113d11]::before {
        border-left: 1px solid #eee;
        border-top: 1px solid #eee;
    }

    .content-wrapper[data-popper-placement^=left] > .popover-arrow[data-v-e2113d11] {
        right: -4px;
    }

    .content-wrapper[data-popper-placement^=right] > .popover-arrow[data-v-e2113d11] {
        left: -4px;
    }

    .tooltip-wrapper-inline[data-v-b905ec3a] {
        display: inline;
    }

    .tooltip-wrapper-flex[data-v-b905ec3a] {
        display: flex;
    }
</style>
<link rel="stylesheet" href="{{asset('css/app/_coachSlug_.B9rErNik.css')}}">
<style>
    .spinner-container[data-v-15ddf44a] {
        text-align: center;
        position: relative;
        min-height: 132px;
        height: 100%;
        padding: 16px 0;
    }

    .spinner-container .loading-coaches-spinner[data-v-15ddf44a] {
        position: absolute;
        top: calc(50% - 50px);
        left: calc(50% - 50px);
        color: #0567a7;
        width: var(--spinner-size, 100px);
        height: var(--spinner-size, 100px);
        border-width: 4px;
    }

    .coach-buying-reassurance[data-v-99fd961d] {
        color: #4a4a4a;
        gap: 8px;
        display: inline-flex;
        flex-direction: column;
    }

    .coach-buying-reassurance > div[data-v-99fd961d] {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 8px;
    }
</style>
<style>
    .igao-text-input--size-s input[data-v-a862fb29] {
        min-height: 32px;
        max-height: 32px;
    }

    .igao-text-input--size-m input[data-v-a862fb29] {
        min-height: 40px;
        max-height: 40px;
    }

    .igao-text-input--size-l input[data-v-a862fb29] {
        min-height: 50px;
        max-height: 50px;
    }

    .igao-text-input-label[data-v-a862fb29] {
        font-size: 14px;
        line-height: 1.57;
        text-transform: uppercase;
        font-weight: bold;
        color: #666666;
        margin-bottom: 8px;
    }

    @media (max-width: 960px) {
        .igao-text-input-label[data-v-a862fb29] {
            font-size: 12px;
            line-height: 1.67;
        }
    }

    .igao-text-input[data-v-a862fb29] {
        display: flex;
        flex-direction: column;
        text-align: left;
        position: relative;
    }

    .igao-text-input.igao-text-input--has-errors input[data-v-a862fb29] {
        border-color: #c63120 !important;
    }

    .igao-text-input.igao-text-input--search input[data-v-a862fb29] {
        background-color: #fff;
        background-image: url(/icons/icon-search.svg);
        background-position: 16px center;
        background-repeat: no-repeat;
        background-size: 14px 14px;
        padding: 0 28px 0 40px;
    }

    .igao-text-input input[data-v-a862fb29] {
        background-clip: padding-box;
        border: 1px solid #d5d4d4;
        border-radius: 0;
        transition: border 0.2s linear, box-shadow 0.2s linear;
        font-size: 14px;
        line-height: 1.57;
        padding: 8px 15px;
        width: 100%;
        background-color: #fff;
        color: #4a4a4a;
    }

    .igao-text-input input[data-v-a862fb29]:focus, .igao-text-input input[data-v-a862fb29]:active:not([disabled]), .igao-text-input input.is-active[data-v-a862fb29] {
        border-color: #0567a7;
        box-shadow: 0 0 0 2px rgba(0, 123, 202, 0.12);
        outline: 0;
    }

    @media (max-width: 960px) {
        .igao-text-input input[data-v-a862fb29] {
            font-size: 16px;
            line-height: 1.5;
        }
    }

    .igao-text-input input[disabled][data-v-a862fb29] {
        background-color: #fafafa;
        color: #aaa;
    }

    .igao-text-input-wrapper[data-v-a862fb29] {
        position: relative;
    }

    .igao-text-input-clear[data-v-a862fb29] {
        background: none;
        -webkit-mask-image: url("/icons/remove.svg");
        mask-image: url("/icons/remove.svg");
        -webkit-mask-repeat: no-repeat;
        mask-repeat: no-repeat;
        -webkit-mask-size: contain;
        mask-size: contain;
        -webkit-mask-position: center;
        mask-position: center;
        background-color: #878787;
        width: 12px;
        height: 12px;
        border: none;
        margin: 0;
        padding: 0;
        opacity: 0.75;
        cursor: pointer;
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        -webkit-mask-size: 12px 12px;
        mask-size: 12px 12px;
        height: 20px;
        width: 20px;
    }

    .igao-text-input-clear[data-v-a862fb29]:hover {
        opacity: 1;
    }

    .igao-spinner[data-v-9d02a4c1] {
        width: 16px;
        height: 16px;
        border-width: 0.2em;
        display: inline-block;
        vertical-align: text-bottom;
        border: 2px solid currentColor;
        border-right-color: transparent;
        border-radius: 50%;
        animation: spinner-border-9d02a4c1 0.75s linear infinite;
    }

    @keyframes spinner-border-9d02a4c1 {
        to {
            transform: rotate(360deg);
        }
    }

    .company-label[data-v-9f120dbc] {
        display: inline-flex;
        justify-content: center;
        align-items: center;
        padding: 2px 4px;
        gap: 4px;
        background-color: #fff;
    }

    .save-coach--style-button[data-v-50024d92] {
        width: 100%;
    }

    .save-coach--style-button img[data-v-50024d92] {
        filter: invert(23%) sepia(97%) saturate(1500%) hue-rotate(183deg) brightness(100%) contrast(96%);
    }

    .save-coach--style-heart[data-v-50024d92] {
        cursor: pointer;
        background: none;
        -webkit-mask-repeat: no-repeat;
        mask-repeat: no-repeat;
        -webkit-mask-size: contain;
        mask-size: contain;
        -webkit-mask-position: center;
        mask-position: center;
        background-color: #aaaaaa;
        width: var(--save-coach-side-desktop, 30px);
        height: var(--save-coach-side-desktop, 30px);
        border: none;
    }

    @media (max-width: 960px) {
        .save-coach--style-heart[data-v-50024d92] {
            width: var(--save-coach-side, 20px);
            height: var(--save-coach-side, 20px);
        }
    }

    .save-coach--style-heart[data-v-50024d92]:hover {
        opacity: 0.7;
    }

    .save-coach--style-heart[data-v-50024d92]:focus {
        outline: none;
    }

    .save-coach--style-heart.save-coach--saved[data-v-50024d92] {
        -webkit-mask-image: url("/icons/heart-full.svg");
        mask-image: url("/icons/heart-full.svg");
    }

    .save-coach--style-heart.save-coach--not-saved[data-v-50024d92] {
        -webkit-mask-image: url("/icons/heart-empty.svg");
        mask-image: url("/icons/heart-empty.svg");
    }

    .star[data-v-d33d28eb] {
        line-height: 1;
    }

    .igao-highlightable-text-highlight {
        background-color: #d3eeff;
    }

    .coach-public-reviews-review-container[data-v-69a6089a] {
        padding: 16px 8px;
        border-bottom: solid 1px #eeeeee;
    }

    .coach-public-reviews-review-container--expanded[data-v-69a6089a] {
        padding: 8px 0;
    }

    .coach-public-reviews-review-header[data-v-69a6089a] {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .coach-public-reviews-review-header .coach-public-reviews-review-header-rating-and-date[data-v-69a6089a] {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .coach-public-reviews-review-header .coach-public-reviews-review-header-rating-and-date > span[data-v-69a6089a]:first-child {
        display: inline-flex;
        align-items: center;
        gap: 4px;
    }

    .coach-public-reviews-user-got-offer[data-v-69a6089a] {
        display: inline-flex;
        flex-direction: row;
        align-items: center;
        flex-wrap: wrap;
        padding: 4px 8px;
        margin-top: 8px;
        gap: 6px;
        background: #ffe8c0;
        font-size: 14px;
        font-weight: 600;
        line-height: 22px;
        transform: translateX(-8px);
    }

    .coach-public-reviews-review-text[data-v-69a6089a] {
        white-space: pre-wrap;
        margin-top: 16px;
    }

    .coach-public-reviews-replied-title[data-v-69a6089a] {
        font-size: 12px;
        line-height: 1.67;
        font-weight: 600;
        color: #333333;
        display: flex;
        align-items: center;
        margin-top: 16px;
    }

    .coach-public-reviews-replied-title > img[data-v-69a6089a] {
        margin-right: 8px;
    }

    .coach-public-reviews-replied-title .coach-public-reviews-replied-date[data-v-69a6089a] {
        color: #4a4a4a;
        font-weight: normal;
    }

    .coach-public-reviews-replied-title .coach-public-reviews-replied-delimiter[data-v-69a6089a] {
        color: #d5d4d4;
    }

    .coach-public-reviews-reply-text[data-v-69a6089a] {
        font-size: 14px;
        line-height: 1.57;
        color: #666666;
        margin-top: 8px;
        margin-left: 20px;
        white-space: pre-wrap;
    }

    .coach-public-reviews-reply-text a[data-v-69a6089a] {
        cursor: pointer;
    }

    .coach-public-review-more-reviews[data-v-69a6089a] {
        color: #878787;
        margin-top: 8px;
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        user-select: none;
    }

    .coach-public-review-more-reviews--expanded .coach-public-review-more-reviews-toggle-icon[data-v-69a6089a] {
        transform: rotate(180deg);
    }

    .coach-public-review-more-reviews-for[data-v-69a6089a] {
        display: inline-flex;
        align-items: center;
    }

    .coach-public-review-more-reviews-container[data-v-69a6089a] {
        padding-left: 16px;
    }

    @media (min-width: 960px) {
        .coach-public-review-more-reviews-container[data-v-69a6089a] {
            padding-left: 32px;
        }
    }

    .coach-public-review-more-reviews-container[data-v-69a6089a] .coach-public-reviews-review-container:last-child {
        border-bottom: none;
        padding-bottom: 0;
    }

    .coach-public-review-more-reviews-toggle-icon[data-v-69a6089a] {
        margin-left: 8px;
        filter: invert(52%) sepia(0%) saturate(0%) hue-rotate(166deg) brightness(100%) contrast(96%);
        transition: all 0.2s ease-in-out;
    }

    .coach-public-review-more-reviews-spinner[data-v-69a6089a] {
        margin-left: 8px;
    }

    .igao-btn.igao-btn--full-width[data-v-0dc3f13d] {
        width: 100%;
    }

    .igao-btn.igao-btn--l[data-v-0dc3f13d] {
        height: 50px;
        padding: 15px 32px;
    }

    .igao-btn.igao-btn--xl[data-v-0dc3f13d] {
        height: 54px;
        padding: 17px 32px;
    }

    .igao-btn.igao-btn--m[data-v-0dc3f13d] {
        height: 40px;
        padding: 10px 20px;
    }

    .igao-btn.igao-btn--s[data-v-0dc3f13d] {
        height: 32px;
        padding: 6px 16px;
    }

    .btn[data-v-55ea226e] {
        font-size: 12px;
        line-height: 1.67;
        border-radius: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #0567a7;
        position: relative;
        padding: 6px 15px;
        font-weight: bold;
        text-transform: uppercase;
        border: none;
        white-space: nowrap;
    }

    .btn.btn-primary[data-v-55ea226e] {
        color: #ffffff;
        background-color: #0567a7;
    }

    .btn.btn-primary[data-v-55ea226e]:hover {
        background-color: #045a8d;
        border-color: #045a8d;
    }

    .btn.btn-primary--green[data-v-55ea226e] {
        background-color: #15835c;
        border-color: #15835c;
    }

    .btn.btn-primary--green[data-v-55ea226e]:hover {
        background-color: #147642;
        border-color: #147642;
    }

    .btn.btn-disabled[data-v-55ea226e] {
        opacity: 0.65;
        pointer-events: none;
    }

    .btn.btn-link[data-v-55ea226e] {
        display: inline-block;
        height: auto;
        background: none;
        border: none;
        color: #0567a7;
        padding: 0;
        font-weight: 600;
    }

    .btn.btn-secondary[data-v-55ea226e] {
        background-color: #ffffff;
        border: solid 2px #eeeeee;
    }

    .btn.btn-secondary--gray[data-v-55ea226e] {
        border-color: #eeeeee;
        background-color: #eeeeee;
        color: #333333;
    }

    .btn.btn-secondary--gray[data-v-55ea226e]:hover {
        background-color: #d5d4d4;
        border-color: #d5d4d4;
    }

    .btn.btn-secondary--black[data-v-55ea226e] {
        border: solid 2px #eeeeee;
        background-color: #fff;
        color: #333;
    }

    .btn.btn-secondary[data-v-55ea226e]:hover {
        background-color: #ececec;
        border-color: #e6e6e6;
        color: #212529;
    }

    .btn.btn-danger[data-v-55ea226e] {
        color: #fff;
        background-color: #e8422f;
    }

    .btn.btn-danger[data-v-55ea226e]:hover {
        color: #fff;
    }

    .btn.btn-no-transform[data-v-55ea226e] {
        text-transform: none;
    }

    .account-buttons[data-v-f9a5b2be] .btn.btn {
        font-size: 16px;
        line-height: 1.5;
        border: none;
        font-weight: bold;
        padding: 3px 12px;
        height: auto;
    }

    .account-buttons[data-v-f9a5b2be] .btn.link-style {
        border: 0;
        background: none;
        color: #242424;
        padding-left: 0;
        padding-right: 0;
    }

    .account-buttons[data-v-f9a5b2be] .btn.link-style:hover {
        opacity: 0.7;
        background: none;
    }

    .account-buttons[data-v-f9a5b2be] .btn.link-style.become-coach {
        color: #666666;
    }

    .coach-details-cta-sessions-left[data-v-********] {
        font-size: 14px;
        line-height: 1.57;
        display: inline-flex;
        justify-content: center;
        font-style: italic;
        color: #878787;
        margin-top: 6px;
        margin-bottom: 6px;
    }

    @media (min-width: 960px) {
        .coach-details-cta-sessions-left[data-v-********] {
            position: absolute;
            top: 42px;
            left: 50%;
            transform: translateX(-50%);
            white-space: nowrap;
        }
    }

    .coach-details-cta-schedule-session-container[data-v-********] {
        position: relative;
        display: inline-flex;
        flex-direction: column;
        width: 100%;
        margin-top: 12px;
    }

    @media (min-width: 960px) {
        .coach-details-cta-schedule-session-container[data-v-********] {
            width: auto;
            margin-top: 0;
            margin-left: 16px;
        }
    }

    .coach-details-buy-sessions-btn[data-v-********] {
        width: 100%;
    }

    @media (min-width: 960px) {
        .coach-details-buy-sessions-btn[data-v-********] {
            width: auto;
        }
    }

    @media (max-width: 960px) {
        .margin-top-12[data-v-********] {
            margin-top: 12px;
        }
    }

    .coach-details-card-save-coach[data-v-f416a284] {
        margin-right: 16px;
    }

    @media (max-width: 960px) {
        .coach-details-card-save-coach[data-v-f416a284] {
            position: absolute;
            right: 20px;
            top: 26px;
            margin-right: 0;
        }
    }

    .header-account-container[data-v-5d2dab64] {
        display: flex;
        align-items: center;
    }

    .account-buttons[data-v-5d2dab64] {
        display: none;
    }

    @media (min-width: 1200px) {
        .account-buttons[data-v-5d2dab64] {
            display: block;
        }
    }

    .account-buttons[data-v-5d2dab64] > a {
        margin-right: 32px;
    }

    .account-buttons[data-v-5d2dab64] > a:last-of-type {
        margin-right: 18px;
    }

    .mobile-menu-icon[data-v-5d2dab64] {
        width: 44px;
        height: 44px;
        align-items: center;
        justify-content: center;
        border: none;
        background: no-repeat;
        display: flex;
    }

    @media (min-width: 1200px) {
        .mobile-menu-icon[data-v-5d2dab64] {
            display: none;
        }
    }

    .mobile-menu-icon.cart-icon[data-v-5d2dab64] {
        display: flex;
    }

    .buy-sessions-modal-heading[data-v-d8bfb7f9] {
        font-size: 24px;
        line-height: 1.42;
        max-width: 620px;
        margin: 26px auto 32px;
        text-align: center;
    }

    @media (max-width: 960px) {
        .buy-sessions-modal-heading[data-v-d8bfb7f9] {
            font-size: 20px;
        }
    }

    .buy-sessions-modal-container[data-v-d8bfb7f9] {
        padding-bottom: 48px;
        font-size: 18px;
    }

    @media (max-width: 960px) {
        .buy-sessions-modal-container[data-v-d8bfb7f9] {
            padding-bottom: 32px;
        }
    }

    .buy-sessions-modal-container[data-v-d8bfb7f9] .btn.purchase-sessions__buttons__add-to-cart {
        background: #fbfbfb;
    }

    .buy-sessions-modal-container[data-v-d8bfb7f9] .purchase-sessions__choices-bar__item {
        padding: 26px 0;
    }

    .buy-sessions-modal-container[data-v-d8bfb7f9] .session-picker-container {
        margin-top: 0;
    }

    @media (max-width: 960px) {
        .buy-sessions-modal-container[data-v-d8bfb7f9] .session-picker-container h4 {
            font-size: 18px;
        }
    }

    @media (max-width: 960px) {
        .buy-sessions-modal-container[data-v-d8bfb7f9] .add-to-cart-btn {
            width: 100%;
        }
    }

    .coach-details-cta[data-v-20fd5c0f] {
        margin-top: 24px;
    }

    @media (min-width: 960px) {
        .coach-details-cta[data-v-20fd5c0f] {
            margin-top: 32px;
        }
    }

    .coach-details-cta[data-v-20fd5c0f] h2 {
        font-size: 24px;
        line-height: 1.42;
    }

    .igao-btn.igao-btn--full-width[data-v-0dc3f13d] {
        width: 100%;
    }

    .igao-btn.igao-btn--l[data-v-0dc3f13d] {
        height: 50px;
        padding: 15px 32px;
    }

    .igao-btn.igao-btn--xl[data-v-0dc3f13d] {
        height: 54px;
        padding: 17px 32px;
    }

    .igao-btn.igao-btn--m[data-v-0dc3f13d] {
        height: 40px;
        padding: 10px 20px;
    }

    .igao-btn.igao-btn--s[data-v-0dc3f13d] {
        height: 32px;
        padding: 6px 16px;
    }

    .coach-avatar-wrapper[data-v-9d2f0bee] {
        position: relative;
    }

    .coach-avatar[data-v-9d2f0bee] {
        filter: grayscale(1);
        border-radius: 50%;
    }

    .ui-info-message[data-v-aaa8948b] {
        font-size: 20px;
        line-height: 1.5;
        display: flex;
        font-weight: 600;
        letter-spacing: -0.1px;
        color: #333;
    }

    .ui-info-message--centered[data-v-aaa8948b] {
        justify-content: center;
    }

    .ui-info-message-icon-container[data-v-aaa8948b] {
        margin-right: 12px;
    }

    .success-checkmark-icon[data-v-aaa8948b] {
        margin-top: 5px;
    }

    .success-checkmark-icon[data-v-aaa8948b] g > polygon {
        fill: #2cae81;
    }

    .alert-checkmark-icon[data-v-aaa8948b] {
        margin-top: 4px;
    }

    .alert-checkmark-icon[data-v-aaa8948b] g > path {
        fill: #e8422f;
    }

    .coach-buying-reassurance[data-v-99fd961d] {
        color: #4a4a4a;
        gap: 8px;
        display: inline-flex;
        flex-direction: column;
    }

    .coach-buying-reassurance > div[data-v-99fd961d] {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 8px;
    }

    .igao-spinner[data-v-9d02a4c1] {
        width: 16px;
        height: 16px;
        border-width: 0.2em;
        display: inline-block;
        vertical-align: text-bottom;
        border: 2px solid currentColor;
        border-right-color: transparent;
        border-radius: 50%;
        animation: spinner-border-9d02a4c1 0.75s linear infinite;
    }

    @keyframes spinner-border-9d02a4c1 {
        to {
            transform: rotate(360deg);
        }
    }
</style>
<style>
    .btn[data-v-55ea226e] {
        font-size: 12px;
        line-height: 1.67;
        border-radius: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #0567a7;
        position: relative;
        padding: 6px 15px;
        font-weight: bold;
        text-transform: uppercase;
        border: none;
        white-space: nowrap;
    }

    .btn.btn-primary[data-v-55ea226e] {
        color: #ffffff;
        background-color: #0567a7;
    }

    .btn.btn-primary[data-v-55ea226e]:hover {
        background-color: #045a8d;
        border-color: #045a8d;
    }

    .btn.btn-primary--green[data-v-55ea226e] {
        background-color: #15835c;
        border-color: #15835c;
    }

    .btn.btn-primary--green[data-v-55ea226e]:hover {
        background-color: #147642;
        border-color: #147642;
    }

    .btn.btn-disabled[data-v-55ea226e] {
        opacity: 0.65;
        pointer-events: none;
    }

    .btn.btn-link[data-v-55ea226e] {
        display: inline-block;
        height: auto;
        background: none;
        border: none;
        color: #0567a7;
        padding: 0;
        font-weight: 600;
    }

    .btn.btn-secondary[data-v-55ea226e] {
        background-color: #ffffff;
        border: solid 2px #eeeeee;
    }

    .btn.btn-secondary--gray[data-v-55ea226e] {
        border-color: #eeeeee;
        background-color: #eeeeee;
        color: #333333;
    }

    .btn.btn-secondary--gray[data-v-55ea226e]:hover {
        background-color: #d5d4d4;
        border-color: #d5d4d4;
    }

    .btn.btn-secondary--black[data-v-55ea226e] {
        border: solid 2px #eeeeee;
        background-color: #fff;
        color: #333;
    }

    .btn.btn-secondary[data-v-55ea226e]:hover {
        background-color: #ececec;
        border-color: #e6e6e6;
        color: #212529;
    }

    .btn.btn-danger[data-v-55ea226e] {
        color: #fff;
        background-color: #e8422f;
    }

    .btn.btn-danger[data-v-55ea226e]:hover {
        color: #fff;
    }

    .btn.btn-no-transform[data-v-55ea226e] {
        text-transform: none;
    }

    .spinner-container[data-v-15ddf44a] {
        text-align: center;
        position: relative;
        min-height: 132px;
        height: 100%;
        padding: 16px 0;
    }

    .spinner-container .loading-coaches-spinner[data-v-15ddf44a] {
        position: absolute;
        top: calc(50% - 50px);
        left: calc(50% - 50px);
        color: #0567a7;
        width: var(--spinner-size, 100px);
        height: var(--spinner-size, 100px);
        border-width: 4px;
    }

    .content-wrapper[data-v-e2113d11] {
        font-size: 12px;
        line-height: 1.67;
        z-index: 500;
        display: none;
        font-weight: 600;
        font-stretch: normal;
        font-style: normal;
        letter-spacing: normal;
        color: #333;
        box-shadow: 0 2px 4px 0 rgba(51, 51, 51, 0.12);
        padding: 11px 12px;
        max-width: 282px;
        white-space: normal;
        background-color: #fff;
        border: 1px solid #eee;
    }

    .content-wrapper .popover-arrow[data-v-e2113d11], .content-wrapper .popover-arrow[data-v-e2113d11]::before {
        position: absolute;
        width: 8px;
        height: 8px;
        background: inherit;
    }

    .content-wrapper .popover-arrow[data-v-e2113d11] {
        visibility: hidden;
    }

    .content-wrapper .popover-arrow[data-v-e2113d11]::before {
        visibility: visible;
        content: "";
        transform: rotate(45deg);
    }

    .content-wrapper[data-show][data-v-e2113d11] {
        display: block;
    }

    .content-wrapper[data-popper-placement^=top] > .popover-arrow[data-v-e2113d11] {
        bottom: -4px;
    }

    .content-wrapper[data-popper-placement^=top] > .popover-arrow[data-v-e2113d11]::before {
        border-right: 1px solid #eee;
        border-bottom: 1px solid #eee;
    }

    .content-wrapper[data-popper-placement^=left] > .popover-arrow[data-v-e2113d11]::before {
        border-right: 1px solid #eee;
        border-top: 1px solid #eee;
    }

    .content-wrapper[data-popper-placement^=right] > .popover-arrow[data-v-e2113d11]::before {
        border-left: 1px solid #eee;
        border-bottom: 1px solid #eee;
    }

    .content-wrapper[data-popper-placement^=bottom] > .popover-arrow[data-v-e2113d11] {
        top: -4px;
    }

    .content-wrapper[data-popper-placement^=bottom] > .popover-arrow[data-v-e2113d11]::before {
        border-left: 1px solid #eee;
        border-top: 1px solid #eee;
    }

    .content-wrapper[data-popper-placement^=left] > .popover-arrow[data-v-e2113d11] {
        right: -4px;
    }

    .content-wrapper[data-popper-placement^=right] > .popover-arrow[data-v-e2113d11] {
        left: -4px;
    }

    .tooltip-wrapper-inline[data-v-b905ec3a] {
        display: inline;
    }

    .tooltip-wrapper-flex[data-v-b905ec3a] {
        display: flex;
    }

    .save-coach--style-button[data-v-50024d92] {
        width: 100%;
    }

    .save-coach--style-button img[data-v-50024d92] {
        filter: invert(23%) sepia(97%) saturate(1500%) hue-rotate(183deg) brightness(100%) contrast(96%);
    }

    .save-coach--style-heart[data-v-50024d92] {
        cursor: pointer;
        background: none;
        -webkit-mask-repeat: no-repeat;
        mask-repeat: no-repeat;
        -webkit-mask-size: contain;
        mask-size: contain;
        -webkit-mask-position: center;
        mask-position: center;
        background-color: #aaaaaa;
        width: var(--save-coach-side-desktop, 30px);
        height: var(--save-coach-side-desktop, 30px);
        border: none;
    }

    @media (max-width: 960px) {
        .save-coach--style-heart[data-v-50024d92] {
            width: var(--save-coach-side, 20px);
            height: var(--save-coach-side, 20px);
        }
    }

    .save-coach--style-heart[data-v-50024d92]:hover {
        opacity: 0.7;
    }

    .save-coach--style-heart[data-v-50024d92]:focus {
        outline: none;
    }

    .save-coach--style-heart.save-coach--saved[data-v-50024d92] {
        -webkit-mask-image: url("/icons/heart-full.svg");
        mask-image: url("/icons/heart-full.svg");
    }

    .save-coach--style-heart.save-coach--not-saved[data-v-50024d92] {
        -webkit-mask-image: url("/icons/heart-empty.svg");
        mask-image: url("/icons/heart-empty.svg");
    }

    .modal-popup[data-v-cd0da137] {
        position: fixed;
        z-index: 600;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0, 0, 0, 0.48);
    }

    .modal-popup--without-header .modal-popup-body[data-v-cd0da137] {
        padding-top: 24px;
    }

    .modal-popup--animated-hide[data-v-cd0da137] {
        transition: background-color 0.4s linear;
        background-color: rgba(36, 36, 36, 0);
    }

    .modal-popup--animated-hide .modal-popup-content[data-v-cd0da137] {
        animation-name: animatedhide-cd0da137;
        animation-duration: 0.4s;
    }

    .modal-popup--animated-show[data-v-cd0da137] {
        transition: all 0.4s linear;
    }

    .modal-popup--animated-show .modal-popup-content[data-v-cd0da137] {
        animation-name: animatedshow-cd0da137;
        animation-duration: 0.4s;
    }

    .modal-popup .modal-popup-footer[data-v-cd0da137] {
        gap: 12px;
    }

    .modal-popup .modal-popup-footer[data-v-cd0da137] .btn {
        height: 40px;
    }

    .modal-popup .modal-popup-footer[data-v-cd0da137] .btn.btn-secondary {
        color: #333;
    }

    .modal-popup-content[data-v-cd0da137] {
        font-size: 14px;
        line-height: 1.57;
        position: relative;
        margin: auto;
        top: 50%;
        transform: translateY(-50%);
        background-color: #fff;
        max-width: calc(100vw - 32px);
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        letter-spacing: normal;
        color: #4a4a4a;
    }

    @media (min-width: 960px) {
        .modal-popup-content[data-v-cd0da137] {
            max-width: 400px;
        }
    }

    @keyframes animatedshow-cd0da137 {
        from {
            top: 40%;
            opacity: 0;
        }

        to {
            top: 50%;
            opacity: 1;
        }
    }

    @keyframes animatedhide-cd0da137 {
        from {
            top: 50%;
            opacity: 1;
        }

        to {
            top: 40%;
            opacity: 0;
        }
    }

    .modal-popup-close[data-v-cd0da137] {
        display: flex;
        align-items: center;
    }

    .modal-popup-close--alternative[data-v-cd0da137] {
        position: absolute;
        right: 24px;
        top: 18px;
    }

    @media (max-width: 960px) {
        .modal-popup-close--alternative[data-v-cd0da137] {
            right: 16px;
        }
    }

    .modal-popup-close[data-v-cd0da137]:hover, .modal-popup-close[data-v-cd0da137]:focus {
        cursor: pointer;
        opacity: 0.8;
    }

    .modal-popup-header[data-v-cd0da137] {
        padding: 16px 24px;
        display: flex;
    }

    @media (max-width: 960px) {
        .modal-popup-header[data-v-cd0da137] {
            padding: 16px;
        }
    }

    .modal-popup-header--primary[data-v-cd0da137] {
        font-size: 20px;
        line-height: 1.5;
        padding-bottom: 0;
        letter-spacing: -0.1px;
    }

    .modal-popup-header--secondary[data-v-cd0da137] {
        font-size: 16px;
        line-height: 1.5;
        letter-spacing: -0.09px;
        border-bottom: 1px solid #eee;
    }

    .modal-popup-header-title[data-v-cd0da137] {
        width: 100%;
        font-weight: 600;
        color: #333;
        padding-right: 22px;
    }

    .modal-popup-body[data-v-cd0da137] {
        padding: 16px 24px 0;
    }

    @media (max-width: 960px) {
        .modal-popup-body[data-v-cd0da137] {
            padding: 16px 16px 0;
        }
    }

    .modal-popup-footer[data-v-cd0da137] {
        display: flex;
        justify-content: flex-end;
        padding: 24px;
    }

    @media (max-width: 960px) {
        .modal-popup-footer[data-v-cd0da137] {
            padding: 16px;
        }
    }

    .igao-box[data-v-253e5cdb] {
        border: solid 2px #eeeeee;
        padding: 32px 16px;
        width: 100%;
    }

    @media (min-width: 960px) {
        .igao-box[data-v-253e5cdb] {
            padding: 36px 80px 48px;
        }
    }
</style>
<link rel="stylesheet" href="{{asset('css/app/index.CoUXExAP.css')}}">

<style>
    .cta-buttons[data-v-b7b4b200] {
        margin-top: 26px;
    }

    @media (min-width: 960px) {
        .cta-buttons[data-v-b7b4b200] {
            margin-top: 40px;
        }
    }

    @media (max-width: 960px) {
        .cta-buttons[data-v-b7b4b200] .btn {
            width: 100%;
        }
    }

    .cta-buttons[data-v-b7b4b200] .btn + .btn {
        margin-top: 12px;
    }

    @media (min-width: 960px) {
        .cta-buttons[data-v-b7b4b200] .btn + .btn {
            margin-top: 0;
            margin-left: 24px;
        }
    }
</style>
