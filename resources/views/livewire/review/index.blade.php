<style>
    .section-wrapper[data-v-c99ebf58] {
        max-width: 1080px;
        margin: auto;
        padding: 0 16px;
        text-align: center;
        margin-top: 48px;
    }

    @media (min-width: 960px) {
        .section-wrapper[data-v-c99ebf58] {
            margin-top: 80px;
        }
    }

    .social-proof-wrapper[data-v-14482d4e] {
        font-size: 14px;
        line-height: 1.57;
        font-weight: 600;
        text-align: center;
        max-width: none;
        width: 100%;
        display: grid;
        grid-template-rows: auto auto;
        gap: 16px;
    }

    .social-proof-wrapper span[data-v-14482d4e] {
        text-decoration: underline;
    }

    .social-proof-wrapper img[data-v-14482d4e] {
        margin: auto;
    }

    .social-proof-separator[data-v-14482d4e] {
        margin: 0 4px;
        text-decoration: none !important;
    }

    @media (max-width: 960px) {
        .social-proof-separator[data-v-14482d4e] {
            width: 0;
            overflow: hidden;
        }
    }

    .social-proof[data-v-14482d4e],
    .social-proof-line[data-v-14482d4e] {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
    }

    .social-proof-tooltip[data-v-14482d4e] {
        display: flex;
        margin-left: 8px;
    }

    @keyframes fadeInDown-edc9e39b {
        0% {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes fadeOutDown-edc9e39b {
        0% {
            opacity: 1;
            transform: translateY(0);
        }
        to {
            opacity: 0;
            transform: translateY(20px);
        }
    }

    .hero-section-carousel-container[data-v-edc9e39b] {
        position: relative;
        margin-bottom: 16px;
    }

    .hero-section-carousel-item[data-v-edc9e39b] {
        color: #0567a7;
        display: inline-block;
        left: 0;
        margin-left: auto;
        margin-right: auto;
        opacity: 0;
        position: absolute;
        right: 0;
    }

    .hero-section-carousel-item--placeholder[data-v-edc9e39b] {
        position: static;
    }

    .hero-section-carousel-item--fade-in[data-v-edc9e39b] {
        animation: fadeInDown-edc9e39b 0.2s ease-in;
        opacity: 1;
        will-change: opacity;
    }

    .hero-section-carousel-item--fade-out[data-v-edc9e39b] {
        animation: fadeOutDown-edc9e39b 0.2s ease-in;
        opacity: 0;
        will-change: opacity;
    }

    .header[data-v-edc9e39b] {
        position: relative;
        color: #333333;
        margin-bottom: 16px;
        letter-spacing: -0.4px;
        white-space: break-spaces;
    }

    @media (min-width: 960px) {
        .header[data-v-edc9e39b] {
            margin: 0 auto 16px;
        }
    }

    .new-badge--desktop[data-v-edc9e39b] {
        transform: translateY(-50%);
        display: none;
    }

    @media (min-width: 960px) {
        .new-badge--desktop[data-v-edc9e39b] {
            display: inline-block;
        }
    }

    .new-badge--on-first-line[data-v-edc9e39b] {
        transform: none;
        margin-left: 6px;
    }

    .new-badge--mobile[data-v-edc9e39b] {
        position: static;
        width: 45px;
        margin: auto;
        display: block;
    }

    @media (min-width: 960px) {
        .new-badge--mobile[data-v-edc9e39b] {
            display: none;
        }
    }

    .subheader[data-v-edc9e39b] {
        color: #4a4a4a;
        max-width: 880px;
        font-size: 18px;
        line-height: 1.56;
        letter-spacing: -0.1px;
        margin: auto;
        margin-bottom: 32px;
        white-space: break-spaces;
        position: relative;
    }

    @media (min-width: 960px) {
        .subheader[data-v-edc9e39b] {
            margin-bottom: 40px;
        }
    }

    .account-buttons[data-astro-cid-hmtt24ci] {
        display: flex;
        flex-direction: column;
        width: 100%
    }

    .account-buttons[data-astro-cid-hmtt24ci] a.btn:not(:last-child) {
        margin-bottom: 12px
    }

    .mobile-navigation-container[data-astro-cid-hmtt24ci] {
        display: none;
        position: fixed;
        top: 0;
        width: 100vw;
        left: 0;
        flex-direction: column;
        align-items: start;
        background: #ffffff;
        z-index: 400;
        padding: 86px 24px 156px;
        height: 100vh;
        overflow: auto
    }

    .mobile-navigation-container[data-astro-cid-hmtt24ci].open {
        display: flex
    }

    nav[data-astro-cid-2xhbaixd] {
        flex-grow: 1;
        display: flex
    }

    .fake-header[data-astro-cid-xbstl6g3] {
        display: flex;
        min-height: 70px;
        width: 100%
    }

    .mobile-sub-navigation[data-astro-cid-xbstl6g3] {
        height: 58px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fff;
        border-bottom: 2px solid #eeeeee;
        color: #4a4a4a
    }

    @media (min-width: 1200px) {
        .mobile-sub-navigation[data-astro-cid-xbstl6g3] {
            display: none
        }
    }

    .mobile-sub-navigation[data-astro-cid-xbstl6g3] a[data-astro-cid-xbstl6g3].navigation-link {
        position: relative;
        box-sizing: border-box;
        text-decoration: none;
        font-weight: 600;
        cursor: pointer;
        color: #000;
        white-space: nowrap;
        font-size: 14px
    }

    .mobile-sub-navigation[data-astro-cid-xbstl6g3] a[data-astro-cid-xbstl6g3].navigation-link.active {
        color: #0567a7
    }

    .mobile-sub-navigation[data-astro-cid-xbstl6g3] a[data-astro-cid-xbstl6g3].navigation-link:not(:last-child) {
        margin-right: 16px
    }

    header[data-astro-cid-xbstl6g3] {
        position: fixed;
        width: 100%;
        box-sizing: border-box;
        height: 70px;
        border-bottom: 6px solid #eeeeee;
        background-color: #ffffff;
        padding: 10px 24px;
        display: flex;
        z-index: 500
    }

    @media (min-width: 1200px) {
        header[data-astro-cid-xbstl6g3] {
            padding: 10px 64px
        }
    }

    header[data-astro-cid-xbstl6g3] .header-container[data-astro-cid-xbstl6g3] {
        display: flex;
        width: 100%;
        align-items: center;
        margin: auto
    }

    header[data-astro-cid-xbstl6g3] .logo[data-astro-cid-xbstl6g3] {
        display: flex
    }

    header[data-astro-cid-xbstl6g3] .logo[data-astro-cid-xbstl6g3] > a[data-astro-cid-xbstl6g3] {
        display: flex
    }

    .content-wrapper[data-v-e2113d11] {
        font-size: 12px;
        line-height: 1.67;
        z-index: 500;
        display: none;
        font-weight: 600;
        font-stretch: normal;
        font-style: normal;
        letter-spacing: normal;
        color: #333;
        box-shadow: 0 2px 4px 0 rgba(51, 51, 51, 0.12);
        padding: 11px 12px;
        max-width: 282px;
        white-space: normal;
        background-color: #fff;
        border: 1px solid #eee;
    }

    .content-wrapper .popover-arrow[data-v-e2113d11],
    .content-wrapper .popover-arrow[data-v-e2113d11]::before {
        position: absolute;
        width: 8px;
        height: 8px;
        background: inherit;
    }

    .content-wrapper .popover-arrow[data-v-e2113d11] {
        visibility: hidden;
    }

    .content-wrapper .popover-arrow[data-v-e2113d11]::before {
        visibility: visible;
        content: "";
        transform: rotate(45deg);
    }

    .content-wrapper[data-show][data-v-e2113d11] {
        display: block;
    }

    .content-wrapper[data-popper-placement^=top] > .popover-arrow[data-v-e2113d11] {
        bottom: -4px;
    }

    .content-wrapper[data-popper-placement^=top] > .popover-arrow[data-v-e2113d11]::before {
        border-right: 1px solid #eee;
        border-bottom: 1px solid #eee;
    }

    .content-wrapper[data-popper-placement^=left] > .popover-arrow[data-v-e2113d11]::before {
        border-right: 1px solid #eee;
        border-top: 1px solid #eee;
    }

    .content-wrapper[data-popper-placement^=right] > .popover-arrow[data-v-e2113d11]::before {
        border-left: 1px solid #eee;
        border-bottom: 1px solid #eee;
    }

    .content-wrapper[data-popper-placement^=bottom] > .popover-arrow[data-v-e2113d11] {
        top: -4px;
    }

    .content-wrapper[data-popper-placement^=bottom] > .popover-arrow[data-v-e2113d11]::before {
        border-left: 1px solid #eee;
        border-top: 1px solid #eee;
    }

    .content-wrapper[data-popper-placement^=left] > .popover-arrow[data-v-e2113d11] {
        right: -4px;
    }

    .content-wrapper[data-popper-placement^=right] > .popover-arrow[data-v-e2113d11] {
        left: -4px;
    }

    .tooltip-wrapper-inline[data-v-b905ec3a] {
        display: inline;
    }

    .tooltip-wrapper-flex[data-v-b905ec3a] {
        display: flex;
    }</style>
<style>
    .igao-btn.igao-btn--full-width[data-v-0dc3f13d] {
        width: 100%;
    }

    .igao-btn.igao-btn--l[data-v-0dc3f13d] {
        height: 50px;
        padding: 15px 32px;
    }

    .igao-btn.igao-btn--xl[data-v-0dc3f13d] {
        height: 54px;
        padding: 17px 32px;
    }

    .igao-btn.igao-btn--m[data-v-0dc3f13d] {
        height: 40px;
        padding: 10px 20px;
    }

    .igao-btn.igao-btn--s[data-v-0dc3f13d] {
        height: 32px;
        padding: 6px 16px;
    }

    .igao-text-input--size-s input[data-v-a862fb29] {
        min-height: 32px;
        max-height: 32px;
    }

    .igao-text-input--size-m input[data-v-a862fb29] {
        min-height: 40px;
        max-height: 40px;
    }

    .igao-text-input--size-l input[data-v-a862fb29] {
        min-height: 50px;
        max-height: 50px;
    }

    .igao-text-input-label[data-v-a862fb29] {
        font-size: 14px;
        line-height: 1.57;
        text-transform: uppercase;
        font-weight: bold;
        color: #666666;
        margin-bottom: 8px;
    }

    @media (max-width: 960px) {
        .igao-text-input-label[data-v-a862fb29] {
            font-size: 12px;
            line-height: 1.67;
        }
    }

    .igao-text-input[data-v-a862fb29] {
        display: flex;
        flex-direction: column;
        text-align: left;
        position: relative;
    }

    .igao-text-input.igao-text-input--has-errors input[data-v-a862fb29] {
        border-color: #c63120 !important;
    }

    .igao-text-input.igao-text-input--search input[data-v-a862fb29] {
        background-color: #fff;
        background-image: url(/icons/icon-search.svg);
        background-position: 16px center;
        background-repeat: no-repeat;
        background-size: 14px 14px;
        padding: 0 28px 0 40px;
    }

    .igao-text-input input[data-v-a862fb29] {
        background-clip: padding-box;
        border: 1px solid #d5d4d4;
        border-radius: 0;
        transition: border 0.2s linear, box-shadow 0.2s linear;
        font-size: 14px;
        line-height: 1.57;
        padding: 8px 15px;
        width: 100%;
        background-color: #fff;
        color: #4a4a4a;
    }

    .igao-text-input input[data-v-a862fb29]:focus, .igao-text-input input[data-v-a862fb29]:active:not([disabled]), .igao-text-input input.is-active[data-v-a862fb29] {
        border-color: #0567a7;
        box-shadow: 0 0 0 2px rgba(0, 123, 202, 0.12);
        outline: 0;
    }

    @media (max-width: 960px) {
        .igao-text-input input[data-v-a862fb29] {
            font-size: 16px;
            line-height: 1.5;
        }
    }

    .igao-text-input input[disabled][data-v-a862fb29] {
        background-color: #fafafa;
        color: #aaa;
    }

    .igao-text-input-wrapper[data-v-a862fb29] {
        position: relative;
    }

    .igao-text-input-clear[data-v-a862fb29] {
        background: none;
        -webkit-mask-image: url("/icons/remove.svg");
        mask-image: url("/icons/remove.svg");
        -webkit-mask-repeat: no-repeat;
        mask-repeat: no-repeat;
        -webkit-mask-size: contain;
        mask-size: contain;
        -webkit-mask-position: center;
        mask-position: center;
        background-color: #878787;
        width: 12px;
        height: 12px;
        border: none;
        margin: 0;
        padding: 0;
        opacity: 0.75;
        cursor: pointer;
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        -webkit-mask-size: 12px 12px;
        mask-size: 12px 12px;
        height: 20px;
        width: 20px;
    }

    .igao-text-input-clear[data-v-a862fb29]:hover {
        opacity: 1;
    }

    .review-card-header[data-v-e194142e] {
        display: flex;
        align-items: center;
    }

    .size-m .headshot-image[data-v-e194142e] {
        width: 64px;
        height: 64px;
    }

    .size-m .company-logo[data-v-e194142e] {
        margin-left: 20px;
    }

    .size-s .headshot-image[data-v-e194142e] {
        width: 48px;
        height: 48px;
    }

    .size-s .company-logo[data-v-e194142e] {
        margin-left: 12px;
    }

    .headshot-image[data-v-e194142e] {
        border-radius: 50%;
    }

    .company-title[data-v-e194142e] {
        font-size: 20px;
        line-height: 1.5;
        font-weight: bold;
        letter-spacing: -0.08px;
        color: #333;
    }

    .link-wrapper[data-v-c8ad65ec] {
        text-decoration: none;
    }

    .link-wrapper .success-story-list-card[data-v-c8ad65ec] {
        border: 1px solid transparent;
    }

    .link-wrapper:hover .success-story-list-card[data-v-c8ad65ec] {
        border: 1px solid #0567a7;
    }

    .success-story-list-card[data-v-c8ad65ec] {
        display: flex;
        flex-direction: column;
        padding: 24px;
        box-shadow: 0 1px 2px 0 rgba(51, 51, 51, 0.12);
        background-color: #fff;
        text-align: left;
        height: 100%;
    }

    .success-story-list-card .quote[data-v-c8ad65ec] {
        font-size: 18px;
        line-height: 1.56;
        font-weight: 600;
        letter-spacing: -0.1px;
        color: #333;
        margin-top: 16px;
    }

    .success-story-list-card .position[data-v-c8ad65ec] {
        font-size: 16px;
        line-height: 1.5;
        font-style: italic;
        letter-spacing: -0.09px;
        color: #4a4a4a;
        flex-grow: 1;
        height: 100%;
        margin-top: 12px;
    }

    .success-story-list-card .position .name[data-v-c8ad65ec] {
        font-weight: 600;
        font-style: normal;
        color: #1a1a1a;
    }

    .success-story-list-card .position .dash[data-v-c8ad65ec] {
        font-weight: 600;
        font-style: normal;
        color: #a6a6a6;
    }

    .success-story-list-card .position .company-name[data-v-c8ad65ec] {
        font-weight: 600;
    }

    .success-story-list-card .actions-line[data-v-c8ad65ec] {
        text-align: right;
        margin-top: 16px;
    }

    .success-story-list-card .actions-line .read-story-link[data-v-c8ad65ec] {
        font-size: 16px;
        line-height: 1.5;
        font-weight: 600;
        letter-spacing: -0.09px;
        color: #0567a7;
    }

    .availability-filter-dropdown-container[data-v-939ee512] {
        font-size: 14px;
        text-align: left;
    }

    .availability-filter-dropdown-picker[data-v-939ee512] {
        position: absolute;
        width: 100%;
        top: calc(100% + 2px);
        right: 0;
        background: #fff;
        padding: 16px;
        border: 1px solid #eeeeee;
    }

    @media (min-width: 960px) {
        .availability-filter-dropdown-picker[data-v-939ee512] {
            width: 376px;
        }
    }

    .availability-filter-dropdown[data-v-939ee512] {
        position: relative;
        background: #fff;
        border: 1px solid #d5d4d4;
        padding: 6px 16px;
        min-height: 40px;
        display: flex;
        align-items: center;
    }

    .availability-filter-dropdown--open[data-v-939ee512] {
        z-index: 7777;
    }

    .availability-filter-dropdown--open[data-v-939ee512]::after {
        rotate: 180deg;
    }

    .availability-filter-dropdown[data-v-939ee512]:focus {
        outline: 0;
        border: 1px solid #0567a7;
    }

    .availability-filter-dropdown[data-v-939ee512]::after {
        background: url("/icons/icon-chevron-down.svg") no-repeat;
        position: absolute;
        content: "";
        height: 7px;
        width: 10px;
        top: 50%;
        right: 16px;
        margin-top: -2.5px;
        pointer-events: none;
        border: none;
    }

    .availability-filter-dropdown-time-period[data-v-939ee512] {
        cursor: pointer;
        background: #eeeeee;
        color: #333333;
        display: flex;
        gap: 8px;
        align-items: center;
        justify-content: center;
    }

    .availability-filter-dropdown-time-period img[data-v-939ee512] {
        filter: invert(13%) sepia(9%) saturate(5%) hue-rotate(7deg) brightness(96%) contrast(81%);
    }

    .availability-filter-dropdown-time-period--selected[data-v-939ee512] {
        background: #0567a7;
        color: #fff;
    }

    .availability-filter-dropdown-time-period--selected img[data-v-939ee512] {
        filter: invert(100%) sepia(0%) saturate(0%) hue-rotate(93deg) brightness(103%) contrast(103%);
    }

    .availability-filter-dropdown-label[data-v-939ee512] {
        font-size: 12px;
        line-height: 1.57;
        text-transform: uppercase;
        font-weight: bold;
        color: #666666;
        margin-bottom: 8px;
    }

    @media (min-width: 960px) {
        .availability-filter-dropdown-label[data-v-939ee512] {
            font-size: 14px;
        }
    }

    .availability-filter-dropdown-value[data-v-939ee512] {
        font-size: 16px;
        color: #4a4a4a;
        font-weight: 600;
        -webkit-user-select: none;
        -moz-user-select: none;
        user-select: none;
        padding-right: 16px;
    }

    @media (min-width: 960px) {
        .availability-filter-dropdown-value[data-v-939ee512] {
            font-size: 14px;
        }
    }

    .availability-filter-dropdown-picker-label[data-v-939ee512] {
        text-transform: uppercase;
        font-weight: bold;
        color: #666666;
        margin-bottom: 8px;
    }

    .availability-filter-dropdown-picker-fallback[data-v-939ee512] {
        height: 368px;
    }

    .availability-filter-dropdown-picker-text[data-v-939ee512] {
        color: #333333;
        margin-bottom: 16px;
    }

    .availability-filter-dropdown-time-periods[data-v-939ee512] {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        grid-template-rows: 36px 36px;
        gap: 16px;
    }

    .coaches-filters-container[data-v-29743a72] {
        display: grid;
        gap: 16px;
        margin-top: 26px;
    }

    @media (min-width: 960px) {
        .coaches-filters-container[data-v-29743a72] {
            margin-top: 40px;
        }
    }

    @media (max-width: 960px) {
        .coaches-filters-container[data-v-29743a72] {
            gap: 8px;
        }
    }

    .coaches-filters-container[data-v-29743a72] .choices__list--dropdown .popular-badge {
        display: inline-flex;
    }

    .coaches-filters-container[data-v-29743a72] .popular-badge {
        font-size: 12px;
        line-height: 1.67;
        display: none;
        margin-left: 4px;
        white-space: nowrap;
        align-self: center;
    }

    .coaches-filters-container .coach-filters-row[data-v-29743a72] {
        display: grid;
        gap: 16px;
    }

    @media (max-width: 960px) {
        .coaches-filters-container .coach-filters-row[data-v-29743a72] {
            grid-template-columns: 1fr !important;
            gap: 8px;
        }
    }

    [data-v-29743a72] .choices__group[role="group"][data-value="All"] {
        display: none;
    }

    [data-v-29743a72] .igao-text-input--focused .igao-text-input-wrapper {
        z-index: 7777;
    }

    .coaches-filter-search-input[data-v-29743a72] input {
        font-weight: 600;
    }

    .coaches-filter-dropdown[data-v-29743a72] .choices__item {
        font-weight: 600;
    }

    .spinner-container[data-v-15ddf44a] {
        text-align: center;
        position: relative;
        min-height: 132px;
        height: 100%;
        padding: 16px 0;
    }

    .spinner-container .loading-coaches-spinner[data-v-15ddf44a] {
        position: absolute;
        top: calc(50% - 50px);
        left: calc(50% - 50px);
        color: #0567a7;
        width: var(--spinner-size, 100px);
        height: var(--spinner-size, 100px);
        border-width: 4px;
    }

    @media (min-width: 960px) {
        .reviews-filters[data-v-347de691] {
            justify-content: center;
        }
    }

    .spinner-container[data-v-347de691] {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
        margin-top: 32px;
    }

    @media (min-width: 960px) {
        .spinner-container[data-v-347de691] {
            margin-top: 64px;
        }
    }

    .no-success-stories[data-v-347de691] {
        margin-top: 32px;
    }

    @media (min-width: 960px) {
        .no-success-stories[data-v-347de691] {
            margin-top: 64px;
        }
    }

    .success-stories-cards[data-v-347de691] {
        display: grid;
        grid-template-columns: 1fr;
        gap: 16px;
        margin-top: 32px;
    }

    @media (min-width: 960px) {
        .success-stories-cards[data-v-347de691] {
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-top: 64px;
        }
    }

    .show-more-button[data-v-347de691] {
        position: relative;
        width: auto;
    }

    .show-more-button.btn[data-v-347de691] {
        padding: 13px 32px;
    }

    .actions-row[data-v-347de691] {
        display: flex;
        justify-content: center;
        margin-top: 24px;
    }

    @media (min-width: 960px) {
        .actions-row[data-v-347de691] {
            margin-top: 48px;
        }
    }

    .btn[data-v-55ea226e] {
        font-size: 12px;
        line-height: 1.67;
        border-radius: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #0567a7;
        position: relative;
        padding: 6px 15px;
        font-weight: bold;
        text-transform: uppercase;
        border: none;
        white-space: nowrap;
    }

    .btn.btn-primary[data-v-55ea226e] {
        color: #ffffff;
        background-color: #0567a7;
    }

    .btn.btn-primary[data-v-55ea226e]:hover {
        background-color: #045a8d;
        border-color: #045a8d;
    }

    .btn.btn-primary--green[data-v-55ea226e] {
        background-color: #15835c;
        border-color: #15835c;
    }

    .btn.btn-primary--green[data-v-55ea226e]:hover {
        background-color: #147642;
        border-color: #147642;
    }

    .btn.btn-disabled[data-v-55ea226e] {
        opacity: 0.65;
        pointer-events: none;
    }

    .btn.btn-link[data-v-55ea226e] {
        display: inline-block;
        height: auto;
        background: none;
        border: none;
        color: #0567a7;
        padding: 0;
        font-weight: 600;
    }

    .btn.btn-secondary[data-v-55ea226e] {
        background-color: #ffffff;
        border: solid 2px #eeeeee;
    }

    .btn.btn-secondary--gray[data-v-55ea226e] {
        border-color: #eeeeee;
        background-color: #eeeeee;
        color: #333333;
    }

    .btn.btn-secondary--gray[data-v-55ea226e]:hover {
        background-color: #d5d4d4;
        border-color: #d5d4d4;
    }

    .btn.btn-secondary--black[data-v-55ea226e] {
        border: solid 2px #eeeeee;
        background-color: #fff;
        color: #333;
    }

    .btn.btn-secondary[data-v-55ea226e]:hover {
        background-color: #ececec;
        border-color: #e6e6e6;
        color: #212529;
    }

    .btn.btn-danger[data-v-55ea226e] {
        color: #fff;
        background-color: #e8422f;
    }

    .btn.btn-danger[data-v-55ea226e]:hover {
        color: #fff;
    }

    .btn.btn-no-transform[data-v-55ea226e] {
        text-transform: none;
    }

    .igao-spinner[data-v-9d02a4c1] {
        width: 16px;
        height: 16px;
        border-width: 0.2em;
        display: inline-block;
        vertical-align: text-bottom;
        border: 2px solid currentColor;
        border-right-color: transparent;
        border-radius: 50%;
        animation: spinner-border-9d02a4c1 0.75s linear infinite;
    }

    @keyframes spinner-border-9d02a4c1 {
        to {
            transform: rotate(360deg);
        }
    }

    .igao-box[data-v-253e5cdb] {
        border: solid 2px #eeeeee;
        padding: 32px 16px;
        width: 100%;
    }

    @media (min-width: 960px) {
        .igao-box[data-v-253e5cdb] {
            padding: 36px 80px 48px;
        }
    }

    .cta-buttons[data-v-b7b4b200] {
        margin-top: 26px;
    }

    @media (min-width: 960px) {
        .cta-buttons[data-v-b7b4b200] {
            margin-top: 40px;
        }
    }

    @media (max-width: 960px) {
        .cta-buttons[data-v-b7b4b200] .btn {
            width: 100%;
        }
    }

    .cta-buttons[data-v-b7b4b200] .btn + .btn {
        margin-top: 12px;
    }

    @media (min-width: 960px) {
        .cta-buttons[data-v-b7b4b200] .btn + .btn {
            margin-top: 0;
            margin-left: 24px;
        }
    }</style>
<div style="margin-top: 50px;">
    <section class="section-wrapper" data-v-edc9e39b="" data-v-c99ebf58="">
        <h1 class="header" data-v-edc9e39b="">Reviews</h1>
        <div class="subheader" data-v-edc9e39b="">
            We’ve coached {{$reviews->total()}} candidates since getting started, here are some
            of their success stories:
        </div>
    </section>

    <section class="section-wrapper" data-v-347de691="" data-v-c99ebf58="">
        <form>
            <div class="coaches-filters-container reviews-filters" vertical="tech" data-v-347de691="" data-v-29743a72="">
                <div class="coach-filters-row" style="grid-template-columns:300px 300px;" data-v-29743a72="">
                    <div class="igao-dropdown igao-dropdown--size-m igao-dropdown--with-search coaches-filter-dropdown"
                         data-testid="role-filter">
                        <label class="igao-dropdown-label">Vai trò:</label>
                        <select class="igao-dropdown-native" wire:model.live="role_id">
                            <option value="">Tất cả</option>
                            @foreach($roles as $role)
                                <option value="{{$role->id}}">{{$role->name}}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="igao-dropdown igao-dropdown--size-m igao-dropdown--with-search coaches-filter-dropdown"
                         data-testid="company-filter">
                        <label class="igao-dropdown-label">Công ty:</label>
                        <select class="igao-dropdown-native" wire:model.live="company_id">
                            <option value="">Tất cả</option>
                            @foreach($companies as $role)
                                <option value="{{$role->id}}">{{$role->name}}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </div>
        </form>

        <div data-v-347de691="">
            <div class="success-stories-cards" data-v-347de691="">
                @foreach($reviews as $review)
                    <a class="link-wrapper" href="{{route('reviews.show',['slug' => $review->id])}}"
                       data-v-347de691="" data-v-c8ad65ec="">
                        <div class="success-story-list-card" data-v-c8ad65ec="">
                            <div data-v-c8ad65ec="">
                                <div class="review-card-header size-m" data-v-c8ad65ec="" data-v-e194142e="">
                                    <img
                                        class="headshot-image"
                                        src="{{\Illuminate\Support\Facades\Storage::url($review->author_image)}}"
                                        alt="{{$review->customer?->full_name}}" height="64" width="64"
                                        data-v-e194142e="">
                                    <img
                                        class="company-logo"
                                        src="{{\Illuminate\Support\Facades\Storage::url($review->company?->logo)}}"
                                        alt="{{$review->company?->name}}" data-v-e194142e="">
                                </div>
                            </div>
                            <div class="quote" data-v-c8ad65ec="">“{{@$review->content['q2'] ?? ''}}”</div>
                            <div class="position" data-v-c8ad65ec="">
                            <span class="name"
                                  data-v-c8ad65ec="">{{$review->customer?->full_name}}</span>
                                <span
                                    class="dash" data-v-c8ad65ec=""> - </span>
                                <span data-v-c8ad65ec="">{{$review->author_position}}</span>
                                {{--                                <span class="company-name" data-v-c8ad65ec="">Amazon</span>--}}
                            </div>
                            <div class="actions-line" data-v-c8ad65ec="">
                            <span class="read-story-link" data-v-c8ad65ec=""> Read
                        Story <img src="{{asset('icons/arrow-right.svg')}}" width="12" height="10"
                                   alt="Arrow right icon"
                                   data-v-c8ad65ec=""></span>
                            </div>
                        </div>
                    </a>

                @endforeach
            </div>
        </div>
    </section>
</div>
