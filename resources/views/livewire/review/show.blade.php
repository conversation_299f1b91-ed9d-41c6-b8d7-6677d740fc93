<style>
    @font-face {
        font-family: "Open Sans";
        font-style: italic;
        font-weight: 400;
        font-stretch: 100%;
        font-display: swap;
        src: url(/fonts/open-sans-400-italic.woff2) format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD
    }

    @font-face {
        font-family: "Open Sans";
        font-style: normal;
        font-weight: 400;
        font-stretch: 100%;
        font-display: swap;
        src: url(/fonts/open-sans-400.woff2) format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD
    }

    @font-face {
        font-family: "Open Sans";
        font-style: normal;
        font-weight: 600;
        font-stretch: 100%;
        font-display: swap;
        src: url(/fonts/open-sans-600.woff2) format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD
    }

    @font-face {
        font-family: "Open Sans";
        font-style: normal;
        font-weight: 700;
        font-stretch: 100%;
        font-display: swap;
        src: url(/fonts/open-sans-700.woff2) format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD
    }

    body {
        font-size: 18px;
        line-height: 1.56;
        font-family: "Open Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
        font-weight: 400;
        color: #4a4a4a;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.004)
    }
</style>
<style>
    .account-buttons[data-astro-cid-hmtt24ci] {
        display: flex;
        flex-direction: column;
        width: 100%
    }

    .account-buttons[data-astro-cid-hmtt24ci] a.btn:not(:last-child) {
        margin-bottom: 12px
    }

    .mobile-navigation-container[data-astro-cid-hmtt24ci] {
        display: none;
        position: fixed;
        top: 0;
        width: 100vw;
        left: 0;
        flex-direction: column;
        align-items: start;
        background: #ffffff;
        z-index: 400;
        padding: 86px 24px 156px;
        height: 100vh;
        overflow: auto
    }

    .mobile-navigation-container[data-astro-cid-hmtt24ci].open {
        display: flex
    }

    nav[data-astro-cid-2xhbaixd] {
        flex-grow: 1;
        display: flex
    }

    .fake-header[data-astro-cid-xbstl6g3] {
        display: flex;
        min-height: 70px;
        width: 100%
    }

    .mobile-sub-navigation[data-astro-cid-xbstl6g3] {
        height: 58px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fff;
        border-bottom: 2px solid #eeeeee;
        color: #4a4a4a
    }

    @media (min-width: 1200px) {
        .mobile-sub-navigation[data-astro-cid-xbstl6g3] {
            display: none
        }
    }

    .mobile-sub-navigation[data-astro-cid-xbstl6g3] a[data-astro-cid-xbstl6g3].navigation-link {
        position: relative;
        box-sizing: border-box;
        text-decoration: none;
        font-weight: 600;
        cursor: pointer;
        color: #000;
        white-space: nowrap;
        font-size: 14px
    }

    .mobile-sub-navigation[data-astro-cid-xbstl6g3] a[data-astro-cid-xbstl6g3].navigation-link.active {
        color: #0567a7
    }

    .mobile-sub-navigation[data-astro-cid-xbstl6g3] a[data-astro-cid-xbstl6g3].navigation-link:not(:last-child) {
        margin-right: 16px
    }

    header[data-astro-cid-xbstl6g3] {
        position: fixed;
        width: 100%;
        box-sizing: border-box;
        height: 70px;
        border-bottom: 6px solid #eeeeee;
        background-color: #ffffff;
        padding: 10px 24px;
        display: flex;
        z-index: 500
    }

    @media (min-width: 1200px) {
        header[data-astro-cid-xbstl6g3] {
            padding: 10px 64px
        }
    }

    header[data-astro-cid-xbstl6g3] .header-container[data-astro-cid-xbstl6g3] {
        display: flex;
        width: 100%;
        align-items: center;
        margin: auto
    }

    header[data-astro-cid-xbstl6g3] .logo[data-astro-cid-xbstl6g3] {
        display: flex
    }

    header[data-astro-cid-xbstl6g3] .logo[data-astro-cid-xbstl6g3] > a[data-astro-cid-xbstl6g3] {
        display: flex
    }

    .marketing-container {
        max-width: 692px;
        margin: auto;
        text-align: left;
        padding: 0 16px;
    }

    .marketing-container > * {
        width: 100%;
    }

    .marketing-container hr {
        margin: 2rem 0;
        border: 0;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
    }

    @media (min-width: 960px) {
        .marketing-container hr {
            margin: 3rem 0;
        }
    }

    .marketing-container h1 {
        margin-top: 12px;
    }

    .marketing-container h2 {
        font-size: 1.5rem;
        font-weight: bold;
        line-height: 1.42;
        letter-spacing: -0.1px;
        color: #333;
    }

    .back-to-offers {
        margin-top: 48px;
        font-size: 14px;
        font-weight: 600;
        letter-spacing: -0.09px;
        color: #0567a7;
    }

    @media (min-width: 960px) {
        .back-to-offers {
            margin-top: 72px;
        }
    }

    .back-to-offers > a {
        display: inline-flex;
        align-items: center;
    }

    .candidate-card {
        margin-top: 24px;
        padding: 20px 20px 0;
        box-shadow: 0 1px 2px 0 rgba(51, 51, 51, 0.12);
        background-color: #fff;
    }

    @media (min-width: 960px) {
        .candidate-card {
            margin-top: 32px;
        }
    }

    .candidate-card__header {
        margin-bottom: 4px;
    }

    .candidate-card__row {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        padding: 13px 0;
    }

    @media (min-width: 960px) {
        .candidate-card__row {
            flex-wrap: nowrap;
        }
    }

    .candidate-card__row:not(:last-of-type) {
        border-bottom: 1px solid #eee;
    }

    .candidate-card__label {
        font-size: 0.75rem;
        font-weight: bold;
        line-height: 1.67;
        color: #4a4a4a;
        text-transform: uppercase;
        margin-right: 1rem;
        white-space: nowrap;
        width: 100%;
    }

    @media (min-width: 960px) {
        .candidate-card__label {
            width: auto;
        }
    }

    .candidate-card__value {
        font-size: 0.875rem;
        line-height: 1.57;
        color: #4a4a4a;
        text-align: left;
    }

    @media (min-width: 960px) {
        .candidate-card__value {
            text-align: right;
        }
    }

    .qa-block {
        margin-top: 1.5rem;
    }

    @media (min-width: 960px) {
        .qa-block {
            margin-top: 2rem;
        }
    }

    .qa-block .qa-block__question {
        font-size: 1.25rem;
        font-weight: bold;
        line-height: 1.5;
        letter-spacing: -0.1px;
        color: #333;
    }

    .qa-block__answer {
        margin-top: 0.75rem;
        font-size: 1rem;
        line-height: 1.5;
        letter-spacing: -0.1px;
        color: #4a4a4a;
    }

    .qa-block__answer.coach-profiles__coaches {
        margin-top: 1.5rem;
        display: grid;
        gap: 16px 15px;
        grid-template-columns: repeat(auto-fill, minmax(270px, 1fr));
        width: 100%;
        text-align: left;
    }

    .related-stories {
        margin-top: 24px;
        display: grid;
        gap: 1rem;
        grid-template-columns: 1fr;
    }

    @media (min-width: 960px) {
        .related-stories {
            margin-top: 32px;
        }
    }

    @media (min-width: 960px) {
        .related-stories {
            grid-template-columns: 1fr 1fr;
        }
    }

    .review-card-header[data-v-e194142e] {
        display: flex;
        align-items: center;
    }

    .size-m .headshot-image[data-v-e194142e] {
        width: 64px;
        height: 64px;
    }

    .size-m .company-logo[data-v-e194142e] {
        margin-left: 20px;
    }

    .size-s .headshot-image[data-v-e194142e] {
        width: 48px;
        height: 48px;
    }

    .size-s .company-logo[data-v-e194142e] {
        margin-left: 12px;
    }

    .headshot-image[data-v-e194142e] {
        border-radius: 50%;
    }

    .company-title[data-v-e194142e] {
        font-size: 20px;
        line-height: 1.5;
        font-weight: bold;
        letter-spacing: -0.08px;
        color: #333;
    }

    .link-wrapper[data-v-c8ad65ec] {
        text-decoration: none;
    }

    .link-wrapper .success-story-list-card[data-v-c8ad65ec] {
        border: 1px solid transparent;
    }

    .link-wrapper:hover .success-story-list-card[data-v-c8ad65ec] {
        border: 1px solid #0567a7;
    }

    .success-story-list-card[data-v-c8ad65ec] {
        display: flex;
        flex-direction: column;
        padding: 24px;
        box-shadow: 0 1px 2px 0 rgba(51, 51, 51, 0.12);
        background-color: #fff;
        text-align: left;
        height: 100%;
    }

    .success-story-list-card .quote[data-v-c8ad65ec] {
        font-size: 18px;
        line-height: 1.56;
        font-weight: 600;
        letter-spacing: -0.1px;
        color: #333;
        margin-top: 16px;
    }

    .success-story-list-card .position[data-v-c8ad65ec] {
        font-size: 16px;
        line-height: 1.5;
        font-style: italic;
        letter-spacing: -0.09px;
        color: #4a4a4a;
        flex-grow: 1;
        height: 100%;
        margin-top: 12px;
    }

    .success-story-list-card .position .name[data-v-c8ad65ec] {
        font-weight: 600;
        font-style: normal;
        color: #1a1a1a;
    }

    .success-story-list-card .position .dash[data-v-c8ad65ec] {
        font-weight: 600;
        font-style: normal;
        color: #a6a6a6;
    }

    .success-story-list-card .position .company-name[data-v-c8ad65ec] {
        font-weight: 600;
    }

    .success-story-list-card .actions-line[data-v-c8ad65ec] {
        text-align: right;
        margin-top: 16px;
    }

    .success-story-list-card .actions-line .read-story-link[data-v-c8ad65ec] {
        font-size: 16px;
        line-height: 1.5;
        font-weight: 600;
        letter-spacing: -0.09px;
        color: #0567a7;
    }

    .coach-profile-card-container[data-v-f24fc346] {
        position: relative;
    }

    .link-wrapper[data-v-f24fc346]:hover, .link-wrapper[data-v-f24fc346]:focus, .link-wrapper[data-v-f24fc346]:active {
        text-decoration: none;
    }

    .link-wrapper:hover .course-container[data-v-f24fc346], .link-wrapper:hover .coach-profile-card[data-v-f24fc346], .link-wrapper:focus .course-container[data-v-f24fc346], .link-wrapper:focus .coach-profile-card[data-v-f24fc346], .link-wrapper:active .course-container[data-v-f24fc346], .link-wrapper:active .coach-profile-card[data-v-f24fc346] {
        border: 1px solid #0567a7;
    }

    .link-wrapper--disabled[data-v-f24fc346] {
        pointer-events: none;
        opacity: 0.7;
    }

    .coach-profile-card[data-v-f24fc346] {
        position: relative;
        padding: 20px;
        box-shadow: 0 1px 2px 0 rgba(51, 51, 51, 0.04);
        border: solid 1px #eeeeee;
        background-color: #ffffff;
        height: 100%;
        display: flex;
        flex-direction: column;
        color: #333333;
        text-align: left;
    }

    .coach-profile-card__avatar-container[data-v-f24fc346] {
        position: relative;
    }

    .coach-profile-card__super-coach-title[data-v-f24fc346] {
        font-size: 10px;
        line-height: 1.8;
        font-weight: 700;
        font-stretch: normal;
        font-style: normal;
        letter-spacing: 2px;
        color: #9e6a1b;
        text-transform: uppercase;
    }

    .coach-profile-card__super-coach-badge[data-v-f24fc346] {
        position: absolute;
        top: -2px;
        right: 18px;
    }

    .coach-profile-card__header[data-v-f24fc346] {
        display: flex;
        margin-bottom: 18px;
    }

    .coach-profile-card__avatar[data-v-f24fc346] {
        max-height: 72px;
        max-width: 72px;
        margin-right: 16px;
        border-radius: 50%;
        position: relative;
        overflow: hidden;
        filter: grayscale(1);
    }

    .coach-profile-card__avatar[data-v-f24fc346]::before {
        content: "";
        width: 100%;
        height: 100%;
        background-color: #eeeeee;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .coach-profile-card__badge[data-v-f24fc346] {
        margin: 4px 0 8px;
    }

    .coach-profile-card__name-and-position[data-v-f24fc346] {
        display: flex;
        align-items: flex-start;
        flex-direction: column;
        justify-content: center;
        flex-grow: 1;
    }

    .coach-profile-card__name[data-v-f24fc346] {
        font-weight: 700;
        font-size: 20px;
        line-height: 1.2;
    }

    .coach-profile-card__position[data-v-f24fc346] {
        font-size: 14px;
        font-weight: 600;
        line-height: 1.57;
        color: #4a4a4a;
        margin-top: 4px;
    }

    .coach-profile-card__info-row[data-v-f24fc346] {
        display: flex;
        align-items: center;
        font-size: 14px;
    }

    .coach-profile-card__info-row + .coach-profile-card__info-row[data-v-f24fc346] {
        margin-top: 10px;
    }

    .coach-profile-card__info-row .new-badge[data-v-f24fc346] {
        color: #a67424;
    }

    .coach-profile-card__info-row .badge-tooltip[data-v-f24fc346] {
        display: flex;
    }

    .coach-profile-card__info-row .badge-tooltip img[data-v-f24fc346] {
        margin-right: 0;
    }

    .coach-profile-card__info-row img[data-v-f24fc346] {
        width: 16px;
        height: 16px;
        min-width: 16px;
        margin-right: 12px;
    }

    .coach-profile-card__info-row img[data-v-f24fc346]:not(.keep-color) {
        filter: invert(74%) sepia(3%) saturate(14%) hue-rotate(320deg) brightness(92%) contrast(91%);
    }

    .coach-profile-card--stub[data-v-f24fc346] {
        height: 209px;
        background-repeat: no-repeat;
        background-size: contain;
        position: relative;
    }

    .coach-profile-card__price-row[data-v-f24fc346] {
        font-size: 12px;
        line-height: 1.67;
        display: flex;
        align-items: center;
        flex-grow: 1;
        height: 100%;
        justify-content: flex-end;
        align-items: flex-end;
    }

    .coach-profile-card__price-row > div[data-v-f24fc346] {
        display: flex;
        gap: 8px;
        align-items: center;
    }

    .coach-profile-card__price-row > div > span[data-v-f24fc346]:last-child {
        font-size: 18px;
        line-height: 1.56;
        font-weight: 700;
        color: #0567a7;
    }

    .coach-badge-container[data-v-92fb9e4b] {
        display: inline-flex;
        justify-content: flex-start;
        align-items: center;
        gap: 4px;
        flex-wrap: wrap;
    }

    @media (min-width: 960px) {
        .coach-badge-container[data-v-92fb9e4b] {
            gap: 8px;
        }
    }

    .coach-badge[data-v-92fb9e4b] {
        text-transform: uppercase;
        color: #fff;
        text-align: center;
        font-size: 10px;
        font-style: normal;
        font-weight: 700;
        line-height: 14px;
        /* 140% */
        letter-spacing: -0.1px;
        display: inline-flex;
        padding: 3px 8px;
        justify-content: center;
        align-items: center;
    }

    .coach-badge--bogof[data-v-92fb9e4b] {
        background: #15835c;
    }

    .coach-badge--supercoach[data-v-92fb9e4b] {
        background: #9e6a1b;
    }

    .coach-badge--certified[data-v-92fb9e4b] {
        background: #666666;
    }

    .content-wrapper[data-v-e2113d11] {
        font-size: 12px;
        line-height: 1.67;
        z-index: 500;
        display: none;
        font-weight: 600;
        font-stretch: normal;
        font-style: normal;
        letter-spacing: normal;
        color: #333;
        box-shadow: 0 2px 4px 0 rgba(51, 51, 51, 0.12);
        padding: 11px 12px;
        max-width: 282px;
        white-space: normal;
        background-color: #fff;
        border: 1px solid #eee;
    }

    .content-wrapper .popover-arrow[data-v-e2113d11], .content-wrapper .popover-arrow[data-v-e2113d11]::before {
        position: absolute;
        width: 8px;
        height: 8px;
        background: inherit;
    }

    .content-wrapper .popover-arrow[data-v-e2113d11] {
        visibility: hidden;
    }

    .content-wrapper .popover-arrow[data-v-e2113d11]::before {
        visibility: visible;
        content: "";
        transform: rotate(45deg);
    }

    .content-wrapper[data-show][data-v-e2113d11] {
        display: block;
    }

    .content-wrapper[data-popper-placement^=top] > .popover-arrow[data-v-e2113d11] {
        bottom: -4px;
    }

    .content-wrapper[data-popper-placement^=top] > .popover-arrow[data-v-e2113d11]::before {
        border-right: 1px solid #eee;
        border-bottom: 1px solid #eee;
    }

    .content-wrapper[data-popper-placement^=left] > .popover-arrow[data-v-e2113d11]::before {
        border-right: 1px solid #eee;
        border-top: 1px solid #eee;
    }

    .content-wrapper[data-popper-placement^=right] > .popover-arrow[data-v-e2113d11]::before {
        border-left: 1px solid #eee;
        border-bottom: 1px solid #eee;
    }

    .content-wrapper[data-popper-placement^=bottom] > .popover-arrow[data-v-e2113d11] {
        top: -4px;
    }

    .content-wrapper[data-popper-placement^=bottom] > .popover-arrow[data-v-e2113d11]::before {
        border-left: 1px solid #eee;
        border-top: 1px solid #eee;
    }

    .content-wrapper[data-popper-placement^=left] > .popover-arrow[data-v-e2113d11] {
        right: -4px;
    }

    .content-wrapper[data-popper-placement^=right] > .popover-arrow[data-v-e2113d11] {
        left: -4px;
    }

    .tooltip-wrapper-inline[data-v-b905ec3a] {
        display: inline;
    }

    .tooltip-wrapper-flex[data-v-b905ec3a] {
        display: flex;
    }

    .account-buttons[data-v-f9a5b2be] .btn.btn {
        font-size: 16px;
        line-height: 1.5;
        border: none;
        font-weight: bold;
        padding: 3px 12px;
        height: auto;
    }

    .account-buttons[data-v-f9a5b2be] .btn.link-style {
        border: 0;
        background: none;
        color: #242424;
        padding-left: 0;
        padding-right: 0;
    }

    .account-buttons[data-v-f9a5b2be] .btn.link-style:hover {
        opacity: 0.7;
        background: none;
    }

    .account-buttons[data-v-f9a5b2be] .btn.link-style.become-coach {
        color: #666666;
    }

    .header-account-container[data-v-5d2dab64] {
        display: flex;
        align-items: center;
    }

    .account-buttons[data-v-5d2dab64] {
        display: none;
    }

    @media (min-width: 1200px) {
        .account-buttons[data-v-5d2dab64] {
            display: block;
        }
    }

    .account-buttons[data-v-5d2dab64] > a {
        margin-right: 32px;
    }

    .account-buttons[data-v-5d2dab64] > a:last-of-type {
        margin-right: 18px;
    }

    .mobile-menu-icon[data-v-5d2dab64] {
        width: 44px;
        height: 44px;
        align-items: center;
        justify-content: center;
        border: none;
        background: no-repeat;
        display: flex;
    }

    @media (min-width: 1200px) {
        .mobile-menu-icon[data-v-5d2dab64] {
            display: none;
        }
    }

    .mobile-menu-icon.cart-icon[data-v-5d2dab64] {
        display: flex;
    }

    .igao-btn.igao-btn--full-width[data-v-0dc3f13d] {
        width: 100%;
    }

    .igao-btn.igao-btn--l[data-v-0dc3f13d] {
        height: 50px;
        padding: 15px 32px;
    }

    .igao-btn.igao-btn--xl[data-v-0dc3f13d] {
        height: 54px;
        padding: 17px 32px;
    }

    .igao-btn.igao-btn--m[data-v-0dc3f13d] {
        height: 40px;
        padding: 10px 20px;
    }

    .igao-btn.igao-btn--s[data-v-0dc3f13d] {
        height: 32px;
        padding: 6px 16px;
    }

    .igao-spinner[data-v-9d02a4c1] {
        width: 16px;
        height: 16px;
        border-width: 0.2em;
        display: inline-block;
        vertical-align: text-bottom;
        border: 2px solid currentColor;
        border-right-color: transparent;
        border-radius: 50%;
        animation: spinner-border-9d02a4c1 0.75s linear infinite;
    }

    @keyframes spinner-border-9d02a4c1 {
        to {
            transform: rotate(360deg);
        }
    }

    .btn[data-v-55ea226e] {
        font-size: 12px;
        line-height: 1.67;
        border-radius: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #0567a7;
        position: relative;
        padding: 6px 15px;
        font-weight: bold;
        text-transform: uppercase;
        border: none;
        white-space: nowrap;
    }

    .btn.btn-primary[data-v-55ea226e] {
        color: #ffffff;
        background-color: #0567a7;
    }

    .btn.btn-primary[data-v-55ea226e]:hover {
        background-color: #045a8d;
        border-color: #045a8d;
    }

    .btn.btn-primary--green[data-v-55ea226e] {
        background-color: #15835c;
        border-color: #15835c;
    }

    .btn.btn-primary--green[data-v-55ea226e]:hover {
        background-color: #147642;
        border-color: #147642;
    }

    .btn.btn-disabled[data-v-55ea226e] {
        opacity: 0.65;
        pointer-events: none;
    }

    .btn.btn-link[data-v-55ea226e] {
        display: inline-block;
        height: auto;
        background: none;
        border: none;
        color: #0567a7;
        padding: 0;
        font-weight: 600;
    }

    .btn.btn-secondary[data-v-55ea226e] {
        background-color: #ffffff;
        border: solid 2px #eeeeee;
    }

    .btn.btn-secondary--gray[data-v-55ea226e] {
        border-color: #eeeeee;
        background-color: #eeeeee;
        color: #333333;
    }

    .btn.btn-secondary--gray[data-v-55ea226e]:hover {
        background-color: #d5d4d4;
        border-color: #d5d4d4;
    }

    .btn.btn-secondary--black[data-v-55ea226e] {
        border: solid 2px #eeeeee;
        background-color: #fff;
        color: #333;
    }

    .btn.btn-secondary[data-v-55ea226e]:hover {
        background-color: #ececec;
        border-color: #e6e6e6;
        color: #212529;
    }

    .btn.btn-danger[data-v-55ea226e] {
        color: #fff;
        background-color: #e8422f;
    }

    .btn.btn-danger[data-v-55ea226e]:hover {
        color: #fff;
    }

    .btn.btn-no-transform[data-v-55ea226e] {
        text-transform: none;
    }
</style>
<div style="padding-top: 40px;">
    <div class="marketing-container">
        <div class="back-to-offers">
            <a href="{{route('reviews.index')}}" wire:navigate>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"
                     width="16" height="16">
                    <g class="nc-icon-wrapper" fill="#111111">
                        <polygon fill="#0567a7" points="10,13.4 4.6,8 10,2.6 11.4,4 7.4,8 11.4,12 "></polygon>
                    </g>
                </svg>

                Quay lại phần Đánh giá
            </a></div>
        <h1>{{$review->customer?->full_name}} nhận được lời mời làm {{$review->role?->name}}
            tại {{$review->company?->name}}</h1>
        <div class="candidate-card">
            <div class="review-card-header size-m" data-v-e194142e="">
                <img class="headshot-image"
                     src="{{\Illuminate\Support\Facades\Storage::url($review->author_image)}}"
                     alt="{{$review->customer?->full_name}}" height="64"
                     width="64" data-v-e194142e="">
                <img
                    class="company-logo"
                    src="{{\Illuminate\Support\Facades\Storage::url($review->company?->logo)}}"
                    alt="{{$review->company?->name}} logo" data-v-e194142e="">
            </div>
            <div class="candidate-card__row">
                <span class="candidate-card__label">Tên</span>
                <span class="candidate-card__value">{{$review->customer?->full_name}}</span>
            </div>
            <div class="candidate-card__row">
                <span class="candidate-card__label">Vị trí mới</span>
                <span class="candidate-card__value">{{$review->author_position}}</span>
            </div>
{{--            <div class="candidate-card__row">--}}
{{--                <span class="candidate-card__label">Location</span>--}}
{{--                <span class="candidate-card__value">North America</span>--}}
{{--            </div>--}}
            <div class="candidate-card__row">
                <span class="candidate-card__label">Ngày nhận offer</span>
                <span class="candidate-card__value">{{$review->created_at->format('M d, Y')}}</span>
            </div>
{{--            <div class="candidate-card__row">--}}
{{--                <span class="candidate-card__label">Coaching Sessions</span>--}}
{{--                <span class="candidate-card__value"> 5 </span>--}}
{{--            </div>--}}
        </div>
        <div class="qas-container">
            <div class="qa-block">
                <h3 class="qa-block__question">Thách thức lớn nhất của bạn là gì?</h3>
                <div class="qa-block__answer">{{@$review->content['q1'] ?? ''}}
                </div>
            </div>
            <div class="qa-block">
                <h3 class="qa-block__question">Moveup đã giúp bạn như thế nào?</h3>
                <div class="qa-block__answer">{{@$review->content['q2'] ?? ''}}
                </div>
            </div>
            <div class="qa-block">
                <h3 class="qa-block__question">Bạn có lời khuyên nào cho các ứng viên khác không?</h3>
                <div class="qa-block__answer">
                    {{@$review->content['q3'] ?? ''}}
                </div>
            </div>
            <div class="qa-block">
                <h3 class="qa-block__question">
                    Bạn khuyên bạn nên làm việc cùng huấn luyện viên nào?

                </h3>
                <div class="qa-block__answer coach-profiles__coaches">
                    <livewire:components.coach :coach="$review->coach"/>

                </div>
            </div>
        </div>
        <hr>
    </div>
</div>
