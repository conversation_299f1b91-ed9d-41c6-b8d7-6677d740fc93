<div>
    @if($faqType && $faqs->count() > 0)
        <div class="faq-container">
            <h2 class="text-2xl font-bold mb-6">{{ $faqType->name }}</h2>
            
            @if($faqType->description)
                <div class="mb-8">
                    {!! $faqType->description !!}
                </div>
            @endif
            
            <div class="space-y-4">
                @foreach($faqs as $faq)
                    <div x-data="{ open: false }" class="border border-gray-200 rounded-lg overflow-hidden">
                        <button 
                            @click="open = !open" 
                            class="flex justify-between items-center w-full px-4 py-3 text-left bg-white hover:bg-gray-50"
                        >
                            <span class="font-medium">{{ $faq->question }}</span>
                            <svg 
                                x-bind:class="open ? 'rotate-180 transform' : ''" 
                                class="w-5 h-5 transition-transform duration-200" 
                                fill="none" 
                                stroke="currentColor" 
                                viewBox="0 0 24 24" 
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        
                        <div 
                            x-show="open" 
                            x-transition:enter="transition ease-out duration-200" 
                            x-transition:enter-start="opacity-0 transform scale-95" 
                            x-transition:enter-end="opacity-100 transform scale-100" 
                            x-transition:leave="transition ease-in duration-100" 
                            x-transition:leave-start="opacity-100 transform scale-100" 
                            x-transition:leave-end="opacity-0 transform scale-95" 
                            class="px-4 py-3 bg-gray-50"
                        >
                            <div class="prose max-w-none">
                                {!! $faq->answer !!}
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @else
        <div class="text-center py-12">
            <p class="text-gray-500">No FAQs found.</p>
        </div>
    @endif
</div>
