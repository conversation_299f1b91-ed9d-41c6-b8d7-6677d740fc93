<?php

use App\Models\BlogCategory;
use App\Models\FaqType;
use App\Models\Service;
use function Livewire\Volt\{state, mount};

state(['type', 'faqs']);

mount(function () {
    $this->faqs = FaqType::query()->where('slug', $this->type)->with(['activeFaqs' => function ($query) {
        $query->orderBy('created_at', 'desc');
    }])->first();
});

?>


<section data-v-c99ebf58="" data-v-39ed3119="" class="section-wrapper">
    <h2 data-v-39ed3119="">Những câu hỏi thường gặp</h2>
    <div data-v-7b9c232d="" data-v-39ed3119="" class="faq-accordion">
        @foreach($faqs?->activeFaqs ?? [] as $index => $faq)
            <div data-v-7b9c232d="" x-data="{ open: false }" class="accordion-item">
                <button data-v-7b9c232d=""
                        @click="open = !open"
                        id="faq-question-{{ $index }}"
                        class="accordion-item-head" type="button"
                        aria-expanded="false" aria-controls="faq-answer-0">
                    {{$faq->question}}
                    <img
                        :src="open
                        ? '{{asset('icons/icon-faq-close.svg')}}'
                        : '{{asset('icons/icon-faq-open.svg')}}'"
                        width="16"
                        height="16"
                        alt="FAQ toggle icon"
                        loading="lazy"
                        class="transition-all duration-200 ease-linear"
                    />
                </button>

                <div data-v-7b9c232d="" :id="'faq-answer-{{ $index }}'"
                     x-show="open"
                     x-transition
                     :style="open ? 'height: 24px; overflow: hidden;' : 'display: none;'"
                     class="accordion-item-description" role="region"
                     aria-labelledby="faq-question-0"
                >
                    <p style="    font-weight: 400;">
                        {{$faq->answer}}
                    </p>
                </div>
            </div>

        @endforeach

    </div>
</section>
