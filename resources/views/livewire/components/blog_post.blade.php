<?php

use App\Models\BlogPost;
use App\Models\Coache;
use App\Models\Company;
use App\Models\Role;
use App\Models\Service;
use App\Models\Specialized;
use function Livewire\Volt\{state, mount, computed, usesPagination, with};

state([
    'posts'
]);
mount(function () {
    $this->posts = BlogPost::query()->select('slug', 'title')->orderBy('created_at', 'desc')->limit(9)->get();
});

?>

<section class="section-wrapper" data-v-d6c5d653="" data-v-c99ebf58="">
    <!--[-->
    <h2 data-v-d6c5d653="">Bắt đầu với một trong những hướng dẫn phỏng vấn miễn phí của chúng tôi</h2>
    <div class="grid-container" data-v-d6c5d653="">
        @foreach($posts as $post)
            <a href="{{route('blog.show',['slug' => $post->slug])}}" class="link-wrapper"
               data-v-d6c5d653="">
                <div class="cards-container" data-v-d6c5d653="">
                    <div class="card-container__short-title" data-v-d6c5d653="">PRODUCT INTERVIEWS</div>
                    <div class="card-container__title" data-v-d6c5d653="">{{$post->title}}</div>
                    <div class="card-container__action" data-v-d6c5d653="">
                        Đọc
                        <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-arrow-right"
                             viewBox="0 0 4 7" width="4" height="8" data-v-d6c5d653="">
                            <path fill="#444" d="M4 3.5L0 7V0z" data-v-d6c5d653=""></path>
                        </svg>
                    </div>
                </div>
            </a>

        @endforeach

    </div>

</section>
