<?php

use App\Models\BlogCategory;
use App\Models\FaqType;
use App\Models\Service;
use function Livewire\Volt\{state, mount};

state(['packages', 'popular' => 5, 'package_id']);

mount(function () {
    $this->packages = \App\Models\Package::query()->where('is_active', true)->orderBy('hours', 'asc')->get();
    if (!$this->package_id) {
        $this->package_id = $this->packages->firstWhere('hours', $this->popular)?->id;
    }
});

$buyNow = function (){
    if (!auth()->guard('customer')->check()){
        $this->redirectRoute('login', navigate: true);
    }
    $package_id = $this->package_id;
    $package = $this->packages->where('id',$package_id)->first();
    if (empty($package)){
        return;
    }
    session()->put('package',$package);
    $this->redirectRoute('checkout', navigate: true);

}

?>


<div data-v-39bff86a="" data-v-d8bfb7f9="" class="session-picker-container"
     x-data="{
                        packages: @js($packages->map(fn($p) => ['id' => $p->id, 'hours' => $p->hours, 'price' => $p->price])->values()),
                        selectedId: @entangle('package_id'),

                        get selectedPackage() {
                            return this.packages.find(p => p.id == this.selectedId);
                        },

                        get basePackage() {
                            return this.packages[0]; // Gói có giá cao nhất theo giờ
                        },

                        get total() {
                            return this.selectedPackage ? this.selectedPackage.price * this.selectedPackage.hours : 0;
                        },

                        get savings() {
                            if (!this.selectedPackage || !this.basePackage) return 0;
                            const baseRate = this.basePackage.price;
                            const selectedRate = this.selectedPackage.price;
                            return Math.max(0, (baseRate - selectedRate) * this.selectedPackage.hours);
                        },

                        get formatVND() {
                            return new Intl.NumberFormat('vi-VN', {
                                style: 'currency',
                                currency: 'VND',
                                maximumFractionDigits: 0
                            });
                        },

                    }"
>
    <h4 data-v-39bff86a="" >
        Bạn muốn mua bao nhiêu giờ huấn luyện?
    </h4>
    <div data-v-39bff86a="" class="session-picker-sub-title">
        Có thể lên lịch giờ học với bất kỳ huấn luyện viên nào
        và cho bất kỳ dịch vụ nào sau khi mua.
    </div>
    <div data-v-39bff86a="" class="purchase-sessions">
        <div data-v-39bff86a="" class="purchase-sessions__choices-container">
            <div  data-v-39bff86a="" class="purchase-sessions__choices-bar"
                  style="max-width: 873px;">
                @foreach($packages as $package)
                    <div data-v-39bff86a="" class="purchase-sessions__choices-bar__item"
                         :class="{ 'active': selectedId == {{ $package->id }} }"
                         @click="selectedId = {{ $package->id }}"
                    >
                                        <span
                                            data-v-39bff86a="" class="purchase-sessions_item_quantity_price">
                                            <span
                                                data-v-39bff86a="">{{$package->hours}}</span>
                                            <span data-v-39bff86a=""
                                                  class="purchase-sessions_item_price_per_hour">{{number_format($package->price)}}đ/h</span>
                                        </span>
                    </div>

                @endforeach

            </div>
            <div data-v-39bff86a="" class="purchase-sessions__choices-responsive">
                <div data-v-39bff86a=""
                     class="purchase-sessions__choices-responsive__most-popular purchase-sessions__choices-responsive__most-popular--hidden">
                    Phổ biến
                </div>
                <select data-v-39bff86a="" wire:model="package_id"
                        x-model="selectedId"
                        class="custom-select purchase-sessions__choices-responsive__dropdown"
                        name="quantity" area-label="Sessions quantity"
                        aria-labelledby="buy-hours-anchor" title="Sessions quantity"
                        data-js="mobile-selector">
                    @foreach($packages as $package)
                        <option value="{{$package->id}}" >{{$package->hours}} Giờ - {{number_format($package->price)}}đ/h</option>
                    @endforeach

                </select>
            </div>
        </div>
        <div data-v-39bff86a="" class="purchase-sessions__total">
            Tổng:
            <span x-text="formatVND.format(total) + ' (' + (selectedPackage?.hours ?? 0) + 'h)'"></span>
        </div>
        <div data-v-39bff86a="" class="purchase-sessions__savings">
            <span x-text="'Bạn tiết kiệm được ' + formatVND.format(savings)"></span>
        </div>
        <div data-v-39bff86a="" class="purchase-sessions__buttons" id="buy-hours-anchor">
            <button data-v-55ea226e="" data-v-0dc3f13d="" data-v-39bff86a="" wire:click="buyNow"
                    class="btn btn-primary igao-btn igao-btn--l" size="l" fullwidth="false"> Mua ngay
            </button>

        </div>
    </div>

</div>
