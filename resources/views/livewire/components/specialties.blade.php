<?php

use function Livewire\Volt\{state, mount};

state([
    'coach',
    'hard_skills',
    'soft_skills',
    'certifications'
]);
mount(function () {
    $specialties = $this->coach?->specialties ?? [];
    $this->hard_skills = @$specialties['hard_skills'] ?? [];
    $this->soft_skills = @$specialties['soft_skills'] ?? [];
    $this->certifications = @$specialties['certifications'] ?? [];
});
?>

<div class="coach-specialties-container" data-v-8f2f8ad9="">
    <div class="coach-specialties-title" data-v-8f2f8ad9=""> <PERSON><PERSON><PERSON><PERSON> ng<PERSON>nh</div>
    <div class="coach-specialties-body" data-v-8f2f8ad9="">
        @if($hard_skills)
            <div class="coach-specialties-group" data-v-8f2f8ad9="">
                <div class="coach-specialties-group-title" data-v-8f2f8ad9=""><PERSON><PERSON> năng cứng</div>
                @foreach($hard_skills as $h)
                    <span class="tag-like-link tag-like-link--s coach-specialties-item" data-v-8f2f8ad9=""
                          data-v-d93fe582="">{{$h}}</span>

                @endforeach

            </div>
        @endif
        @if($soft_skills)
            <div class="coach-specialties-group" data-v-8f2f8ad9="">
                <div class="coach-specialties-group-title" data-v-8f2f8ad9="">Kỹ năng mền</div>
                @foreach($soft_skills as $s)
                    <span class="tag-like-link tag-like-link--s coach-specialties-item" data-v-8f2f8ad9=""
                          data-v-d93fe582="">{{$s}}</span>

                @endforeach

            </div>
        @endif
        @if($certifications)
            <div class="coach-specialties-group" data-v-8f2f8ad9="">
                <div class="coach-specialties-group-title" data-v-8f2f8ad9="">Chứng chỉ</div>
                @foreach($certifications as $c)
                    <span class="tag-like-link tag-like-link--s coach-specialties-item" data-v-8f2f8ad9=""
                          data-v-d93fe582="">{{$c}}</span>

                @endforeach

            </div>
        @endif
    </div>
</div>
