<?php

use function Livewire\Volt\{state};
state([
    'coach'
]);
?>

@php
    $statistics = $coach->getStatistics(true);
@endphp
<div class="coach-profile-card-container" data-v-287d27d2="" data-v-f24fc346="" data-v-b905ec3a="">
    <a href="{{route('profile',['slug' => Str::slug($coach->name),'c' => $coach->id])}}" wire:navigate class="link-wrapper" target="_blank"
       data-testid="coach-card-link" data-v-f24fc346="">
        <div class="coach-profile-card" data-v-f24fc346="">
            <div class="coach-profile-card__header" data-v-f24fc346="">
                <div class="coach-profile-card__avatar-container" data-v-f24fc346="">
                    <img
                        loading="lazy"
                        class="coach-profile-card__avatar"
                        height="72"
                        width="72"
                        alt="Photo of {{$coach->name}}"
                        src="{{Storage::url($coach->avatar)}}"
                        data-v-f24fc346=""
                    />
                </div>
                <div class="coach-profile-card__name-and-position" data-v-f24fc346="">
                    <div class="coach-profile-card__name" data-v-f24fc346="">{{$coach->name}}</div>
                    <div class="coach-profile-card__position" data-v-f24fc346="">
                        <div data-v-f24fc346="">Engineering manager</div>
                        <div data-v-f24fc346="">Software engineer</div>
                        <div data-v-f24fc346="">Chief of staff</div>
                        <div data-v-f24fc346="">+ 2 other</div>
                    </div>
                </div>
            </div>
            @if(@$coach->specialties['company'])
                <div class="coach-profile-card__info-row" data-v-f24fc346="">
                    <img loading="lazy" width="16" height="16" alt="Interview coaching company icon"
                         src="{{asset('icons/icon-company-black.svg')}}" data-v-f24fc346=""/><span
                        data-v-f24fc346="">{{$coach->specialties['company']}}</span>
                </div>
            @endif
            <div class="coach-profile-card__info-row" data-v-f24fc346="">
                <img loading="lazy" width="16" height="16" alt="Interview coaching company icon"
                     src="{{asset('icons/icon-clients-black.svg')}}" data-v-f24fc346=""/><span
                    data-v-f24fc346="">{{$statistics['total_sessions']}} clients</span>
            </div>
            <!---->
            <div class="coach-profile-card__info-row" data-v-f24fc346="">
                <img loading="lazy" width="16" height="16" alt="Positive Interview coaching reviews icon"
                     src="{{asset('icons/icon-positive-reviews.svg')}}" class="keep-color"
                     data-v-f24fc346=""/>
                @if(!empty($statistics['total_reviews']))
                    <span data-v-f24fc346="">{{$statistics['average_rating'] ?? 0}}&nbsp; </span>
                    <span data-v-f24fc346=""> ({{$statistics['total_reviews'] ?? 0}} reviews) </span>
                @else
                    <span data-v-f24fc346="">Mới</span>
                @endif

            </div>
        </div>
    </a>
</div>
