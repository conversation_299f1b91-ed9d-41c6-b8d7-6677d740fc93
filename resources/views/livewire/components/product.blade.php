<?php

use App\Models\Coache;
use App\Models\Company;
use App\Models\Role;
use App\Models\Service;
use App\Models\Specialized;
use function Livewire\Volt\{state, mount, computed,usesPagination, with};
usesPagination();
state([
    'services', 'roles',
    'company', 'specialized',
    'service_id', 'role_id',
    'company_id', 'specialized_id',
    'page','coaches',
    'date_range'
]);
mount(function () {
    $this->services = Service::query()->select('id', 'name')->orderBy('created_at', 'desc')->get();
    $this->roles = Role::query()->select('id', 'name')->orderBy('created_at', 'desc')->get();
    $this->company = Company::query()->select('id', 'name')->orderBy('created_at', 'desc')->get();
    $this->specialized = Specialized::query()->select('id', 'name')->orderBy('created_at', 'desc')->get();
    $this->page = 1;
    $this->coaches = collect();
});
with(fn () => [
    'coachesData' => Coache::query()
        ->when($this->service_id, function ($query) {
            $query->whereHas('services', function ($query) {
                $query->where('service_id', $this->service_id);
            });
        })
        ->when($this->role_id, function ($query) {
            $query->whereHas('roles', function ($query) {
                $query->where('role_id', $this->role_id);
            });
        })
        ->when($this->company_id, function ($query) {
            $query->whereHas('companies', function ($query) {
                $query->where('company_id', $this->company_id);
            });
        })
        ->when($this->specialized_id, function ($query) {
            $query->whereHas('specialized', function ($query) {
                $query->where('specialized_id', $this->specialized_id);
            });
        })
        ->where('status',Coache::STATUS_APPROVED)
        ->orderBy('created_at', 'desc')

        ->get()
]);
?>
<section class="section-wrapper coaches-section" id="coaches-section-anchor">
    <!--[-->
    <h2>{{count($coachesData)}} huấn luyện viên có sẵn ngay bây giờ</h2>
    <div class="coaches-filters-container">
        <div class="coach-filters-row" style="grid-template-columns: 1fr 1fr 1fr 1fr;">
            <div class="igao-dropdown igao-dropdown--size-m coaches-filter-dropdown"
                 data-testid="service-filter">
                <label class="igao-dropdown-label">Dịch vụ:</label>
                <select class="igao-dropdown-native" wire:model.live="service_id">
                    <option value="">Tất cả</option>
                    @foreach($services as $service)
                        <option value="{{$service->id}}">{{$service->name}}</option>
                    @endforeach
                </select>
            </div>
            <div class="igao-dropdown igao-dropdown--size-m igao-dropdown--with-search coaches-filter-dropdown"
                 data-testid="role-filter">
                <label class="igao-dropdown-label">Cấp bậc:</label>
                <select class="igao-dropdown-native" wire:model.live="role_id">
                    <option value="">Tất cả</option>
                    @foreach($roles as $role)
                        <option value="{{$role->id}}">{{$role->name}}</option>
                    @endforeach
                </select>
            </div>
            <div class="igao-dropdown igao-dropdown--size-m igao-dropdown--with-search coaches-filter-dropdown"
                 data-testid="company-filter">
                <label class="igao-dropdown-label">Lĩnh vực:</label>
                <select class="igao-dropdown-native" wire:model.live="company_id">
                    <option value="">Tất cả</option>
                    @foreach($company as $role)
                        <option value="{{$role->id}}">{{$role->name}}</option>
                    @endforeach
                </select>
            </div>
            <div
                x-data="{
                        dateRange: @entangle('date_range').live,
                        selectedTimeRange: '',
                        showTimeOptions: false,
                        timeOptions: [
                            '8h - 10h',
                            '10h - 14h',
                            '14h - 18h',
                            '18h - 20h'
                        ],
                        initFlatpickr() {
                            flatpickr(this.$refs.dateInput, {
                                mode: 'range',
                                minDate: 'today',
                                dateFormat: 'd-m-Y',
                                locale: 'vn',
                                onChange: (selectedDates, dateStr) => {
                                    this.dateRange = dateStr;
                                    this.showTimeOptions = true; // Hiện chọn giờ sau khi chọn ngày
                                }
                            });
                        },
                        selectTimeRange(time) {
                            this.selectedTimeRange = time;
                            this.showTimeOptions = false; // Ẩn dropdown giờ sau khi chọn
                            // Bạn có thể cập nhật biến entangle khác để gửi data về backend nếu cần
                        }
                    }"
                    x-init="initFlatpickr()"
                    class="igao-dropdown igao-dropdown--size-m igao-dropdown--with-search coaches-filter-dropdown"
                    style="position: relative;"
                >
                <label class="igao-dropdown-label">Lịch coach:</label>
                <input
                    type="text"
                    x-ref="dateInput"
                    x-model="dateRange"
                    class="igao-dropdown-native"
                    placeholder="Chọn khoảng ngày"
                    @focus="showTimeOptions = false"
                />

            </div>



        </div>
        <div class="coach-filters-row" style="grid-template-columns: 1fr 1fr 1fr 1fr;">
            <div class="igao-text-input igao-text-input--size-m igao-text-input--search coaches-filter-search-input"
                 data-testid="search-query-filter">
                <label class="igao-text-input-label">Tìm kiếm:</label>
                <div class="igao-text-input-wrapper">
                    <input class="coaches-filter-search-input"
                           data-testid="search-query-filter" type="text"
                           placeholder="Tìm kiếm theo từ khóa" value=""
                    />
                </div>
            </div>
            <!----><!--]--><!--]-->
        </div>
        <!--]-->
    </div>
    <div class="coach-cards-container" data-v-63aeaf57="" data-v-287d27d2="">
        <div class="coach-cards" data-v-287d27d2="">
            @foreach($coachesData as $coach)
                <livewire:components.coach :coach="$coach"/>
            @endforeach

        </div>
    </div>
{{--    <button class="btn btn-primary igao-btn igao-btn--l browse-all-coaches-btn" type="button" wire:click="loadMore" size="l" fullwidth="false"--}}
{{--            data-v-63aeaf57="" data-v-0dc3f13d="" data-v-55ea226e="">--}}
{{--        Tải thêm--}}
{{--    </button>--}}
</section>
