<?php

use App\Models\Coache;
use App\Models\Company;
use App\Models\Role;
use App\Models\Service;
use App\Models\Specialized;
use App\Models\CoachAvailability;
use App\Models\CoachingSession;
use Carbon\Carbon;
use function Livewire\Volt\{state, mount, computed,usesPagination, with};
usesPagination();
state([
    'services', 'roles',
    'company', 'specialized',
    'service_id', 'role_id',
    'company_id', 'specialized_id',
    'page','coaches',
    'date_range', 'start_time', 'end_time'
]);
mount(function () {
    $this->services = Service::query()->select('id', 'name')->orderBy('created_at', 'desc')->get();
    $this->roles = Role::query()->select('id', 'name')->orderBy('created_at', 'desc')->get();
    $this->company = Company::query()->select('id', 'name')->orderBy('created_at', 'desc')->get();
    $this->specialized = Specialized::query()->select('id', 'name')->orderBy('created_at', 'desc')->get();
    $this->page = 1;
    $this->start_time = '00:00';
    $this->end_time = '23:59';
});
with(fn () => [
    'coachesData' => $this->date_range ? $this->getAvailableCoachesInDateRange() : Coache::query()
        ->when($this->service_id, function ($query) {
            $query->whereHas('services', function ($query) {
                $query->where('service_id', $this->service_id);
            });
        })
        ->when($this->role_id, function ($query) {
            $query->whereHas('roles', function ($query) {
                $query->where('role_id', $this->role_id);
            });
        })
        ->when($this->company_id, function ($query) {
            $query->whereHas('companies', function ($query) {
                $query->where('company_id', $this->company_id);
            });
        })
        ->when($this->specialized_id, function ($query) {
            $query->whereHas('specialized', function ($query) {
                $query->where('specialized_id', $this->specialized_id);
            });
        })
        ->where('status',Coache::STATUS_APPROVED)
        ->orderBy('created_at', 'desc')
        ->get()
]);

$getAvailableCoachesInDateRange = function() {
    if (!$this->date_range) {
        return collect();
    }
    // Parse date range
    $dates = explode(' đến ', $this->date_range);
    if (count($dates) !== 2) {
        $dates = explode(' - ', $this->date_range);
    }

    if (count($dates) == 1) {
        $dates = [
            ...$dates,
            ...$dates
        ];
    }

    try {
        $startDate = Carbon::createFromFormat('d-m-Y', trim($dates[0]))->setTime(0,0,0);
        $endDate = Carbon::createFromFormat('d-m-Y', trim($dates[1]));
    } catch (Exception $e) {
        return collect();
    }
    // Get all approved coaches with filters
    $coachQuery = Coache::where('status', Coache::STATUS_APPROVED)
        ->when($this->service_id, function ($query) {
            $query->whereHas('services', function ($query) {
                $query->where('service_id', $this->service_id);
            });
        })
        ->when($this->role_id, function ($query) {
            $query->whereHas('roles', function ($query) {
                $query->where('role_id', $this->role_id);
            });
        })
        ->when($this->company_id, function ($query) {
            $query->whereHas('companies', function ($query) {
                $query->where('company_id', $this->company_id);
            });
        })
        ->when($this->specialized_id, function ($query) {
            $query->whereHas('specialized', function ($query) {
                $query->where('specialized_id', $this->specialized_id);
            });
        });
    $availableCoaches = collect();

    foreach ($coachQuery->get() as $coach) {
        if ($this->hasAvailabilityInDateRange($coach, $startDate, $endDate)) {
            $availableCoaches->push($coach);
        }
    }
    return $availableCoaches;
};

$hasAvailabilityInDateRange = function($coach, $startDate, $endDate) {
    $current = $startDate->copy();

    while ($current->lte($endDate)) {
        $dayOfWeek = $current->dayOfWeek;

        // Check if coach has availability for this day of week with time overlap
        $availability = CoachAvailability::where('coach_id', $coach->id)
            ->where('day_of_week', $dayOfWeek)
            ->where('is_available', true)
            ->where('start_time', '<=', $this->end_time)
            ->where('end_time', '>=', $this->start_time)
            ->first();

        if ($availability) {
            // Check if there are any conflicting sessions on this specific date within the time range
            $hasConflictingSessions = CoachingSession::where('coach_id', $coach->id)
                ->whereDate('start_time', $current->format('Y-m-d'))
                ->where(function ($query) {
                    $query->where(function ($q) {
                        $q->whereTime('start_time', '<', $this->end_time)
                          ->whereTime('end_time', '>', $this->start_time);
                    });
                })
                ->whereIn('status', [
                    CoachingSession::STATUS_SCHEDULED,
                    CoachingSession::STATUS_IN_PROGRESS
                ])
                ->exists();
            if (!$hasConflictingSessions) {
                return true;
            }
        }

        $current->addDay();
    }

    return false; // No available days found in the range
};

?>
<section class="section-wrapper coaches-section" id="coaches-section-anchor">
    <!--[-->
    <h2>
        {{count($coachesData)}} huấn luyện viên
        
    </h2>
    <div class="coaches-filters-container">
        <div class="coach-filters-row" style="grid-template-columns: 1fr 1fr 1fr 1fr;">
            <div class="igao-dropdown igao-dropdown--size-m coaches-filter-dropdown"
                 data-testid="service-filter">
                <label class="igao-dropdown-label">Dịch vụ:</label>
                <select class="igao-dropdown-native" wire:model.live="service_id">
                    <option value="">Tất cả</option>
                    @foreach($services as $service)
                        <option value="{{$service->id}}">{{$service->name}}</option>
                    @endforeach
                </select>
            </div>
            <div class="igao-dropdown igao-dropdown--size-m igao-dropdown--with-search coaches-filter-dropdown"
                 data-testid="role-filter">
                <label class="igao-dropdown-label">Cấp bậc:</label>
                <select class="igao-dropdown-native" wire:model.live="role_id">
                    <option value="">Tất cả</option>
                    @foreach($roles as $role)
                        <option value="{{$role->id}}">{{$role->name}}</option>
                    @endforeach
                </select>
            </div>
            <div class="igao-dropdown igao-dropdown--size-m igao-dropdown--with-search coaches-filter-dropdown"
                 data-testid="company-filter">
                <label class="igao-dropdown-label">Lĩnh vực:</label>
                <select class="igao-dropdown-native" wire:model.live="company_id">
                    <option value="">Tất cả</option>
                    @foreach($company as $role)
                        <option value="{{$role->id}}">{{$role->name}}</option>
                    @endforeach
                </select>
            </div>
            <div
                x-data="{
                        dateRange: @entangle('date_range').live,
                        startTime: @entangle('start_time').live,
                        endTime: @entangle('end_time').live,
                        showTimeOptions: false,
                        initFlatpickr() {
                            flatpickr(this.$refs.dateInput, {
                                mode: 'range',
                                minDate: 'today',
                                dateFormat: 'd-m-Y',
                                locale: 'vn',
                                onChange: (selectedDates, dateStr) => {
                                    this.dateRange = dateStr;
                                    this.showTimeOptions = true;
                                }
                            });
                        },
                        resetFilters() {
                            this.dateRange = '';
                            this.startTime = '00:00';
                            this.endTime = '23:59';
                            this.showTimeOptions = false;
                        }
                    }"
                    x-init="initFlatpickr()"
                    class="igao-dropdown igao-dropdown--size-m igao-dropdown--with-search coaches-filter-dropdown"
                    style="position: relative;"
                >
                <label class="igao-dropdown-label">Lịch coach:</label>
                <input
                    type="text"
                    x-ref="dateInput"
                    x-model="dateRange"
                    class="igao-dropdown-native"
                    placeholder="Chọn khoảng ngày"
                    @focus="showTimeOptions = false"
                />

                <!-- Time selection dropdown -->
                <div x-show="showTimeOptions" class="time-selection-dropdown" style="position: absolute; top: 100%; left: 0; right: 0; background: white; border: 1px solid #ddd; border-radius: 4px; padding: 15px; z-index: 1000; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                    <div style="margin-bottom: 10px;">
                        <label style="font-size: 14px; font-weight: 600; color: #333; display: block; margin-bottom: 8px;">Chọn khoảng thời gian:</label>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                        <div>
                            <label style="font-size: 12px; color: #666; display: block; margin-bottom: 4px;">Từ giờ:</label>
                            <select x-model="startTime" class="igao-dropdown-native" style="width: 100%; font-size: 14px;">
                                <option value="00:00">00:00</option>
                                <option value="01:00">01:00</option>
                                <option value="02:00">02:00</option>
                                <option value="03:00">03:00</option>
                                <option value="04:00">04:00</option>
                                <option value="05:00">05:00</option>
                                <option value="06:00">06:00</option>
                                <option value="07:00">07:00</option>
                                <option value="08:00">08:00</option>
                                <option value="09:00">09:00</option>
                                <option value="10:00">10:00</option>
                                <option value="11:00">11:00</option>
                                <option value="12:00">12:00</option>
                                <option value="13:00">13:00</option>
                                <option value="14:00">14:00</option>
                                <option value="15:00">15:00</option>
                                <option value="16:00">16:00</option>
                                <option value="17:00">17:00</option>
                                <option value="18:00">18:00</option>
                                <option value="19:00">19:00</option>
                                <option value="20:00">20:00</option>
                                <option value="21:00">21:00</option>
                                <option value="22:00">22:00</option>
                                <option value="23:00">23:00</option>
                            </select>
                        </div>
                        <div>
                            <label style="font-size: 12px; color: #666; display: block; margin-bottom: 4px;">Đến giờ:</label>
                            <select x-model="endTime" class="igao-dropdown-native" style="width: 100%; font-size: 14px;">
                                <option value="01:00">01:00</option>
                                <option value="02:00">02:00</option>
                                <option value="03:00">03:00</option>
                                <option value="04:00">04:00</option>
                                <option value="05:00">05:00</option>
                                <option value="06:00">06:00</option>
                                <option value="07:00">07:00</option>
                                <option value="08:00">08:00</option>
                                <option value="09:00">09:00</option>
                                <option value="10:00">10:00</option>
                                <option value="11:00">11:00</option>
                                <option value="12:00">12:00</option>
                                <option value="13:00">13:00</option>
                                <option value="14:00">14:00</option>
                                <option value="15:00">15:00</option>
                                <option value="16:00">16:00</option>
                                <option value="17:00">17:00</option>
                                <option value="18:00">18:00</option>
                                <option value="19:00">19:00</option>
                                <option value="20:00">20:00</option>
                                <option value="21:00">21:00</option>
                                <option value="22:00">22:00</option>
                                <option value="23:00">23:00</option>
                                <option value="23:59">23:59</option>
                            </select>
                        </div>
                    </div>
                    <div style="display: flex; gap: 8px;">
                        <button type="button" @click="showTimeOptions = false" class="btn btn-primary" style="flex: 1; padding: 8px 12px; background: #007bff; color: white; border: none; border-radius: 4px; font-size: 13px; font-weight: 500;">Tìm kiếm</button>
                        <button type="button" @click="resetFilters()" class="btn btn-secondary" style="flex: 1; padding: 8px 12px; background: #6c757d; color: white; border: none; border-radius: 4px; font-size: 13px; font-weight: 500;">Đặt lại</button>
                    </div>
                </div>

            </div>



        </div>
        <div class="coach-filters-row" style="grid-template-columns: 1fr 1fr 1fr 1fr;">
            <div class="igao-text-input igao-text-input--size-m igao-text-input--search coaches-filter-search-input"
                 data-testid="search-query-filter">
                <label class="igao-text-input-label">Tìm kiếm:</label>
                <div class="igao-text-input-wrapper">
                    <input class="coaches-filter-search-input"
                           data-testid="search-query-filter" type="text"
                           placeholder="Tìm kiếm theo từ khóa" value=""
                    />
                </div>
            </div>
            <!----><!--]--><!--]-->
        </div>
        <!--]-->
    </div>
    <div class="coach-cards-container" data-v-63aeaf57="" data-v-287d27d2="">
        <div class="coach-cards" data-v-287d27d2="">
            @foreach($coachesData as $coach)
                <livewire:components.coach :coach="$coach"/>
            @endforeach

        </div>
    </div>
{{--    <button class="btn btn-primary igao-btn igao-btn--l browse-all-coaches-btn" type="button" wire:click="loadMore" size="l" fullwidth="false"--}}
{{--            data-v-63aeaf57="" data-v-0dc3f13d="" data-v-55ea226e="">--}}
{{--        Tải thêm--}}
{{--    </button>--}}
</section>
