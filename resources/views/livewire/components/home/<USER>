<?php

use function Livewire\Volt\{state, mount};

state([
    'offers'
]);
mount(function () {
    $this->offers = \App\Models\Review::query()
        ->where('is_published', true)
        ->orderBy('created_at', 'desc')
        ->with('customer','coach')
        ->limit(9)
        ->get();
})
?>
<section class="section-wrapper" data-astro-cid-hc2l2ojz="true" data-v-c99ebf58="">
    <h2 class="social-proof-title" data-astro-cid-hc2l2ojz="">Họ đã nhận được lời đề nghị</h2>
    <div class="testimonials-container" data-v-499e8952="">
        <!--[-->
        @foreach($offers as $offer)
        <div data-v-499e8952="" data-v-e04ff970="">
            <div class="testimonial" data-v-e04ff970="">
                <div class="testimonial-header" data-v-e04ff970="">
                    <img
                        src="{{asset(Storage::url($offer->author_image) )}}"
                        alt="Amy's photo" width="24" height="24" data-v-e04ff970=""/> {{$offer->customer?->full_name}} đã nhận được lời đề nghị
                </div>
                <div class="testimonial-body" data-v-e04ff970="">
                    {{@$offer->content['q2'] ?? ''}}
                </div>
                <div class="testimonial-footer" data-v-e04ff970="">
                    Coached bởi
                    <!--[-->
                    <a href="{{route('profile',['slug' => Str::slug($offer->coach?->name),'c' => $offer->coach?->id])}}" target="_blank" data-v-e04ff970="">{{$offer->coach?->name}}</a>
                    <!----><!--]--><!----><!----><!---->
                </div>
            </div>
        </div>
        @endforeach
    </div>

</section>
