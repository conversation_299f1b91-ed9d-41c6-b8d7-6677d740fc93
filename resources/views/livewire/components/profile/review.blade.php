<?php

use App\Models\SessionReview;
use Livewire\WithPagination;
use Livewire\Volt\Component;

new class extends Component {
    use WithPagination;

    public $coach;
    public $keyword;
    public $service;
    public $sort_by;

    public function mount(): void
    {

    }

    public function with(): array
    {
        $coachId = $this->coach->id;
        $keyword = $this->keyword;
        return [
            'reviews' => SessionReview::query()->where('is_published', true)
                ->whereHas('coachingSession', function ($query) use ($coachId) {
                    $query->where('coach_id', $coachId);
                })
                ->when(is_null($this->sort_by), function ($query) {
                    $query->orderBy('created_at', 'desc');
                })
                ->when($this->sort_by == 2, function ($query) {
                    $query->orderBy('rating', 'desc');
                })
                ->when($this->sort_by == 3, function ($query) {
                    $query->orderBy('rating', 'asc');
                })
                ->when(!!$this->keyword, function ($query) use ($keyword) {
                    $query->whereLike('comment', "%{$keyword}%");
                })
                ->with(['customer'])
                ->paginate(10),
        ];
    }
}
?>


<div>
    <div class="coach-public-reviews-filters" data-js="coach-public-reviews-filters" data-v-25bcaaa8="">
        <div class="coach-public-reviews-filters-wrapper" data-v-25bcaaa8="">
            <div class="igao-text-input igao-text-input--size-m igao-text-input--search" data-v-25bcaaa8=""
                 data-v-a862fb29="">
                <label class="igao-text-input-label" data-v-a862fb29="">Tìm kiếm:</label>
                <div class="igao-text-input-wrapper" data-v-a862fb29="">
                    <input type="text" wire:model.live.debounce.200ms="keyword" placeholder="Tìm kiếm theo từ khóa"
                           data-v-a862fb29="">
                </div>
            </div>
            <div class="igao-dropdown igao-dropdown--size-m" data-v-25bcaaa8="" data-v-b5ca3b34="">
                <label class="igao-dropdown-label" data-v-b5ca3b34=""
                       id="igao-dropdown-label-ac0fea81-db09-4cdf-a7c0-fadcddfd503a">Dịch vụ:</label>
                <select class="igao-dropdown-native" wire:model.live="service">
                    <option value="">Tất cả</option>

                </select>
            </div>
            <div class="igao-dropdown igao-dropdown--size-m" data-v-25bcaaa8="" data-v-b5ca3b34="">
                <label class="igao-dropdown-label" data-v-b5ca3b34=""
                       id="igao-dropdown-label-0443f604-303b-40db-9dfa-c58f12a528c4">Sắp xếp theo:</label>
                <select class="igao-dropdown-native" wire:model.live="sort_by">
                    <option value="">Tất cả</option>
                    <option value="2">Tốt nhất</option>
                    <option value="">Gần đây</option>
                    <option value="3">Tệ nhất</option>

                </select>
            </div>
        </div>
    </div>
    @if($reviews->total())
        <div class="coach-public-reviews-body" data-js="coach-public-reviews-body" data-v-25bcaaa8="">
            @foreach($reviews as $rev)
                <div class="coach-public-reviews-review-container" data-v-25bcaaa8="" data-v-69a6089a="">
                    <div class="coach-public-reviews-review-header" data-v-69a6089a="">
                        <div class="coach-public-reviews-review-header-rating-and-date" data-v-69a6089a="">
                    <span data-v-69a6089a="">
                        @for($i = 0; $i < $rev->rating;$i++)
                            <span class="star" data-v-d33d28eb="">
                            <img src="{{asset('/icons/filled-star.svg')}}" width="12" height="12" alt="filled star"
                                 data-v-d33d28eb="">
                        </span>
                        @endfor
                    </span>
                            <span data-v-69a6089a="">{{$rev->created_at}}</span>
                        </div>
                        <div data-v-69a6089a="">
                    <span class="semi-bold" data-v-69a6089a="">
                        <span class="">{{$rev->customer?->full_name}}</span>
                    </span>
                            {{--                        <span class="">{{$rev->customer?->full_name}} · Engineering manager</span></span>--}}
                        </div>
                        <div class="italic" data-v-69a6089a="">
                            {{--                    <span class="">Interview coaching · People management and Leadership mock interview</span>--}}
                        </div>
                    </div>
                    <div class="coach-public-reviews-review-text" style="white-space:unset" data-v-69a6089a="">
                        <span class="">{{$rev->comment}}</span>
                    </div>
                </div>
            @endforeach
        </div>
        {{ $reviews->links('components.custom-pagination') }}
    @else
        <div data-v-25bcaaa8="" class="coach-public-reviews-empty"> Có vẻ như chúng tôi không tìm thấy bất kỳ đánh giá nào. <br
                data-v-25bcaaa8=""> Hãy thử thay đổi một số bộ lọc.
        </div>
    @endif


</div>
