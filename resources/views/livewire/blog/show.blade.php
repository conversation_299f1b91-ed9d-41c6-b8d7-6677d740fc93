<style>
    @font-face {
        font-family: "Open Sans";
        font-style: italic;
        font-weight: 400;
        font-stretch: 100%;
        font-display: swap;
        src: url(/fonts/open-sans-400-italic.woff2) format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD
    }

    @font-face {
        font-family: "Open Sans";
        font-style: normal;
        font-weight: 400;
        font-stretch: 100%;
        font-display: swap;
        src: url(/fonts/open-sans-400.woff2) format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD
    }

    @font-face {
        font-family: "Open Sans";
        font-style: normal;
        font-weight: 600;
        font-stretch: 100%;
        font-display: swap;
        src: url(/fonts/open-sans-600.woff2) format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD
    }

    @font-face {
        font-family: "Open Sans";
        font-style: normal;
        font-weight: 700;
        font-stretch: 100%;
        font-display: swap;
        src: url(/fonts/open-sans-700.woff2) format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD
    }

    body {
        font-size: 18px;
        line-height: 1.56;
        font-family: "Open Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
        font-weight: 400;
        color: #4a4a4a;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.004)
    }

    nav[data-astro-cid-nbh2emmn].articles-sub-navigation {
        width: 100%;
        border-bottom: 2px solid #eeeeee;
        height: 60px;
        padding: 0 64px;
        align-items: center;
        background-color: #fff;
        display: none
    }

    @media (min-width: 1200px) {
        nav[data-astro-cid-nbh2emmn].articles-sub-navigation {
            display: flex
        }
    }

    nav[data-astro-cid-nbh2emmn].articles-sub-navigation .nav-item, nav[data-astro-cid-nbh2emmn].articles-sub-navigation .menu-item {
        font-weight: 600;
        color: #4a4a4a
    }

    nav[data-astro-cid-nbh2emmn].articles-sub-navigation .nav-item.active, nav[data-astro-cid-nbh2emmn].articles-sub-navigation .menu-item.active {
        color: #0567a7
    }

    nav[data-astro-cid-nbh2emmn].articles-sub-navigation .nav-item:first-child {
        padding-left: 0
    }

    .navigation-search-input-container[data-astro-cid-nbh2emmn] {
        display: flex;
        flex-grow: 1;
        justify-content: flex-end
    }

    .mobile-search-input-container[data-astro-cid-nbh2emmn] {
        display: none;
        margin: 16px 0 0;
        padding: 0 16px;
        width: 100%
    }

    .mobile-search-input-container[data-astro-cid-nbh2emmn] .search-input {
        width: 100%;
        position: relative
    }

    @media (max-width: 960px) {
        .mobile-search-input-container[data-astro-cid-nbh2emmn] {
            display: block
        }
    }</style>
<link rel="stylesheet" href="{{asset('css/app/BlogArticle.D8CZDMOi.css')}}">
<style>
    .account-buttons[data-astro-cid-hmtt24ci] {
        display: flex;
        flex-direction: column;
        width: 100%
    }

    .account-buttons[data-astro-cid-hmtt24ci] a.btn:not(:last-child) {
        margin-bottom: 12px
    }

    .mobile-navigation-container[data-astro-cid-hmtt24ci] {
        display: none;
        position: fixed;
        top: 0;
        width: 100vw;
        left: 0;
        flex-direction: column;
        align-items: start;
        background: #ffffff;
        z-index: 400;
        padding: 86px 24px 156px;
        height: 100vh;
        overflow: auto
    }

    .mobile-navigation-container[data-astro-cid-hmtt24ci].open {
        display: flex
    }

    nav[data-astro-cid-2xhbaixd] {
        flex-grow: 1;
        display: flex
    }

    .fake-header[data-astro-cid-xbstl6g3] {
        display: flex;
        min-height: 70px;
        width: 100%
    }

    .mobile-sub-navigation[data-astro-cid-xbstl6g3] {
        height: 58px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fff;
        border-bottom: 2px solid #eeeeee;
        color: #4a4a4a
    }

    @media (min-width: 1200px) {
        .mobile-sub-navigation[data-astro-cid-xbstl6g3] {
            display: none
        }
    }

    .mobile-sub-navigation[data-astro-cid-xbstl6g3] a[data-astro-cid-xbstl6g3].navigation-link {
        position: relative;
        box-sizing: border-box;
        text-decoration: none;
        font-weight: 600;
        cursor: pointer;
        color: #000;
        white-space: nowrap;
        font-size: 14px
    }

    .mobile-sub-navigation[data-astro-cid-xbstl6g3] a[data-astro-cid-xbstl6g3].navigation-link.active {
        color: #0567a7
    }

    .mobile-sub-navigation[data-astro-cid-xbstl6g3] a[data-astro-cid-xbstl6g3].navigation-link:not(:last-child) {
        margin-right: 16px
    }

    header[data-astro-cid-xbstl6g3] {
        position: fixed;
        width: 100%;
        box-sizing: border-box;
        height: 70px;
        border-bottom: 6px solid #eeeeee;
        background-color: #ffffff;
        padding: 10px 24px;
        display: flex;
        z-index: 500
    }

    @media (min-width: 1200px) {
        header[data-astro-cid-xbstl6g3] {
            padding: 10px 64px
        }
    }

    header[data-astro-cid-xbstl6g3] .header-container[data-astro-cid-xbstl6g3] {
        display: flex;
        width: 100%;
        align-items: center;
        margin: auto
    }

    header[data-astro-cid-xbstl6g3] .logo[data-astro-cid-xbstl6g3] {
        display: flex
    }

    header[data-astro-cid-xbstl6g3] .logo[data-astro-cid-xbstl6g3] > a[data-astro-cid-xbstl6g3] {
        display: flex
    }

    .search-input[data-v-3d295d3e] {
        font-size: 16px;
        line-height: 1.5;
        width: 200px;
        background-color: #fff;
        background-image: url(/icons/icon-search.svg);
        background-position: 12px center;
        background-repeat: no-repeat;
        background-size: 14px 14px;
        padding: 0 0 0 39px;
        border: solid 2px #eeeeee;
        cursor: pointer;
        height: 32px;
        position: relative;
        transition: width 400ms ease-in-out, margin-left 400ms ease-in-out;
        margin-bottom: 0;
        border-radius: 0;
    }

    .search-input[data-v-3d295d3e]:focus {
        outline: 1px solid #0567a7;
    }

    .spinner-container[data-v-15ddf44a] {
        text-align: center;
        position: relative;
        min-height: 132px;
        height: 100%;
        padding: 16px 0;
    }

    .spinner-container .loading-coaches-spinner[data-v-15ddf44a] {
        position: absolute;
        top: calc(50% - 50px);
        left: calc(50% - 50px);
        color: #0567a7;
        width: var(--spinner-size, 100px);
        height: var(--spinner-size, 100px);
        border-width: 4px;
    }

    .igao-btn.igao-btn--full-width[data-v-0dc3f13d] {
        width: 100%;
    }

    .igao-btn.igao-btn--l[data-v-0dc3f13d] {
        height: 50px;
        padding: 15px 32px;
    }

    .igao-btn.igao-btn--xl[data-v-0dc3f13d] {
        height: 54px;
        padding: 17px 32px;
    }

    .igao-btn.igao-btn--m[data-v-0dc3f13d] {
        height: 40px;
        padding: 10px 20px;
    }

    .igao-btn.igao-btn--s[data-v-0dc3f13d] {
        height: 32px;
        padding: 6px 16px;
    }

    .search-fetching-spinner[data-v-3a59aeef] {
        margin: 120px 0;
    }

    .load-more-button.btn[data-v-3a59aeef] {
        margin: 48px auto 0;
        display: flex;
    }

    @media (max-width: 960px) {
        .load-more-button.btn[data-v-3a59aeef] {
            margin-top: 24px;
        }
    }

    .articles-wrapper[data-v-3a59aeef] {
        max-width: 972px;
        margin: auto;
        padding: 56px 16px 0;
    }

    @media (max-width: 960px) {
        .articles-wrapper[data-v-3a59aeef] {
            padding-top: 32px;
        }
    }

    .articles-wrapper .page-header-container[data-v-3a59aeef] {
        margin-bottom: 32px;
    }

    .articles-wrapper .search-results[data-v-3a59aeef] {
        display: inline-block;
        margin-top: 8px;
    }

    .articles-wrapper .search-query[data-v-3a59aeef] {
        font-weight: bold;
    }

    .articles-grid[data-v-3a59aeef] {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }

    @media (max-width: 960px) {
        .articles-grid[data-v-3a59aeef] {
            grid-template-columns: 1fr;
        }
    }

    .btn[data-v-55ea226e] {
        font-size: 12px;
        line-height: 1.67;
        border-radius: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #0567a7;
        position: relative;
        padding: 6px 15px;
        font-weight: bold;
        text-transform: uppercase;
        border: none;
        white-space: nowrap;
    }

    .btn.btn-primary[data-v-55ea226e] {
        color: #ffffff;
        background-color: #0567a7;
    }

    .btn.btn-primary[data-v-55ea226e]:hover {
        background-color: #045a8d;
        border-color: #045a8d;
    }

    .btn.btn-primary--green[data-v-55ea226e] {
        background-color: #15835c;
        border-color: #15835c;
    }

    .btn.btn-primary--green[data-v-55ea226e]:hover {
        background-color: #147642;
        border-color: #147642;
    }

    .btn.btn-disabled[data-v-55ea226e] {
        opacity: 0.65;
        pointer-events: none;
    }

    .btn.btn-link[data-v-55ea226e] {
        display: inline-block;
        height: auto;
        background: none;
        border: none;
        color: #0567a7;
        padding: 0;
        font-weight: 600;
    }

    .btn.btn-secondary[data-v-55ea226e] {
        background-color: #ffffff;
        border: solid 2px #eeeeee;
    }

    .btn.btn-secondary--gray[data-v-55ea226e] {
        border-color: #eeeeee;
        background-color: #eeeeee;
        color: #333333;
    }

    .btn.btn-secondary--gray[data-v-55ea226e]:hover {
        background-color: #d5d4d4;
        border-color: #d5d4d4;
    }

    .btn.btn-secondary--black[data-v-55ea226e] {
        border: solid 2px #eeeeee;
        background-color: #fff;
        color: #333;
    }

    .btn.btn-secondary[data-v-55ea226e]:hover {
        background-color: #ececec;
        border-color: #e6e6e6;
        color: #212529;
    }

    .btn.btn-danger[data-v-55ea226e] {
        color: #fff;
        background-color: #e8422f;
    }

    .btn.btn-danger[data-v-55ea226e]:hover {
        color: #fff;
    }

    .btn.btn-no-transform[data-v-55ea226e] {
        text-transform: none;
    }

    .article-card[data-v-88303a3c] {
        display: flex;
        flex-direction: column;
        transition: opacity 0.4s;
    }

    .article-card[data-v-88303a3c]:hover {
        opacity: 0.85;
    }

    .article-card:hover .article-card-icon-right[data-v-88303a3c] {
        transform: translateX(7px);
    }

    .article-card img[data-v-88303a3c] {
        width: 100%;
        height: auto;
    }

    .article-card h5[data-v-88303a3c] {
        font-weight: bold;
        margin: 8px 0;
    }

    .article-card .article-card-body[data-v-88303a3c] {
        padding: 24px 32px;
        background-color: #fff;
    }

    .article-card .article-card-tag[data-v-88303a3c] {
        font-weight: bold;
        color: #9e6a1b;
        margin-right: 16px;
    }

    .article-card .article-card-date[data-v-88303a3c] {
        font-size: 12px;
        font-weight: 600;
        color: #aaaaaa;
    }

    .article-card .article-card-header[data-v-88303a3c] {
        font-size: 12px;
        line-height: 1.67;
        text-transform: uppercase;
    }

    .article-card .article-card-title[data-v-88303a3c] {
        font-size: 20px;
        line-height: 1.5;
        font-weight: bold;
        color: #333333;
    }

    .article-card .article-card-excerpt[data-v-88303a3c] {
        font-size: 16px;
        line-height: 1.5;
        color: #4a4a4a;
        margin-bottom: 24px;
    }

    .article-card .article-card-read-more[data-v-88303a3c] {
        font-size: 12px;
        line-height: 1.67;
        font-weight: bold;
        text-transform: uppercase;
        color: #0567a7;
    }

    .article-card .article-card-icon-right[data-v-88303a3c] {
        transition: transform 0.4s;
        margin-left: 8px;
    }

    .article-card .article-card-icon-right path[data-v-88303a3c] {
        fill: #0567a7;
    }

    .igao-spinner[data-v-9d02a4c1] {
        width: 16px;
        height: 16px;
        border-width: 0.2em;
        display: inline-block;
        vertical-align: text-bottom;
        border: 2px solid currentColor;
        border-right-color: transparent;
        border-radius: 50%;
        animation: spinner-border-9d02a4c1 0.75s linear infinite;
    }

    @keyframes spinner-border-9d02a4c1 {
        to {
            transform: rotate(360deg);
        }
    }

    .btn[data-v-55ea226e] {
        font-size: 12px;
        line-height: 1.67;
        border-radius: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #0567a7;
        position: relative;
        padding: 6px 15px;
        font-weight: bold;
        text-transform: uppercase;
        border: none;
        white-space: nowrap;
    }

    .btn.btn-primary[data-v-55ea226e] {
        color: #ffffff;
        background-color: #0567a7;
    }

    .btn.btn-primary[data-v-55ea226e]:hover {
        background-color: #045a8d;
        border-color: #045a8d;
    }

    .btn.btn-primary--green[data-v-55ea226e] {
        background-color: #15835c;
        border-color: #15835c;
    }

    .btn.btn-primary--green[data-v-55ea226e]:hover {
        background-color: #147642;
        border-color: #147642;
    }

    .btn.btn-disabled[data-v-55ea226e] {
        opacity: 0.65;
        pointer-events: none;
    }

    .btn.btn-link[data-v-55ea226e] {
        display: inline-block;
        height: auto;
        background: none;
        border: none;
        color: #0567a7;
        padding: 0;
        font-weight: 600;
    }

    .btn.btn-secondary[data-v-55ea226e] {
        background-color: #ffffff;
        border: solid 2px #eeeeee;
    }

    .btn.btn-secondary--gray[data-v-55ea226e] {
        border-color: #eeeeee;
        background-color: #eeeeee;
        color: #333333;
    }

    .btn.btn-secondary--gray[data-v-55ea226e]:hover {
        background-color: #d5d4d4;
        border-color: #d5d4d4;
    }

    .btn.btn-secondary--black[data-v-55ea226e] {
        border: solid 2px #eeeeee;
        background-color: #fff;
        color: #333;
    }

    .btn.btn-secondary[data-v-55ea226e]:hover {
        background-color: #ececec;
        border-color: #e6e6e6;
        color: #212529;
    }

    .btn.btn-danger[data-v-55ea226e] {
        color: #fff;
        background-color: #e8422f;
    }

    .btn.btn-danger[data-v-55ea226e]:hover {
        color: #fff;
    }

    .btn.btn-no-transform[data-v-55ea226e] {
        text-transform: none;
    }

    .igao-spinner[data-v-9d02a4c1] {
        width: 16px;
        height: 16px;
        border-width: 0.2em;
        display: inline-block;
        vertical-align: text-bottom;
        border: 2px solid currentColor;
        border-right-color: transparent;
        border-radius: 50%;
        animation: spinner-border-9d02a4c1 0.75s linear infinite;
    }

    @keyframes spinner-border-9d02a4c1 {
        to {
            transform: rotate(360deg);
        }
    }

    .igao-btn.igao-btn--full-width[data-v-0dc3f13d] {
        width: 100%;
    }

    .igao-btn.igao-btn--l[data-v-0dc3f13d] {
        height: 50px;
        padding: 15px 32px;
    }

    .igao-btn.igao-btn--xl[data-v-0dc3f13d] {
        height: 54px;
        padding: 17px 32px;
    }

    .igao-btn.igao-btn--m[data-v-0dc3f13d] {
        height: 40px;
        padding: 10px 20px;
    }

    .igao-btn.igao-btn--s[data-v-0dc3f13d] {
        height: 32px;
        padding: 6px 16px;
    }

    .header-account-container[data-v-5d2dab64] {
        display: flex;
        align-items: center;
    }

    .account-buttons[data-v-5d2dab64] {
        display: none;
    }

    @media (min-width: 1200px) {
        .account-buttons[data-v-5d2dab64] {
            display: block;
        }
    }

    .account-buttons[data-v-5d2dab64] > a {
        margin-right: 32px;
    }

    .account-buttons[data-v-5d2dab64] > a:last-of-type {
        margin-right: 18px;
    }

    .mobile-menu-icon[data-v-5d2dab64] {
        width: 44px;
        height: 44px;
        align-items: center;
        justify-content: center;
        border: none;
        background: no-repeat;
        display: flex;
    }

    @media (min-width: 1200px) {
        .mobile-menu-icon[data-v-5d2dab64] {
            display: none;
        }
    }

    .mobile-menu-icon.cart-icon[data-v-5d2dab64] {
        display: flex;
    }

    .account-buttons[data-v-f9a5b2be] .btn.btn {
        font-size: 16px;
        line-height: 1.5;
        border: none;
        font-weight: bold;
        padding: 3px 12px;
        height: auto;
    }

    .account-buttons[data-v-f9a5b2be] .btn.link-style {
        border: 0;
        background: none;
        color: #242424;
        padding-left: 0;
        padding-right: 0;
    }

    .account-buttons[data-v-f9a5b2be] .btn.link-style:hover {
        opacity: 0.7;
        background: none;
    }

    .account-buttons[data-v-f9a5b2be] .btn.link-style.become-coach {
        color: #666666;
    }
</style>
<div style="margin-top: 70px;">
    <livewire:layout.blog_nav :categoryId="$post->category->id"/>

    <div class="article-container" data-astro-cid-xnubymra="">
        <div class="article-holder" data-astro-cid-xnubymra="">
            <div class="article-breadcrumbs" data-astro-cid-xnubymra="">
                <a data-astro-cid-xnubymra="true"
                   class="igao-link igao-link--secondary" href="{{route('blog.index')}}" wire:navigate
                   data-v-bb39e5c0="">
                    Blog
                </a>
                <span data-astro-cid-xnubymra="">&gt;</span>
                <a data-astro-cid-xnubymra="true"
                   class="igao-link igao-link--secondary" wire:navigate
                   href="{{route('blog.category',['slug' => $post->category->slug])}}"
                   data-v-bb39e5c0="">
                    {{$post->category->name}}
                </a>
            </div>
            <h1 data-astro-cid-xnubymra="">{{$post->title}}</h1>
            <div class="article-header-line" data-astro-cid-xnubymra="">
                <span data-astro-cid-xnubymra="">{{$post->created_at->format('M d, Y')}}</span>
            </div>
            <img class="article-main-image" width="660" height="360" alt="{{$post->title}}"
                 src="{{Storage::url($post->featured_image)}}"
                 data-astro-cid-xnubymra="">
            <div class="article-content" data-astro-cid-xnubymra="">
                {!! $post->content !!}
            </div>
        </div>
        <div class="article-related-articles" data-astro-cid-xnubymra="">
            <h2 data-astro-cid-xnubymra="">Related articles:</h2>
            <div class="article-related-articles-grid" data-astro-cid-xnubymra="">
                @foreach($relatedPosts as $relatedPost)

                    <a href="{{route('blog.show',['slug' => $relatedPost->slug])}}" wire:navigate class="article-card"
                       data-astro-cid-xnubymra="true" data-v-88303a3c="">
                        <img
                            src="{{Storage::url($relatedPost->featured_image)}}"
                            alt="{{$relatedPost->title}}" width="460" height="230" data-v-88303a3c="">
                        <div class="article-card-body" data-v-88303a3c="">

                            <div class="article-card-header" data-v-88303a3c="">
                                <span class="article-card-tag"
                                      data-v-88303a3c="">{{$relatedPost->category->name}}</span>
                                <span
                                    class="article-card-date"
                                    data-v-88303a3c="">{{$relatedPost->created_at->format('M d, Y')}}</span>
                            </div>
                            <div class="article-card-title" data-v-88303a3c="">
                                {{$relatedPost->title}}
                            </div>
                            <div class="article-card-excerpt" data-v-88303a3c="">
                                {{$relatedPost->excerpt}}
                            </div>
                            <span
                                class="article-card-read-more" data-v-88303a3c=""> Read more
                                <svg focusable="false"
                                     role="presentation"
                                     class="article-card-icon-right"
                                     viewBox="0 0 4 7"
                                     width="7" height="7"
                                     data-v-88303a3c="">
                                 <path fill="#444" d="M4 3.5L0 7V0z" data-v-88303a3c=""></path>
                                </svg>
                            </span>
                        </div>
                    </a>
                @endforeach
            </div>
        </div>


    </div>
</div>
