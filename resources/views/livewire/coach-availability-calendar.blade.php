


<style type="text/css">
    /*!
 * FullCalendar v3.9.0
 * Docs & License: https://fullcalendar.io/
 * (c) 2018 <PERSON>
 */
    .fc {
        direction: ltr;
        text-align: left
    }

    .fc-rtl {
        text-align: right
    }

    body .fc {
        font-size: 1em
    }

    .fc-highlight {
        background: #bce8f1;
        opacity: .3
    }

    .fc-bgevent {
        background: #8fdf82;
        opacity: .3
    }

    .fc-nonbusiness {
        background: #d7d7d7
    }

    .fc button {
        box-sizing: border-box;
        margin: 0;
        height: 2.1em;
        padding: 0 .6em;
        font-size: 1em;
        white-space: nowrap;
        cursor: pointer
    }

    .fc button::-moz-focus-inner {
        margin: 0;
        padding: 0
    }

    .fc-state-default {
        border: 1px solid
    }

    .fc-state-default.fc-corner-left {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px
    }

    .fc-state-default.fc-corner-right {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px
    }

    .fc button .fc-icon {
        position: relative;
        top: -.05em;
        margin: 0 .2em;
        vertical-align: middle
    }

    .fc-state-default {
        background-color: #f5f5f5;
        background-image: linear-gradient(180deg, #fff, #e6e6e6);
        background-repeat: repeat-x;
        border-color: #e6e6e6 #e6e6e6 #bfbfbf;
        border-color: rgba(0, 0, 0, .1) rgba(0, 0, 0, .1) rgba(0, 0, 0, .25);
        color: #333;
        text-shadow: 0 1px 1px hsla(0, 0%, 100%, .75);
        box-shadow: inset 0 1px 0 hsla(0, 0%, 100%, .2), 0 1px 2px rgba(0, 0, 0, .05)
    }

    .fc-state-active, .fc-state-disabled, .fc-state-down, .fc-state-hover {
        color: #333;
        background-color: #e6e6e6
    }

    .fc-state-hover {
        color: #333;
        text-decoration: none;
        background-position: 0 -15px;
        transition: background-position .1s linear
    }

    .fc-state-active, .fc-state-down {
        background-color: #ccc;
        background-image: none;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, .15), 0 1px 2px rgba(0, 0, 0, .05)
    }

    .fc-state-disabled {
        cursor: default;
        background-image: none;
        opacity: .65;
        box-shadow: none
    }

    .fc-button-group {
        display: inline-block
    }

    .fc .fc-button-group > * {
        float: left;
        margin: 0 0 0 -1px
    }

    .fc .fc-button-group > :first-child {
        margin-left: 0
    }

    .fc-popover {
        position: absolute;
        box-shadow: 0 2px 6px rgba(0, 0, 0, .15)
    }

    .fc-popover .fc-header {
        padding: 2px 4px
    }

    .fc-popover .fc-header .fc-title {
        margin: 0 2px
    }

    .fc-popover .fc-header .fc-close {
        cursor: pointer
    }

    .fc-ltr .fc-popover .fc-header .fc-title, .fc-rtl .fc-popover .fc-header .fc-close {
        float: left
    }

    .fc-ltr .fc-popover .fc-header .fc-close, .fc-rtl .fc-popover .fc-header .fc-title {
        float: right
    }

    .fc-divider {
        border-style: solid;
        border-width: 1px
    }

    hr.fc-divider {
        height: 0;
        margin: 0;
        padding: 0 0 2px;
        border-width: 1px 0
    }

    .fc-clear {
        clear: both
    }

    .fc-bg, .fc-bgevent-skeleton, .fc-helper-skeleton, .fc-highlight-skeleton {
        position: absolute;
        top: 0;
        left: 0;
        right: 0
    }

    .fc-bg {
        bottom: 0
    }

    .fc-bg table {
        height: 100%
    }

    .fc table {
        width: 100%;
        box-sizing: border-box;
        table-layout: fixed;
        border-collapse: collapse;
        border-spacing: 0;
        font-size: 1em
    }

    .fc th {
        text-align: center
    }

    .fc td, .fc th {
        border-style: solid;
        border-width: 1px;
        padding: 0;
        vertical-align: top
    }

    .fc td.fc-today {
        border-style: double
    }

    a[data-goto] {
        cursor: pointer
    }

    a[data-goto]:hover {
        text-decoration: underline
    }

    .fc .fc-row {
        border-style: solid;
        border-width: 0
    }

    .fc-row table {
        border-left: 0 hidden transparent;
        border-right: 0 hidden transparent;
        border-bottom: 0 hidden transparent
    }

    .fc-row:first-child table {
        border-top: 0 hidden transparent
    }

    .fc-row {
        position: relative
    }

    .fc-row .fc-bg {
        z-index: 1
    }

    .fc-row .fc-bgevent-skeleton, .fc-row .fc-highlight-skeleton {
        bottom: 0
    }

    .fc-row .fc-bgevent-skeleton table, .fc-row .fc-highlight-skeleton table {
        height: 100%
    }

    .fc-row .fc-bgevent-skeleton td, .fc-row .fc-highlight-skeleton td {
        border-color: transparent
    }

    .fc-row .fc-bgevent-skeleton {
        z-index: 2
    }

    .fc-row .fc-highlight-skeleton {
        z-index: 3
    }

    .fc-row .fc-content-skeleton {
        position: relative;
        z-index: 4;
        padding-bottom: 2px
    }

    .fc-row .fc-helper-skeleton {
        z-index: 5
    }

    .fc .fc-row .fc-content-skeleton table, .fc .fc-row .fc-content-skeleton td, .fc .fc-row .fc-helper-skeleton td {
        background: none;
        border-color: transparent
    }

    .fc-row .fc-content-skeleton td, .fc-row .fc-helper-skeleton td {
        border-bottom: 0
    }

    .fc-row .fc-content-skeleton tbody td, .fc-row .fc-helper-skeleton tbody td {
        border-top: 0
    }

    .fc-scroller {
        -webkit-overflow-scrolling: touch
    }

    .fc-scroller > .fc-day-grid, .fc-scroller > .fc-time-grid {
        position: relative;
        width: 100%
    }

    .fc-event {
        position: relative;
        display: block;
        font-size: .85em;
        line-height: 1.3;
        border-radius: 3px;
        border: 1px solid #3a87ad
    }

    .fc-event, .fc-event-dot {
        background-color: #3a87ad
    }

    .fc-event, .fc-event:hover {
        color: #fff;
        text-decoration: none
    }

    .fc-event.fc-draggable, .fc-event[href] {
        cursor: pointer
    }

    .fc-not-allowed, .fc-not-allowed .fc-event {
        cursor: not-allowed
    }

    .fc-event .fc-bg {
        z-index: 1;
        background: #fff;
        opacity: .25
    }

    .fc-event .fc-content {
        position: relative;
        z-index: 2
    }

    .fc-event .fc-resizer {
        position: absolute;
        z-index: 4;
        display: none
    }

    .fc-event.fc-allow-mouse-resize .fc-resizer, .fc-event.fc-selected .fc-resizer {
        display: block
    }

    .fc-event.fc-selected .fc-resizer:before {
        content: "";
        position: absolute;
        z-index: 9999;
        top: 50%;
        left: 50%;
        width: 40px;
        height: 40px;
        margin-left: -20px;
        margin-top: -20px
    }

    .fc-event.fc-selected {
        z-index: 9999 !important;
        box-shadow: 0 2px 5px rgba(0, 0, 0, .2)
    }

    .fc-event.fc-selected.fc-dragging {
        box-shadow: 0 2px 7px rgba(0, 0, 0, .3)
    }

    .fc-h-event.fc-selected:before {
        content: "";
        position: absolute;
        z-index: 3;
        top: -10px;
        bottom: -10px;
        left: 0;
        right: 0
    }

    .fc-ltr .fc-h-event.fc-not-start, .fc-rtl .fc-h-event.fc-not-end {
        margin-left: 0;
        border-left-width: 0;
        padding-left: 1px;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0
    }

    .fc-ltr .fc-h-event.fc-not-end, .fc-rtl .fc-h-event.fc-not-start {
        margin-right: 0;
        border-right-width: 0;
        padding-right: 1px;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0
    }

    .fc-ltr .fc-h-event .fc-start-resizer, .fc-rtl .fc-h-event .fc-end-resizer {
        cursor: w-resize;
        left: -1px
    }

    .fc-ltr .fc-h-event .fc-end-resizer, .fc-rtl .fc-h-event .fc-start-resizer {
        cursor: e-resize;
        right: -1px
    }

    .fc-h-event.fc-allow-mouse-resize .fc-resizer {
        width: 7px;
        top: -1px;
        bottom: -1px
    }

    .fc-h-event.fc-selected .fc-resizer {
        border-radius: 4px;
        border-width: 1px;
        width: 6px;
        height: 6px;
        border-style: solid;
        border-color: inherit;
        background: #fff;
        top: 50%;
        margin-top: -4px
    }

    .fc-ltr .fc-h-event.fc-selected .fc-start-resizer, .fc-rtl .fc-h-event.fc-selected .fc-end-resizer {
        margin-left: -4px
    }

    .fc-ltr .fc-h-event.fc-selected .fc-end-resizer, .fc-rtl .fc-h-event.fc-selected .fc-start-resizer {
        margin-right: -4px
    }

    .fc-day-grid-event {
        margin: 1px 2px 0;
        padding: 0 1px
    }

    tr:first-child > td > .fc-day-grid-event {
        margin-top: 2px
    }

    .fc-day-grid-event.fc-selected:after {
        content: "";
        position: absolute;
        z-index: 1;
        top: -1px;
        right: -1px;
        bottom: -1px;
        left: -1px;
        background: #000;
        opacity: .25
    }

    .fc-day-grid-event .fc-content {
        white-space: nowrap;
        overflow: hidden
    }

    .fc-day-grid-event .fc-time {
        font-weight: 700
    }

    .fc-ltr .fc-day-grid-event.fc-allow-mouse-resize .fc-start-resizer, .fc-rtl .fc-day-grid-event.fc-allow-mouse-resize .fc-end-resizer {
        margin-left: -2px
    }

    .fc-ltr .fc-day-grid-event.fc-allow-mouse-resize .fc-end-resizer, .fc-rtl .fc-day-grid-event.fc-allow-mouse-resize .fc-start-resizer {
        margin-right: -2px
    }

    a.fc-more {
        margin: 1px 3px;
        font-size: .85em;
        cursor: pointer;
        text-decoration: none
    }

    a.fc-more:hover {
        text-decoration: underline
    }

    .fc-limited {
        display: none
    }

    .fc-day-grid .fc-row {
        z-index: 1
    }

    .fc-more-popover {
        z-index: 2;
        width: 220px
    }

    .fc-more-popover .fc-event-container {
        padding: 10px
    }

    .fc-now-indicator {
        position: absolute;
        border: 0 solid red
    }

    .fc-unselectable {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        -webkit-touch-callout: none;
        -webkit-tap-highlight-color: transparent
    }

    .fc-unthemed .fc-content, .fc-unthemed .fc-divider, .fc-unthemed .fc-list-heading td, .fc-unthemed .fc-list-view, .fc-unthemed .fc-popover, .fc-unthemed .fc-row, .fc-unthemed tbody, .fc-unthemed td, .fc-unthemed th, .fc-unthemed thead {
        border-color: #ddd
    }

    .fc-unthemed .fc-popover {
        background-color: #fff
    }

    .fc-unthemed .fc-divider, .fc-unthemed .fc-list-heading td, .fc-unthemed .fc-popover .fc-header {
        background: #eee
    }

    .fc-unthemed .fc-popover .fc-header .fc-close {
        color: #666
    }

    .fc-unthemed td.fc-today {
        background: #fcf8e3
    }

    .fc-unthemed .fc-disabled-day {
        background: #d7d7d7;
        opacity: .3
    }

    .fc-icon {
        display: inline-block;
        height: 1em;
        line-height: 1em;
        font-size: 1em;
        text-align: center;
        overflow: hidden;
        font-family: Courier New, Courier, monospace;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none
    }

    .fc-icon:after {
        position: relative
    }

    .fc-icon-left-single-arrow:after {
        content: "\2039";
        font-weight: 700;
        font-size: 200%;
        top: -7%
    }

    .fc-icon-right-single-arrow:after {
        content: "\203A";
        font-weight: 700;
        font-size: 200%;
        top: -7%
    }

    .fc-icon-left-double-arrow:after {
        content: "\AB";
        font-size: 160%;
        top: -7%
    }

    .fc-icon-right-double-arrow:after {
        content: "\BB";
        font-size: 160%;
        top: -7%
    }

    .fc-icon-left-triangle:after {
        content: "\25C4";
        font-size: 125%;
        top: 3%
    }

    .fc-icon-right-triangle:after {
        content: "\25BA";
        font-size: 125%;
        top: 3%
    }

    .fc-icon-down-triangle:after {
        content: "\25BC";
        font-size: 125%;
        top: 2%
    }

    .fc-icon-x:after {
        content: "\D7";
        font-size: 200%;
        top: 6%
    }

    .fc-unthemed .fc-popover {
        border-width: 1px;
        border-style: solid
    }

    .fc-unthemed .fc-popover .fc-header .fc-close {
        font-size: .9em;
        margin-top: 2px
    }

    .fc-unthemed .fc-list-item:hover td {
        background-color: #f5f5f5
    }

    .ui-widget .fc-disabled-day {
        background-image: none
    }

    .fc-popover > .ui-widget-header + .ui-widget-content {
        border-top: 0
    }

    .ui-widget .fc-event {
        color: #fff;
        text-decoration: none;
        font-weight: 400
    }

    .ui-widget td.fc-axis {
        font-weight: 400
    }

    .fc-time-grid .fc-slats .ui-widget-content {
        background: none
    }

    .fc.fc-bootstrap3 a {
        text-decoration: none
    }

    .fc.fc-bootstrap3 a[data-goto]:hover {
        text-decoration: underline
    }

    .fc-bootstrap3 hr.fc-divider {
        border-color: inherit
    }

    .fc-bootstrap3 .fc-today.alert {
        border-radius: 0
    }

    .fc-bootstrap3 .fc-popover .panel-body {
        padding: 0
    }

    .fc-bootstrap3 .fc-time-grid .fc-slats table {
        background: none
    }

    .fc.fc-bootstrap4 a {
        text-decoration: none
    }

    .fc.fc-bootstrap4 a[data-goto]:hover {
        text-decoration: underline
    }

    .fc-bootstrap4 hr.fc-divider {
        border-color: inherit
    }

    .fc-bootstrap4 .fc-today.alert {
        border-radius: 0
    }

    .fc-bootstrap4 a.fc-event:not([href]):not([tabindex]) {
        color: #fff
    }

    .fc-bootstrap4 .fc-popover.card {
        position: absolute
    }

    .fc-bootstrap4 .fc-popover .card-body {
        padding: 0
    }

    .fc-bootstrap4 .fc-time-grid .fc-slats table {
        background: none
    }

    .fc-toolbar {
        text-align: center
    }

    .fc-toolbar.fc-header-toolbar {
        margin-bottom: 1em
    }

    .fc-toolbar.fc-footer-toolbar {
        margin-top: 1em
    }

    .fc-toolbar .fc-left {
        float: left
    }

    .fc-toolbar .fc-right {
        float: right
    }

    .fc-toolbar .fc-center {
        display: inline-block
    }

    .fc .fc-toolbar > * > * {
        float: left;
        margin-left: .75em
    }

    .fc .fc-toolbar > * > :first-child {
        margin-left: 0
    }

    .fc-toolbar h2 {
        margin: 0
    }

    .fc-toolbar button {
        position: relative
    }

    .fc-toolbar .fc-state-hover, .fc-toolbar .ui-state-hover {
        z-index: 2
    }

    .fc-toolbar .fc-state-down {
        z-index: 3
    }

    .fc-toolbar .fc-state-active, .fc-toolbar .ui-state-active {
        z-index: 4
    }

    .fc-toolbar button:focus {
        z-index: 5
    }

    .fc-view-container *, .fc-view-container :after, .fc-view-container :before {
        box-sizing: content-box
    }

    .fc-view, .fc-view > table {
        position: relative;
        z-index: 1
    }

    .fc-basicDay-view .fc-content-skeleton, .fc-basicWeek-view .fc-content-skeleton {
        padding-bottom: 1em
    }

    .fc-basic-view .fc-body .fc-row {
        min-height: 4em
    }

    .fc-row.fc-rigid {
        overflow: hidden
    }

    .fc-row.fc-rigid .fc-content-skeleton {
        position: absolute;
        top: 0;
        left: 0;
        right: 0
    }

    .fc-day-top.fc-other-month {
        opacity: .3
    }

    .fc-basic-view .fc-day-number, .fc-basic-view .fc-week-number {
        padding: 2px
    }

    .fc-basic-view th.fc-day-number, .fc-basic-view th.fc-week-number {
        padding: 0 2px
    }

    .fc-ltr .fc-basic-view .fc-day-top .fc-day-number {
        float: right
    }

    .fc-rtl .fc-basic-view .fc-day-top .fc-day-number {
        float: left
    }

    .fc-ltr .fc-basic-view .fc-day-top .fc-week-number {
        float: left;
        border-radius: 0 0 3px 0
    }

    .fc-rtl .fc-basic-view .fc-day-top .fc-week-number {
        float: right;
        border-radius: 0 0 0 3px
    }

    .fc-basic-view .fc-day-top .fc-week-number {
        min-width: 1.5em;
        text-align: center;
        background-color: #f2f2f2;
        color: gray
    }

    .fc-basic-view td.fc-week-number {
        text-align: center
    }

    .fc-basic-view td.fc-week-number > * {
        display: inline-block;
        min-width: 1.25em
    }

    .fc-agenda-view .fc-day-grid {
        position: relative;
        z-index: 2
    }

    .fc-agenda-view .fc-day-grid .fc-row {
        min-height: 3em
    }

    .fc-agenda-view .fc-day-grid .fc-row .fc-content-skeleton {
        padding-bottom: 1em
    }

    .fc .fc-axis {
        vertical-align: middle;
        padding: 0 4px;
        white-space: nowrap
    }

    .fc-ltr .fc-axis {
        text-align: right
    }

    .fc-rtl .fc-axis {
        text-align: left
    }

    .fc-time-grid, .fc-time-grid-container {
        position: relative;
        z-index: 1
    }

    .fc-time-grid {
        min-height: 100%
    }

    .fc-time-grid table {
        border: 0 hidden transparent
    }

    .fc-time-grid > .fc-bg {
        z-index: 1
    }

    .fc-time-grid .fc-slats, .fc-time-grid > hr {
        position: relative;
        z-index: 2
    }

    .fc-time-grid .fc-content-col {
        position: relative
    }

    .fc-time-grid .fc-content-skeleton {
        position: absolute;
        z-index: 3;
        top: 0;
        left: 0;
        right: 0
    }

    .fc-time-grid .fc-business-container {
        position: relative;
        z-index: 1
    }

    .fc-time-grid .fc-bgevent-container {
        position: relative;
        z-index: 2
    }

    .fc-time-grid .fc-highlight-container {
        z-index: 3
    }

    .fc-time-grid .fc-event-container {
        position: relative;
        z-index: 4
    }

    .fc-time-grid .fc-now-indicator-line {
        z-index: 5
    }

    .fc-time-grid .fc-helper-container {
        position: relative;
        z-index: 6
    }

    .fc-time-grid .fc-slats td {
        height: 1.5em;
        border-bottom: 0
    }

    .fc-time-grid .fc-slats .fc-minor td {
        border-top-style: dotted
    }

    .fc-time-grid .fc-highlight-container {
        position: relative
    }

    .fc-time-grid .fc-highlight {
        position: absolute;
        left: 0;
        right: 0
    }

    .fc-ltr .fc-time-grid .fc-event-container {
        margin: 0 2.5% 0 2px
    }

    .fc-rtl .fc-time-grid .fc-event-container {
        margin: 0 2px 0 2.5%
    }

    .fc-time-grid .fc-bgevent, .fc-time-grid .fc-event {
        position: absolute;
        z-index: 1
    }

    .fc-time-grid .fc-bgevent {
        left: 0;
        right: 0
    }

    .fc-v-event.fc-not-start {
        border-top-width: 0;
        padding-top: 1px;
        border-top-left-radius: 0;
        border-top-right-radius: 0
    }

    .fc-v-event.fc-not-end {
        border-bottom-width: 0;
        padding-bottom: 1px;
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0
    }

    .fc-time-grid-event {
        overflow: hidden
    }

    .fc-time-grid-event.fc-selected {
        overflow: visible
    }

    .fc-time-grid-event.fc-selected .fc-bg {
        display: none
    }

    .fc-time-grid-event .fc-content {
        overflow: hidden
    }

    .fc-time-grid-event .fc-time, .fc-time-grid-event .fc-title {
        padding: 0 1px
    }

    .fc-time-grid-event .fc-time {
        font-size: .85em;
        white-space: nowrap
    }

    .fc-time-grid-event.fc-short .fc-content {
        white-space: nowrap
    }

    .fc-time-grid-event.fc-short .fc-time, .fc-time-grid-event.fc-short .fc-title {
        display: inline-block;
        vertical-align: top
    }

    .fc-time-grid-event.fc-short .fc-time span {
        display: none
    }

    .fc-time-grid-event.fc-short .fc-time:before {
        content: attr(data-start)
    }

    .fc-time-grid-event.fc-short .fc-time:after {
        content: "\A0-\A0"
    }

    .fc-time-grid-event.fc-short .fc-title {
        font-size: .85em;
        padding: 0
    }

    .fc-time-grid-event.fc-allow-mouse-resize .fc-resizer {
        left: 0;
        right: 0;
        bottom: 0;
        height: 8px;
        overflow: hidden;
        line-height: 8px;
        font-size: 11px;
        font-family: monospace;
        text-align: center;
        cursor: s-resize
    }

    .fc-time-grid-event.fc-allow-mouse-resize .fc-resizer:after {
        content: "="
    }

    .fc-time-grid-event.fc-selected .fc-resizer {
        border-radius: 5px;
        border-width: 1px;
        width: 8px;
        height: 8px;
        border-style: solid;
        border-color: inherit;
        background: #fff;
        left: 50%;
        margin-left: -5px;
        bottom: -5px
    }

    .fc-time-grid .fc-now-indicator-line {
        border-top-width: 1px;
        left: 0;
        right: 0
    }

    .fc-time-grid .fc-now-indicator-arrow {
        margin-top: -5px
    }

    .fc-ltr .fc-time-grid .fc-now-indicator-arrow {
        left: 0;
        border-width: 5px 0 5px 6px;
        border-top-color: transparent;
        border-bottom-color: transparent
    }

    .fc-rtl .fc-time-grid .fc-now-indicator-arrow {
        right: 0;
        border-width: 5px 6px 5px 0;
        border-top-color: transparent;
        border-bottom-color: transparent
    }

    .fc-event-dot {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 5px
    }

    .fc-rtl .fc-list-view {
        direction: rtl
    }

    .fc-list-view {
        border-width: 1px;
        border-style: solid
    }

    .fc .fc-list-table {
        table-layout: auto
    }

    .fc-list-table td {
        border-width: 1px 0 0;
        padding: 8px 14px
    }

    .fc-list-table tr:first-child td {
        border-top-width: 0
    }

    .fc-list-heading {
        border-bottom-width: 1px
    }

    .fc-list-heading td {
        font-weight: 700
    }

    .fc-ltr .fc-list-heading-main {
        float: left
    }

    .fc-ltr .fc-list-heading-alt, .fc-rtl .fc-list-heading-main {
        float: right
    }

    .fc-rtl .fc-list-heading-alt {
        float: left
    }

    .fc-list-item.fc-has-url {
        cursor: pointer
    }

    .fc-list-item-marker, .fc-list-item-time {
        white-space: nowrap;
        width: 1px
    }

    .fc-ltr .fc-list-item-marker {
        padding-right: 0
    }

    .fc-rtl .fc-list-item-marker {
        padding-left: 0
    }

    .fc-list-item-title a {
        text-decoration: none;
        color: inherit
    }

    .fc-list-item-title a[href]:hover {
        text-decoration: underline
    }

    .fc-list-empty-wrap2 {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0
    }

    .fc-list-empty-wrap1 {
        width: 100%;
        height: 100%;
        display: table
    }

    .fc-list-empty {
        display: table-cell;
        vertical-align: middle;
        text-align: center
    }

    .fc-unthemed .fc-list-empty {
        background-color: #eee
    }

    @import url(https://fonts.googleapis.com/css?family=Open+Sans:400,600);
    .fc-view-container {
        background-color: #fbfbfb;
        color: #333
    }

    .fc-row.fc-widget-header {
        border-bottom: 1px solid #ececec
    }

    .fc-row.fc-widget-header .fc-day-header {
        font-size: 12px;
        font-weight: 600;
        color: #acacac
    }

    .fc-axis {
        color: #acacac;
        font-size: .9em
    }

    .fc-state-default {
        text-shadow: none;
        box-shadow: none;
        background-image: none;
        background-color: #fff;
        border-color: #fff
    }

    .fc-button {
        text-transform: uppercase;
        font-weight: 600;
        font-size: 1.1em;
        border: 0;
        outline: none
    }

    .fc-button:active, .fc-button:focus, .fc-button:hover, .fc-button:visited {
        outline: none;
        border: 0;
        background-color: transparent
    }

    .fc-content-skeleton {
        border-top: 1px solid #ddd
    }

    .fc .fc-toolbar {
        padding: 0;
        margin-bottom: 0;
        border-bottom: 1px solid #ececec;
        min-height: 48px
    }

    .fc .fc-toolbar > * > button {
        padding: 15px 17px;
        height: auto;
        outline: 0;
        margin-left: 0;
        transition: opacity .2s ease;
        opacity: .3
    }

    .fc .fc-toolbar > * > button:hover {
        opacity: 1
    }

    .fc .fc-toolbar > * > button.fc-state-disabled {
        transition: opacity 0s;
        opacity: 0
    }

    .fc .fc-toolbar > * > button.fc-prev-button {
        padding-right: 8px
    }

    .fc .fc-toolbar > * > button.fc-next-button {
        padding-left: 8px
    }

    .fc .fc-toolbar > * > button .fc-icon {
        font-size: 1.1em
    }

    .fc .fc-toolbar > .fc-right > button.fc-today-button {
        padding: 16px 5px
    }

    .fc .fc-toolbar > .fc-right h2 {
        font-size: 13px;
        padding: 15px 0 15px 20px;
        color: #333;
        font-weight: 600
    }

    .fc-unthemed td.fc-today {
        background: #fff
    }

    .fc-body > tr > .fc-widget-content, .fc-head > tr > .fc-widget-header {
        border: 0 !important
    }

    .fc th {
        border-color: #fff;
        padding: 5px
    }

    .fc-unthemed .fc-divider, .fc-unthemed .fc-popover .fc-header {
        background-color: transparent
    }

    .empty-calendar .fc-event {
        opacity: 0
    }

    .fc-event {
        transition: color .2s ease, border-color .2s ease, opacity .6s ease, box-shadow .2s ease;
        border: none;
        border-left: 2px solid #939393;
        padding: 3px;
        background-color: #fff;
        border-radius: 3px;
        color: #333;
        margin: 1px 0;
        box-shadow: 0 1px 2px rgba(0, 0, 0, .07);
        cursor: pointer;
        margin-bottom: 2px;
        opacity: 1
    }

    .fc-event-clicked, .fc-event:hover {
        box-shadow: 0 2px 4px rgba(0, 0, 0, .12);
        border-left: 3px solid #2e5bec;
        color: #2e5bec;
        font-weight: 600;
        padding-left: 2px
    }

    .fc-event .fc-content {
        transform: translateX(0);
        transition: transform .2s ease
    }

    .fc-event:hover .fc-content {
        transform: translateX(2px)
    }

    .fc-event .fc-bg {
        opacity: 0
    }

    .fc-day-grid-event {
        padding: 13px 15px;
        margin: 1px 0 3px
    }

    .fc-day-grid-event-clicked, .fc-day-grid-event:hover {
        padding-left: 14px
    }

    .fc-day-grid-event .fc-time, .fc-day-grid-event .fc-title {
        font-size: 12px;
        font-weight: 500
    }

    .fc-day-grid-event .fc-title {
        padding: 0 5px 5px
    }

    .fc-day-grid-event-clicked .fc-time, .fc-day-grid-event-clicked .fc-title, .fc-day-grid-event:hover .fc-time, .fc-day-grid-event:hover .fc-title {
        font-weight: 600
    }

    .fc-time-grid .fc-slats .fc-minor td {
        border-top-style: none
    }

    .fc-time-grid .fc-slats td {
        border-top-color: #fbfbfb
    }

    .fc-time-grid .fc-slats td.fc-axis {
        border-top-color: #ececec
    }

    .fc-time-grid-event.fc-short .fc-content {
        font-size: .7em;
        line-height: .2em
    }

    .fc-time-grid-event.fc-short .fc-time:after {
        content: ""
    }

    .fc-time-grid-event .fc-time {
        font-size: 1.1em;
        padding: 5px
    }

    .fc-time-grid-event .fc-title {
        padding: 0 5px 5px;
        font-weight: 700
    }

    .fc-unthemed .fc-divider, .fc-unthemed .fc-popover, .fc-unthemed .fc-row, .fc-unthemed tbody, .fc-unthemed td, .fc-unthemed th, .fc-unthemed thead {
        border-color: #ececec
    }

    .fc-agendaMonthly-view .fc-event {
        color: #fff
    }

    .fc-now-indicator {
        border-color: rgba(255, 0, 0, .5)
    }

    .fc-unthemed .fc-basic-view .fc-scroller {
        padding: 5px 15px
    }

    .fc-unthemed .fc-basic-view .fc-content-skeleton {
        border-top: 0
    }

    .fc-unthemed .fc-list-view .fc-scroller {
        padding: 0 15px
    }

    .fc-list-view {
        border-width: 0
    }

    .fc-list-table {
        width: 80%;
        max-width: 400px;
        margin: 0 auto 30px
    }

    .fc-unthemed .fc-list-heading td {
        background: transparent;
        border-color: transparent;
        font-size: 1.3em;
        line-height: 1em;
        padding: 20px 19px 15px;
        font-weight: 500;
        color: #2e5bec
    }

    .fc-unthemed .fc-list-heading td .fc-list-heading-alt {
        color: #acacac
    }

    .is-small .fc-unthemed .fc-list-heading td {
        font-size: 1.1em
    }

    .fc-unthemed .fc-list-item:hover td {
        background-color: transparent
    }

    .fc-list-item {
        display: block;
        transition: color .2s ease, border-color .2s ease, opacity .6s ease, box-shadow .2s ease;
        border: none;
        border-left: 2px solid #939393;
        background-color: #fff;
        border-radius: 3px;
        color: #333;
        margin: 1px 0;
        box-shadow: 0 1px 2px rgba(0, 0, 0, .07);
        cursor: pointer;
        margin-bottom: 3px;
        font-weight: 500;
        font-size: 12px
    }

    .fc-list-item:hover {
        box-shadow: 0 2px 4px rgba(0, 0, 0, .12);
        border-left: 3px solid #2e5bec;
        color: #2e5bec;
        font-weight: 600;
        padding-left: 2px
    }

    .fc-list-item td {
        background: transparent;
        border-color: transparent;
        transform: translateX(0);
        transition: transform .2s ease
    }

    .fc-list-item:hover td {
        background: transparent;
        transform: translateX(2px)
    }

    .fc-list-item .fc-list-item-marker {
        display: none
    }

    .fc-list-item .fc-list-item-time {
        padding-right: 0;
        min-width: 110px
    }

    .fc-list-item .fc-list-item-title a {
        font-weight: 600
    }

    .fc-unthemed .fc-list-empty {
        background-color: transparent
    }

    @keyframes spin {
        to {
            transform: rotate(1turn)
        }
    }

    @keyframes shake {
        0% {
            transform: translateX(0)
        }
        25% {
            transform: translateX(5px)
        }
        50% {
            transform: translateX(-5px)
        }
        75% {
            transform: translateX(5px)
        }
        to {
            transform: translateX(0)
        }
    }

    @import url(https://fonts.googleapis.com/css?family=Open+Sans:400,600);
    /*!
 * Booking.js
 * http://timekit.io
 * (c) 2015 Timekit Inc.
 */
    @keyframes spin {
        to {
            transform: rotate(1turn)
        }
    }

    @keyframes shake {
        0% {
            transform: translateX(0)
        }
        25% {
            transform: translateX(5px)
        }
        50% {
            transform: translateX(-5px)
        }
        75% {
            transform: translateX(5px)
        }
        to {
            transform: translateX(0)
        }
    }

    .bookingjs {
        position: relative;
        font-family: Open Sans, Helvetica, Tahoma, Arial, sans-serif;
        font-size: 13px;
        border-radius: 4px;
        background-color: #fff;
        box-shadow: 0 3px 40px 0 rgba(0, 0, 0, .15);
        margin: 20px auto;
        z-index: 10;
        opacity: 1;
        color: #333;
        border-top: 1px solid #ececec;
        min-height: 200px
    }

    .bookingjs.has-avatar {
        margin-top: 60px
    }

    .bookingjs-calendar {
        border-bottom: 1px solid #ececec
    }

    .is-small.has-avatar.has-displayname .bookingjs-calendar .fc-toolbar {
        padding-bottom: 24px
    }

    .is-small.has-avatar .bookingjs-calendar .fc-toolbar .fc-right h2 {
        display: none
    }

    .bookingjs-footer {
        position: relative;
        color: #aeaeae;
        text-align: left;
        padding: 0 15px;
        background-color: #fbfbfb;
        min-height: 30px;
        line-height: 30px;
        z-index: 40;
        border-radius: 0 0 4px 4px;
        font-size: 12px;
        overflow: hidden
    }

    .is-small .bookingjs-footer {
        padding: 3px 15px;
        min-height: 25px;
        line-height: 25px
    }

    .bookingjs-footer-tz {
        float: left
    }

    .bookingjs-footer-tz-picker {
        display: inline-block
    }

    .bookingjs-footer-tz-picker-select:focus, .bookingjs-footer-tz-picker:hover .bookingjs-footer-tz-picker-select {
        color: #333;
        border-color: #d3d3d3
    }

    .bookingjs-footer-tz-picker:hover .bookingjs-footer-tz-picker-arrowdown {
        opacity: .6
    }

    .bookingjs-footer-tz-picker-select {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background: transparent;
        font-size: inherit;
        padding: 1px 20px 1px 5px;
        transition: border .2s ease, color .2s ease;
        border-radius: 3px;
        border: 1px solid #ececec;
        color: #aeaeae;
        outline: none;
        max-width: 220px
    }

    .bookingjs-footer-tz-picker-select::-ms-expand {
        display: none
    }

    .is-small .bookingjs-footer-tz-picker-select {
        max-width: 180px
    }

    .bookingjs-footer-tz-picker-arrowdown {
        position: relative;
        display: inline-block;
        pointer-events: none;
        opacity: .3;
        left: -19px;
        top: -2px;
        transition: opacity .2s ease
    }

    .bookingjs-footer-tz-picker-arrowdown svg {
        width: 8px;
        height: 5px
    }

    .bookingjs-footer-tz .bookingjs-timezoneicon {
        height: 10px;
        width: 10px;
        margin-right: 5px
    }

    .is-small .bookingjs-footer-tz .bookingjs-timezoneicon {
        display: none
    }

    .bookingjs-footer-by {
        float: right;
        transition: color .2s ease;
        color: #aeaeae;
        text-decoration: none;
        outline: none
    }

    .bookingjs-footer-by .bookingjs-timekitlogo {
        width: 12px;
        height: 12px;
        margin-left: 5px;
        top: 1px;
        position: relative
    }

    .bookingjs-footer-by .bookingjs-timekitlogo path {
        transition: fill .2s ease;
        fill: #aeaeae
    }

    .is-small .bookingjs-footer-by .bookingjs-timekitlogo {
        display: none
    }

    .bookingjs-footer-by:focus, .bookingjs-footer-by:hover {
        color: #333
    }

    .bookingjs-footer-by:focus .bookingjs-timekitlogo path, .bookingjs-footer-by:hover .bookingjs-timekitlogo path {
        fill: #333
    }

    .is-small .bookingjs-footer-by {
        float: left
    }

    .bookingjs-avatar {
        position: absolute;
        top: -50px;
        left: 50%;
        transform: translateX(-50%);
        border-radius: 150px;
        border: 3px solid #fff;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .13);
        overflow: hidden;
        z-index: 40;
        background-color: #fff
    }

    .is-small .bookingjs-avatar {
        top: -40px
    }

    .bookingjs-avatar img {
        max-width: 100%;
        vertical-align: middle;
        display: inline-block;
        width: 80px;
        height: 80px
    }

    .is-small .bookingjs-avatar img {
        width: 70px;
        height: 70px
    }

    .bookingjs-displayname {
        position: absolute;
        top: 0;
        left: 0;
        padding: 15px 20px;
        color: #333;
        font-weight: 600
    }

    .is-small.has-avatar .bookingjs-displayname {
        top: 44px;
        padding: 0 20px;
        text-align: center;
        left: 0;
        right: 0;
        box-sizing: border-box
    }

    .bookingjs-bookpage {
        position: absolute;
        height: 100%;
        width: 100%;
        top: 0;
        left: 0;
        background-color: #fbfbfb;
        z-index: 30;
        opacity: 0;
        transition: opacity .2s ease;
        border-radius: 4px;
        text-align: center
    }

    .bookingjs-bookpage.show {
        opacity: 1
    }

    .bookingjs-bookpage-close {
        position: absolute;
        top: 0;
        right: 0;
        padding: 18px;
        transition: opacity .2s ease;
        opacity: .3
    }

    .bookingjs-bookpage-close:hover {
        opacity: 1
    }

    .bookingjs-bookpage-header {
        margin-bottom: 30px
    }

    .bookingjs-bookpage-date, .bookingjs-bookpage h2 {
        text-align: center;
        font-size: 34px;
        font-weight: 400;
        margin-top: 50px;
        margin-bottom: 10px
    }

    .is-small .bookingjs-bookpage-date, .is-small .bookingjs-bookpage h2 {
        font-size: 27px
    }

    .bookingjs-bookpage-resource, .bookingjs-bookpage-time, .bookingjs-bookpage h3 {
        text-align: center;
        font-size: 17px;
        font-weight: 400;
        margin-bottom: 15px;
        margin-top: 10px
    }

    .is-small .bookingjs-bookpage-resource, .is-small .bookingjs-bookpage-time, .is-small .bookingjs-bookpage h3 {
        font-size: 15px;
        margin-bottom: 15px
    }

    .bookingjs-bookpage-resource-prefix {
        text-align: center;
        font-size: 10px;
        font-weight: 300;
        display: inline-block;
        position: relative
    }

    .bookingjs-bookpage-resource-prefix:before {
        left: -45px
    }

    .bookingjs-bookpage-resource-prefix:after, .bookingjs-bookpage-resource-prefix:before {
        content: "";
        display: block;
        width: 40px;
        height: 0;
        border-top: 1px solid #ececec;
        position: absolute;
        top: 7px
    }

    .bookingjs-bookpage-resource-prefix:after {
        right: -45px
    }

    .bookingjs-closeicon {
        height: 15px;
        width: 15px
    }

    .bookingjs-form {
        width: 350px;
        position: relative;
        margin: 0 auto;
        text-align: center
    }

    .is-small .bookingjs-form {
        width: 90%
    }

    .bookingjs-form-box {
        position: relative;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .1);
        overflow: hidden;
        background-color: #fff;
        line-height: 0
    }

    .bookingjs-form-success-message {
        position: absolute;
        top: -999px;
        left: 0;
        right: 0;
        padding: 30px;
        z-index: 10;
        background-color: #fff;
        opacity: 0;
        transition: opacity .3s ease;
        line-height: normal
    }

    .is-small .bookingjs-form-success-message {
        padding: 22px 10px
    }

    .bookingjs-form-success-message .title {
        font-size: 20px;
        display: block;
        margin-bottom: 25px
    }

    .bookingjs-form-success-message-body {
        display: block;
        position: relative;
        transform: translateY(-50%);
        top: 50%
    }

    .bookingjs-form-success-message-body .booked-email {
        color: #aeaeae
    }

    .bookingjs-form.success .bookingjs-form-success-message {
        opacity: 1;
        top: 0;
        bottom: 0
    }

    .bookingjs-form-field {
        position: relative
    }

    .bookingjs-form-field--dirty .bookingjs-form-label {
        opacity: 1;
        top: 20px;
        font-size: 11px;
        color: #2e5bec
    }

    .bookingjs-form-field--dirty .bookingjs-form-input {
        padding: 25px 25px 5px
    }

    .bookingjs-form-field--dirty .bookingjs-form-input:invalid {
        box-shadow: inset 0 0 1px 1px #d83b46
    }

    .bookingjs-form-field--dirty .bookingjs-form-input--textarea {
        padding: 30px 25px 10px
    }

    .bookingjs-form-label {
        position: absolute;
        top: 30px;
        left: 25px;
        color: #333;
        opacity: 0;
        font-size: 12px;
        transition: opacity .2s ease, font-size .2s ease, color .2s ease, top .2s ease
    }

    .bookingjs-form-input {
        transition: box-shadow .2s ease;
        width: 100%;
        padding: 15px 25px;
        margin: 0;
        border: 0 solid #ececec;
        font-size: 1em;
        box-shadow: inset 0 0 1px 1px hsla(0, 0%, 100%, 0);
        text-align: left;
        box-sizing: border-box;
        line-height: 1.5em;
        font-family: Open Sans, Helvetica, Tahoma, Arial, sans-serif;
        color: #333;
        overflow: auto;
        border-bottom: 1px solid #ececec
    }

    .bookingjs-form-input:focus {
        outline: 0;
        box-shadow: inset 0 0 1px 1px #2e5bec
    }

    .bookingjs-form-input.hidden {
        display: none
    }

    .bookingjs-form-input:-moz-read-only {
        cursor: not-allowed;
        font-style: italic
    }

    .bookingjs-form-input:read-only {
        cursor: not-allowed;
        font-style: italic
    }

    .bookingjs-form-input:-moz-read-only:focus {
        box-shadow: inset 0 0 1px 1px #d8d8d8
    }

    .bookingjs-form-input:read-only:focus {
        box-shadow: inset 0 0 1px 1px #d8d8d8
    }

    .bookingjs-form-field--checkbox {
        text-align: left;
        position: relative
    }

    .bookingjs-form-label--checkbox {
        padding: 25px 25px 25px 50px;
        display: block;
        font-family: Open Sans, Helvetica, Tahoma, Arial, sans-serif;
        color: #787878;
        font-size: 1em;
        border-bottom: 1px solid #ececec
    }

    .bookingjs-form-label--checkbox:disabled {
        cursor: not-allowed;
        font-style: italic
    }

    .bookingjs-form-label--checkbox:disabled:focus {
        box-shadow: inset 0 0 1px 1px #d8d8d8
    }

    .bookingjs-form-input--checkbox[type=checkbox] {
        position: absolute;
        top: 19px;
        left: 25px;
        width: auto;
        margin: 0
    }

    .bookingjs-form-input--textarea {
        padding: 15px 25px 25px;
        overflow: auto
    }

    .bookingjs-form-field--select {
        text-align: left
    }

    .bookingjs-form-label--select {
        opacity: 1;
        top: 20px;
        font-size: 11px;
        color: #2e5bec
    }

    .bookingjs-form-input--select {
        transition: box-shadow .2s ease;
        width: 100%;
        padding: 15px 25px;
        margin: 0;
        border: 0 solid #ececec;
        font-size: 1em;
        box-shadow: inset 0 0 1px 1px hsla(0, 0%, 100%, 0);
        text-align: left;
        box-sizing: border-box;
        line-height: 1.5em;
        font-family: Open Sans, Helvetica, Tahoma, Arial, sans-serif;
        color: #333;
        overflow: auto;
        border-bottom: 1px solid #ececec;
        padding: 25px 25px 5px;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        border-radius: 0;
        background: transparent;
        line-height: 35px
    }

    .bookingjs-form-input--select:focus {
        outline: 0;
        box-shadow: inset 0 0 1px 1px #2e5bec
    }

    .bookingjs-form-input--select.hidden {
        display: none
    }

    .bookingjs-form-input-arrow--select {
        position: absolute;
        top: 31px;
        right: 25px;
        width: 12px;
        height: 7.42px;
        opacity: .2;
        transition: opacity .2s ease;
        pointer-events: none
    }

    .bookingjs-form-field--select:hover .bookingjs-form-input-arrow--select {
        opacity: .5
    }

    .bookingjs-form-button {
        position: relative;
        transition: background-color .2s, max-width .3s;
        display: inline-block;
        padding: 13px 25px;
        background-color: #2e5bec;
        text-transform: uppercase;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .15);
        color: #fff;
        border: 0;
        border-radius: 3px;
        font-size: 1.1em;
        font-weight: 600;
        margin-top: 30px;
        cursor: pointer;
        height: 44px;
        outline: 0;
        text-align: center;
        max-width: 200px
    }

    .bookingjs-form-button .error-text, .bookingjs-form-button .loading-text, .bookingjs-form-button .success-text {
        transition: opacity .3s ease;
        position: absolute;
        top: 13px;
        left: 50%;
        transform: translateX(-50%);
        opacity: 0
    }

    .bookingjs-form-button .inactive-text {
        white-space: nowrap;
        opacity: 1
    }

    .bookingjs-form-button .loading-text svg {
        height: 19px;
        width: 19px;
        animation: spin .6s infinite linear
    }

    .bookingjs-form-button .error-text svg {
        height: 15px;
        width: 15px;
        margin-top: 2px
    }

    .bookingjs-form-button .success-text svg {
        height: 15px;
        margin-top: 2px;
        transform: scale(0);
        transition: transform .6s ease
    }

    .bookingjs-form-button:focus, .bookingjs-form-button:hover {
        background-color: #1341d4
    }

    .bookingjs-form-button.button-shake {
        animation: shake .5s 1 ease
    }

    .bookingjs-form.loading .bookingjs-form-button, .bookingjs-form.loading .bookingjs-form-button:hover {
        max-width: 80px;
        background-color: #b1b1b1;
        cursor: not-allowed
    }

    .bookingjs-form.loading .bookingjs-form-button .inactive-text, .bookingjs-form.loading .bookingjs-form-button:hover .inactive-text {
        opacity: 0
    }

    .bookingjs-form.loading .bookingjs-form-button .loading-text, .bookingjs-form.loading .bookingjs-form-button:hover .loading-text {
        opacity: 1
    }

    .bookingjs-form.error .bookingjs-form-button, .bookingjs-form.error .bookingjs-form-button:hover {
        max-width: 80px;
        background-color: #d83b46;
        cursor: not-allowed
    }

    .bookingjs-form.error .bookingjs-form-button .inactive-text, .bookingjs-form.error .bookingjs-form-button:hover .inactive-text {
        opacity: 0
    }

    .bookingjs-form.error .bookingjs-form-button .error-text, .bookingjs-form.error .bookingjs-form-button:hover .error-text {
        opacity: 1
    }

    .bookingjs-form.success .bookingjs-form-button, .bookingjs-form.success .bookingjs-form-button:hover {
        max-width: 80px;
        background-color: #46ce92;
        cursor: pointer
    }

    .bookingjs-form.success .bookingjs-form-button .inactive-text, .bookingjs-form.success .bookingjs-form-button:hover .inactive-text {
        opacity: 0
    }

    .bookingjs-form.success .bookingjs-form-button .success-text, .bookingjs-form.success .bookingjs-form-button:hover .success-text {
        opacity: 1
    }

    .bookingjs-form.success .bookingjs-form-button .success-text svg, .bookingjs-form.success .bookingjs-form-button:hover .success-text svg {
        transform: scale(1)
    }

    .bookingjs-loading {
        position: absolute;
        height: 100%;
        width: 100%;
        top: 0;
        left: 0;
        background-color: #fbfbfb;
        z-index: 30;
        opacity: 0;
        transition: opacity .5s ease;
        border-radius: 4px
    }

    .bookingjs-loading.show {
        opacity: 1
    }

    .bookingjs-loading-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%)
    }

    .bookingjs-loading-icon svg {
        height: 30px;
        width: 30px;
        animation: spin .6s infinite linear
    }

    .bookingjs-loading-icon svg path {
        fill: #2e5bec
    }

    .bookingjs-error {
        position: absolute;
        height: 100%;
        width: 100%;
        top: 0;
        left: 0;
        background-color: #fbfbfb;
        z-index: 31;
        opacity: 0;
        transition: opacity .5s ease;
        border-radius: 4px
    }

    .bookingjs-error.show {
        opacity: 1
    }

    .bookingjs-error-inner {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        text-align: center;
        overflow: scroll;
        max-height: 100%;
        padding: 30px;
        box-sizing: border-box;
        width: 100%
    }

    .bookingjs-error-icon svg {
        height: 30px;
        width: 30px
    }

    .bookingjs-error-icon svg g {
        fill: #d83b46
    }

    .bookingjs-error-heading {
        color: #d83b46;
        font-size: 15px;
        margin: 15px 0
    }

    .bookingjs-error-text {
        font-size: 12px;
        color: #aeaeae;
        word-break: break-word;
        overflow: scroll
    }

    .bookingjs-error-text-context, .bookingjs-error-text-messag {
        display: block
    }

    @import url(https://fonts.googleapis.com/css?family=Open+Sans:400,600);
    .bookingjs-ribbon-wrapper {
        height: 140px;
        width: 35px;
        bottom: -34px;
        right: 19px;
        z-index: 42;
        -webkit-backface-visibility: hidden
    }

    .bookingjs-ribbon-wrapper, .bookingjs-ribbon-wrapper .bookingjs-ribbon-container {
        background: transparent;
        position: absolute;
        transform: rotate(45deg);
        overflow: hidden
    }

    .bookingjs-ribbon-wrapper .bookingjs-ribbon-container {
        height: 110px;
        width: 110px;
        left: -54px;
        top: 15px
    }

    .bookingjs-ribbon-wrapper .bookingjs-ribbon-container:before {
        content: "";
        display: block;
        position: absolute;
        right: 94px;
        top: 0;
        width: 0;
        height: 0;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        border-bottom: 6px solid #ff8c22
    }

    .bookingjs-ribbon-wrapper .bookingjs-ribbon-container:after {
        content: "";
        display: block;
        position: absolute;
        right: 0;
        top: 92px;
        width: 0;
        height: 0;
        border-top: 6px solid transparent;
        border-bottom: 6px solid transparent;
        border-left: 6px solid #ff8c22
    }

    .bookingjs-ribbon-wrapper .bookingjs-ribbon-container .bookingjs-ribbon {
        width: 140px;
        height: 21px;
        position: relative;
        top: 32px;
        right: 3px;
        z-index: 1;
        overflow: hidden;
        transform: rotate(45deg);
        background: #ffb46e
    }

    .bookingjs-ribbon-wrapper .bookingjs-ribbon-container .bookingjs-ribbon > span {
        text-align: center;
        display: block;
        position: relative;
        bottom: -6px;
        transform: rotate(180deg);
        font-size: 10px;
        color: #fbfbfb;
        text-transform: uppercase;
        font-weight: 700;
        letter-spacing: 1px;
        line-height: 1
    }</style>


<div id="availability-calendar" class="coach-details-page-availability" data-astro-cid-2k7izlee="true">
    <div class="availability-header">
        <div class="availability-header-title">Khả dụng</div>
    </div>
    <div id="bookingjs" class="bookingjs">
        <div class="bookingjs-calendar fc fc-unthemed fc-ltr">
            <div class="fc-toolbar fc-header-toolbar" style="">
                <div class="fc-left">

                </div>
                <div class="fc-right">
                    <h2>Available Time Slots (Next 14 Days)</h2>
                </div>
                <div class="fc-center"></div>
                <div class="fc-clear"></div>
            </div>
            <div class="fc-view-container" style="">
                <div class="fc-view fc-listing-view fc-list-view fc-widget-content" style="">
                    <div class="fc-scroller" style="overflow: hidden auto;height: 500px">
                        <table class="fc-list-table ">
                            <tbody>
                            @foreach($daysOfWeek as $dayNum => $dayName)
                                @if(isset($allTimeSlots[$dayNum]) && count($allTimeSlots[$dayNum]) > 0)
                                    @php
                                        $firstSlot = $allTimeSlots[$dayNum]->first();
                                        $date = \Carbon\Carbon::parse($firstSlot['date']);
                                    @endphp
                                    <tr class="fc-list-heading" data-date="{{ $date->format('Y-m-d') }}">
                                        <td class="fc-widget-header" colspan="3">
                                            <span class="fc-list-heading-main">{{ $date->format('F j, Y') }}</span>
                                            <span class="fc-list-heading-alt">{{ $dayName }}</span>
                                        </td>
                                    </tr>
                                @endif
                                @foreach($allTimeSlots[$dayNum] ?? [] as $slot)
                                    <tr class="fc-list-item" data-original-title="" title="">
                                        <td class="fc-list-item-time fc-widget-content">{{ $slot['formatted'] }}</td>
                                        <td class="fc-list-item-marker fc-widget-content"><span class="fc-event-dot"></span>
                                        </td>
                                        <td class="fc-list-item-title fc-widget-content"><a></a></td>
                                        <td class="slot-expiration-td">

                                        </td>
                                    </tr>
                                @endforeach
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="bookingjs-footer">
            <div class="bookingjs-footer-tz">
                <svg class="bookingjs-timezoneicon" viewBox="0 0 98 98" version="1.1"
                     xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                     xmlns:sketch="http://www.bohemiancoding.com/sketch/ns"><title>Shape</title>
                    <desc>Created with Sketch.</desc>
                    <defs></defs>
                    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"
                       sketch:type="MSPage">
                        <g id="timezone-icon" sketch:type="MSLayerGroup" fill="#AEAEAE">
                            <path
                                d="M37.656,1.387 L39.381,2.516 L46.176,3.475 L49.313,2.778 L55.186,3.495 L56.364,5.065 L52.274,4.52 L48.092,6.262 L49.293,9.385 L53.613,11.348 L54.189,7.395 L58.285,7.133 L64.121,12.707 L65.775,14.887 L66.56,16.28 L62.029,18.067 L55.185,21.169 L54.624,24.206 L50.095,28.476 L50.271,32.572 L48.9,32.559 L48.353,29.086 L45.757,28.238 L38.294,28.631 L35.286,34.137 L37.901,37.274 L42.221,34.917 L42.516,38.755 L44.172,40.062 L47.131,43.46 L46.985,47.751 L52.448,49.034 L56.454,46.159 L58.284,46.768 L65.003,49.45 L74.433,52.985 L76.396,57.698 L83.111,60.968 L84.644,66.732 L80.062,71.857 L74.66,77.519 L68.933,80.482 L63.04,84.408 L55.185,89.515 L50.835,93.941 L49.292,92.263 L52.782,83.419 L53.663,73.167 L46.15,66.34 L46.199,60.596 L48.164,58.239 L50.471,51.415 L45.809,48.811 L42.664,43.706 L37.75,41.817 L30.047,37.667 L26.904,29.024 L25.334,33.344 L22.977,26.276 L23.762,15.671 L27.69,12.136 L26.512,9.779 L29.26,5.459 L23.905,6.99 C9.611,15.545 0.01,31.135 0.01,49.006 C0.01,76.062 21.945,98 49.006,98 C76.062,98 98,76.062 98,49.006 C98,21.947 76.062,0.012 49.006,0.012 C45.092,0.012 41.305,0.52 37.656,1.387 Z"
                                id="Shape" sketch:type="MSShapeGroup"></path>
                        </g>
                    </g>
                </svg>

            </div>
        </div>
    </div>
</div>


