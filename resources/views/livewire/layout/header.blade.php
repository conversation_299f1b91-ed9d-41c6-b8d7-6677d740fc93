<?php

use App\Models\Service;
use Illuminate\Support\Facades\Storage;
use function Livewire\Volt\{state, mount};

state(['topServices', 'logo', 'blogCategories']);

mount(function () {
    $this->logo = Storage::url(get_config('logo'));
    $this->topServices = Service::query()
        ->select('services.name', 'services.slug')
        ->join('coache_service', 'services.id', '=', 'coache_service.service_id')
        ->join('coaches', 'coache_service.coache_id', '=', 'coaches.id')
        ->join('coaching_sessions', 'coaches.id', '=', 'coaching_sessions.coach_id')
        ->where('coaching_sessions.status', '=', \App\Models\CoachingSession::STATUS_COMPLETED)
        ->groupBy('services.id', 'services.name', 'services.slug')
        ->orderByRaw('COUNT(coaching_sessions.id) DESC')
        ->limit(5)
        ->get();
    $this->blogCategories = \App\Models\BlogCategory::query()->select('name', 'slug')->orderBy('created_at', 'desc')->get();
});
$logout = function () {
    auth()->guard('customer')->logout();
    $this->redirectRoute('home', navigate: true);
}
?>

<header>
    <div class="header-container">
        <div class="logo">
            <a href="{{route('home')}}" wire:navigate>
                <img width="150" height="44" alt="logo"
                     src="{{$logo}}"
                     loading="eager"/>
            </a>
        </div>
        <nav>
            {{--            active--}}
            <div class="nav-item ">
                <a href="#">
                    Coaches
                    <img src="https://igotanoffer.com/_astro/icon-chevron-down.X-y6WSnL.svg"
                         alt="Icon to expand interview coaching navigation menu" height="10" width="10"
                         loading="lazy"/>
                </a>
                <div class="child-items">
                    <div class="empty-space"></div>
                    <div class="menu-triangle"></div>
                    <div class="items-container">
                        {{--                        active--}}
                        <a class=" menu-item {{request()->route()->getName() == 'home' ? 'active' : ''}}"
                           href="{{route('home')}}" wire:navigate>
                            Tất cả Coaches
                            <!----><!---->
                        </a>
                        @foreach($topServices as $service)
                            <a class="menu-item {{request('slug') == $service->slug ? 'active' : ''}}"
                               href="{{$service->slug}}" wire:navigate>
                                {{$service->name}}
                                @if($loop->first)
                                    <span class="new-badge new-badge--small new-badge--child" data-v-19dfa40e=""> Phổ biến </span>
                                @endif
                            </a>
                        @endforeach


                    </div>
                </div>
            </div>
            <div class="nav-item ">
                <a href="{{route('blog.index')}}" wire:navigate>
                    Blog
                    <img src="https://igotanoffer.com/_astro/icon-chevron-down.X-y6WSnL.svg"
                         alt="Icon to expand interview coaching navigation menu" height="10" width="10"
                         loading="lazy"/>
                </a>
                <div class="child-items">
                    <div class="empty-space"></div>
                    <div class="menu-triangle"></div>
                    <div class="items-container">
                        {{--                        active--}}
                        @foreach($blogCategories as $item)
                            <a class="menu-item" href="{{route('blog.category',['slug' => $item->slug])}}"
                               wire:navigate>
                                {{$item->name}}
                            </a>
                        @endforeach
                    </div>
                </div>
            </div>
            <div class="nav-item">
                <a href="{{route('reviews.index')}}" wire:navigate>
                    Đánh giá
                </a>
            </div>

        </nav>
        <div class="account">
            <style>
                astro-island,
                astro-slot,
                astro-static-slot {
                    display: contents;
                }
            </style>
            <div data-v-5d2dab64="" class="header-account-container">
                <div data-v-f9a5b2be="" data-v-5d2dab64="" class="account-buttons">
                    @if(!auth()->guard('customer')->user())
                        <a data-v-55ea226e="" data-v-0dc3f13d=""
                           data-v-f9a5b2be="" class="btn igao-btn igao-btn--m become-coach link-style"
                           href="{{route('filament.public.pages.apply-coach')}}"
                           aria-disabled="false" size="m" fullwidth="false">
                            Ứng tuyển làm huấn luyện viên </a>
                    @endif
                    @if(auth()->guard('customer')->user())
                        <div data-v-f9a5b2be="" data-v-5d2dab64="" class="account-buttons">
                            <a data-v-55ea226e=""
                               data-v-0dc3f13d=""
                               data-v-f9a5b2be=""
                               class="btn btn-primary igao-btn igao-btn--m"
                               href="{{ route('filament.customer.pages.dashboard') }}"
                               aria-disabled="false"
                               size="m"
                               fullwidth="false"><!---->
                                Tài khoản </a>
                            <a data-v-55ea226e="" wire:click="logout" data-v-0dc3f13d="" data-v-f9a5b2be=""
                               class="btn igao-btn igao-btn--m link-style" href="#"
                               aria-disabled="false" size="m" fullwidth="false">Logout
                            </a>
                        </div>
                    @else
                        <a data-v-55ea226e="" data-v-0dc3f13d=""
                           data-v-f9a5b2be=""
                           class="btn igao-btn igao-btn--m link-style"
                           href="{{route('login')}}" wire:navigate aria-disabled="false" size="m"
                           fullwidth="false">Đăng nhập </a>
                    @endif

                </div>
                <button data-v-5d2dab64="" class="mobile-menu-icon" @click="openMenu = !openMenu">
                    <img data-v-5d2dab64="" src="https://igotanoffer.com/images/icon-menu-open.svg"
                         width="16" height="14"
                         alt="Icon to open interview coaching navigation menu">
                </button>
            </div>
        </div>
    </div>
</header>
