<?php

use App\Models\BlogCategory;
use App\Models\Service;
use function Livewire\Volt\{state, mount};

state(['categories', 'selectedCategory','categoryId']);

mount(function () {
    $this->categories = BlogCategory::all();
});

?>

<nav class="articles-sub-navigation" data-astro-cid-nbh2emmn="">
    <div class="nav-item {{empty($categoryId) ? 'active' : ''}}" data-astro-cid-nbh2emmn="true"
         data-v-19dfa40e="">
        <a href="{{route('blog.index')}}"
           data-v-19dfa40e="">
            All
        </a>
    </div>
    @foreach($categories as $cate)
        <div class="nav-item {{$cate->id == $categoryId ? 'active' : ''}}" data-astro-cid-nbh2emmn="true"
             data-v-19dfa40e="">
            <a href="{{route('blog.category',['slug' => $cate->slug])}}" wire:navigate
               data-v-19dfa40e="">
                {{$cate->name}}
            </a>
        </div>
    @endforeach

</nav>
