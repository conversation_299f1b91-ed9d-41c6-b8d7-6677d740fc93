<div style="margin-top: 100px;">
    <style>@font-face {
            font-family: "Open Sans";
            font-style: italic;
            font-weight: 400;
            font-stretch: 100%;
            font-display: swap;
            src: url(/fonts/open-sans-400-italic.woff2) format("woff2");
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD
        }

        @font-face {
            font-family: "Open Sans";
            font-style: normal;
            font-weight: 400;
            font-stretch: 100%;
            font-display: swap;
            src: url(/fonts/open-sans-400.woff2) format("woff2");
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD
        }

        @font-face {
            font-family: "Open Sans";
            font-style: normal;
            font-weight: 600;
            font-stretch: 100%;
            font-display: swap;
            src: url(/fonts/open-sans-600.woff2) format("woff2");
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD
        }

        @font-face {
            font-family: "Open Sans";
            font-style: normal;
            font-weight: 700;
            font-stretch: 100%;
            font-display: swap;
            src: url(/fonts/open-sans-700.woff2) format("woff2");
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD
        }

        body {
            font-size: 18px;
            line-height: 1.56;
            font-family: "Open Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            font-weight: 400;
            color: #4a4a4a;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.004)
        }
    </style>
    <style>.account-buttons[data-astro-cid-hmtt24ci] {
            display: flex;
            flex-direction: column;
            width: 100%
        }

        .account-buttons[data-astro-cid-hmtt24ci] a.btn:not(:last-child) {
            margin-bottom: 12px
        }

        .mobile-navigation-container[data-astro-cid-hmtt24ci] {
            display: none;
            position: fixed;
            top: 0;
            width: 100vw;
            left: 0;
            flex-direction: column;
            align-items: start;
            background: #ffffff;
            z-index: 400;
            padding: 86px 24px 156px;
            height: 100vh;
            overflow: auto
        }

        .mobile-navigation-container[data-astro-cid-hmtt24ci].open {
            display: flex
        }

        nav[data-astro-cid-2xhbaixd] {
            flex-grow: 1;
            display: flex
        }

        .fake-header[data-astro-cid-xbstl6g3] {
            display: flex;
            min-height: 70px;
            width: 100%
        }

        .mobile-sub-navigation[data-astro-cid-xbstl6g3] {
            height: 58px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #fff;
            border-bottom: 2px solid #eeeeee;
            color: #4a4a4a
        }

        @media (min-width: 1200px) {
            .mobile-sub-navigation[data-astro-cid-xbstl6g3] {
                display: none
            }
        }

        .mobile-sub-navigation[data-astro-cid-xbstl6g3] a[data-astro-cid-xbstl6g3].navigation-link {
            position: relative;
            box-sizing: border-box;
            text-decoration: none;
            font-weight: 600;
            cursor: pointer;
            color: #000;
            white-space: nowrap;
            font-size: 14px
        }

        .mobile-sub-navigation[data-astro-cid-xbstl6g3] a[data-astro-cid-xbstl6g3].navigation-link.active {
            color: #0567a7
        }

        .mobile-sub-navigation[data-astro-cid-xbstl6g3] a[data-astro-cid-xbstl6g3].navigation-link:not(:last-child) {
            margin-right: 16px
        }

        header[data-astro-cid-xbstl6g3] {
            position: fixed;
            width: 100%;
            box-sizing: border-box;
            height: 70px;
            border-bottom: 6px solid #eeeeee;
            background-color: #ffffff;
            padding: 10px 24px;
            display: flex;
            z-index: 500
        }

        @media (min-width: 1200px) {
            header[data-astro-cid-xbstl6g3] {
                padding: 10px 64px
            }
        }

        header[data-astro-cid-xbstl6g3] .header-container[data-astro-cid-xbstl6g3] {
            display: flex;
            width: 100%;
            align-items: center;
            margin: auto
        }

        header[data-astro-cid-xbstl6g3] .logo[data-astro-cid-xbstl6g3] {
            display: flex
        }

        header[data-astro-cid-xbstl6g3] .logo[data-astro-cid-xbstl6g3] > a[data-astro-cid-xbstl6g3] {
            display: flex
        }

        .auth-page-title[data-v-3a966b0f] {
            font-size: 40px;
            line-height: 1.25;
            font-weight: bold;
            max-width: 480px;
            margin: 48px auto 0;
            text-align: center;
        }

        .auth-page-subtitle[data-v-3a966b0f] {
            font-size: 16px;
            line-height: 1.5;
            font-weight: normal;
        }

        .auth-form[data-v-94350294] {
            max-width: 420px;
            display: flex;
            flex-direction: column;
            margin: auto;
        }

        .auth-form label[data-v-94350294] {
            font-size: 14px;
            line-height: 1.57;
            font-weight: 600;
            margin: 20px 0 6px;
        }

        .auth-form label[data-v-94350294]:first-child {
            margin-top: 0;
        }

        .auth-form-submit[data-v-94350294] {
            margin-top: 20px;
        }

        .auth-form-field-with-error[data-v-94350294] input {
            border: 1px solid #c63120 !important;
        }

        .auth-form-field-error[data-v-94350294] {
            font-size: 12px;
            line-height: 1.67;
            white-space: pre-wrap;
            color: #c63120;
            margin-top: 4px;
            font-weight: 700;
        }

        .auth-form-container[data-v-94350294] {
            max-width: 60rem;
            margin-left: auto;
            margin-right: auto;
            background: #ffffff;
            box-shadow: 0 1px 2px 0 rgba(51, 51, 51, 0.12);
            margin-top: 2rem;
            padding: 30px 16px;
        }

        @media (min-width: 960px) {
            .auth-form-container[data-v-94350294] {
                padding: 48px 36px;
            }
        }

        .auth-form-field-hint[data-v-94350294] {
            font-size: 12px;
            line-height: 1.67;
            color: #6c757d;
        }

        main {
            padding-left: 16px;
            padding-right: 16px;
        }

        .auth-form-error[data-v-cefafc1c] {
            font-size: 14px;
            line-height: 1.57;
            color: #c63120;
            color: #700000;
            background-color: #f7cccc;
            padding: 12px 20px;
            border: 1px solid #f4b8b8;
        }

        a.auth-form-bottom-link[data-v-146e71b1] {
            font-size: 12px;
            line-height: 1.67;
            margin-top: 16px;
            text-align: center;
            text-transform: uppercase;
        }

        .igao-link[data-v-bb39e5c0] {
            font-weight: 600;
            font-stretch: normal;
            font-style: normal;
            letter-spacing: normal;
            cursor: pointer;
        }

        .igao-link[disabled=true][data-v-bb39e5c0] {
            opacity: 0.56;
            pointer-events: none;
        }

        a.igao-link--primary[data-v-bb39e5c0] {
            color: #0567a7;
        }

        a.igao-link--primary[data-v-bb39e5c0]:hover {
            color: #0567a7;
        }

        a.igao-link--secondary[data-v-bb39e5c0] {
            color: #aaaaaa;
            text-decoration: underline;
        }

        a.igao-link--secondary[data-v-bb39e5c0]:hover, a.igao-link--secondary[data-v-bb39e5c0]:active {
            color: #4a4a4a;
        }

        .login-page-password-label {
            display: flex;
            justify-content: space-between;
        }

        .igao-btn.igao-btn--full-width[data-v-0dc3f13d] {
            width: 100%;
        }

        .igao-btn.igao-btn--l[data-v-0dc3f13d] {
            height: 50px;
            padding: 15px 32px;
        }

        .igao-btn.igao-btn--xl[data-v-0dc3f13d] {
            height: 54px;
            padding: 17px 32px;
        }

        .igao-btn.igao-btn--m[data-v-0dc3f13d] {
            height: 40px;
            padding: 10px 20px;
        }

        .igao-btn.igao-btn--s[data-v-0dc3f13d] {
            height: 32px;
            padding: 6px 16px;
        }

        .igao-text-input--size-s input[data-v-a862fb29] {
            min-height: 32px;
            max-height: 32px;
        }

        .igao-text-input--size-m input[data-v-a862fb29] {
            min-height: 40px;
            max-height: 40px;
        }

        .igao-text-input--size-l input[data-v-a862fb29] {
            min-height: 50px;
            max-height: 50px;
        }

        .igao-text-input-label[data-v-a862fb29] {
            font-size: 14px;
            line-height: 1.57;
            text-transform: uppercase;
            font-weight: bold;
            color: #666666;
            margin-bottom: 8px;
        }

        @media (max-width: 960px) {
            .igao-text-input-label[data-v-a862fb29] {
                font-size: 12px;
                line-height: 1.67;
            }
        }

        .igao-text-input[data-v-a862fb29] {
            display: flex;
            flex-direction: column;
            text-align: left;
            position: relative;
        }

        .igao-text-input.igao-text-input--has-errors input[data-v-a862fb29] {
            border-color: #c63120 !important;
        }

        .igao-text-input.igao-text-input--search input[data-v-a862fb29] {
            background-color: #fff;
            background-image: url(/icons/icon-search.svg);
            background-position: 16px center;
            background-repeat: no-repeat;
            background-size: 14px 14px;
            padding: 0 28px 0 40px;
        }

        .igao-text-input input[data-v-a862fb29] {
            background-clip: padding-box;
            border: 1px solid #d5d4d4;
            border-radius: 0;
            transition: border 0.2s linear, box-shadow 0.2s linear;
            font-size: 14px;
            line-height: 1.57;
            padding: 8px 15px;
            width: 100%;
            background-color: #fff;
            color: #4a4a4a;
        }

        .igao-text-input input[data-v-a862fb29]:focus, .igao-text-input input[data-v-a862fb29]:active:not([disabled]), .igao-text-input input.is-active[data-v-a862fb29] {
            border-color: #0567a7;
            box-shadow: 0 0 0 2px rgba(0, 123, 202, 0.12);
            outline: 0;
        }

        @media (max-width: 960px) {
            .igao-text-input input[data-v-a862fb29] {
                font-size: 16px;
                line-height: 1.5;
            }
        }

        .igao-text-input input[disabled][data-v-a862fb29] {
            background-color: #fafafa;
            color: #aaa;
        }

        .igao-text-input-wrapper[data-v-a862fb29] {
            position: relative;
        }

        .igao-text-input-clear[data-v-a862fb29] {
            background: none;
            -webkit-mask-image: url("/icons/remove.svg");
            mask-image: url("/icons/remove.svg");
            -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
            -webkit-mask-size: contain;
            mask-size: contain;
            -webkit-mask-position: center;
            mask-position: center;
            background-color: #878787;
            width: 12px;
            height: 12px;
            border: none;
            margin: 0;
            padding: 0;
            opacity: 0.75;
            cursor: pointer;
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            -webkit-mask-size: 12px 12px;
            mask-size: 12px 12px;
            height: 20px;
            width: 20px;
        }

        .igao-text-input-clear[data-v-a862fb29]:hover {
            opacity: 1;
        }

        .igao-simple-modal__content[data-v-aff3d3c9] {
            font-size: 14px;
            line-height: 1.57;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            letter-spacing: normal;
            color: #4a4a4a;
        }

        .igao-simple-modal__header[data-v-aff3d3c9] {
            font-size: 20px;
            line-height: 1.5;
            margin-bottom: 16px;
            padding-right: 28px;
            font-weight: 600;
            font-stretch: normal;
            font-style: normal;
            letter-spacing: -0.1px;
            color: #333333;
        }

        .igao-simple-modal__header--without-close[data-v-aff3d3c9] {
            padding-right: 0;
        }

        .modal-popup-content.igao-simple-modal-content--align-left {
            --modal-text-align: left;
        }

        .modal-popup-content.igao-simple-modal-content--align-center {
            --modal-text-align: center;
        }

        .modal-popup-content.igao-simple-modal-content--align-right {
            --modal-text-align: right;
        }

        .modal-popup-content.igao-simple-modal-content .modal-popup-body {
            text-align: var(--modal-text-align);
        }

        .modal-popup-content.igao-simple-modal-content .modal-popup-footer {
            justify-content: center;
        }

        @media (max-width: 960px) {
            .modal-popup-content.igao-simple-modal-content .modal-popup-footer {
                flex-direction: column;
            }

            .modal-popup-content.igao-simple-modal-content .modal-popup-footer > .btn + .btn,
            .modal-popup-content.igao-simple-modal-content .modal-popup-footer > .btn + div {
                margin-left: 0;
                margin-top: 8px;
            }
        }

        .btn[data-v-55ea226e] {
            font-size: 12px;
            line-height: 1.67;
            border-radius: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #0567a7;
            position: relative;
            padding: 6px 15px;
            font-weight: bold;
            text-transform: uppercase;
            border: none;
            white-space: nowrap;
        }

        .btn.btn-primary[data-v-55ea226e] {
            color: #ffffff;
            background-color: #0567a7;
        }

        .btn.btn-primary[data-v-55ea226e]:hover {
            background-color: #045a8d;
            border-color: #045a8d;
        }

        .btn.btn-primary--green[data-v-55ea226e] {
            background-color: #15835c;
            border-color: #15835c;
        }

        .btn.btn-primary--green[data-v-55ea226e]:hover {
            background-color: #147642;
            border-color: #147642;
        }

        .btn.btn-disabled[data-v-55ea226e] {
            opacity: 0.65;
            pointer-events: none;
        }

        .btn.btn-link[data-v-55ea226e] {
            display: inline-block;
            height: auto;
            background: none;
            border: none;
            color: #0567a7;
            padding: 0;
            font-weight: 600;
        }

        .btn.btn-secondary[data-v-55ea226e] {
            background-color: #ffffff;
            border: solid 2px #eeeeee;
        }

        .btn.btn-secondary--gray[data-v-55ea226e] {
            border-color: #eeeeee;
            background-color: #eeeeee;
            color: #333333;
        }

        .btn.btn-secondary--gray[data-v-55ea226e]:hover {
            background-color: #d5d4d4;
            border-color: #d5d4d4;
        }

        .btn.btn-secondary--black[data-v-55ea226e] {
            border: solid 2px #eeeeee;
            background-color: #fff;
            color: #333;
        }

        .btn.btn-secondary[data-v-55ea226e]:hover {
            background-color: #ececec;
            border-color: #e6e6e6;
            color: #212529;
        }

        .btn.btn-danger[data-v-55ea226e] {
            color: #fff;
            background-color: #e8422f;
        }

        .btn.btn-danger[data-v-55ea226e]:hover {
            color: #fff;
        }

        .btn.btn-no-transform[data-v-55ea226e] {
            text-transform: none;
        }

        .content-wrapper[data-v-e2113d11] {
            font-size: 12px;
            line-height: 1.67;
            z-index: 500;
            display: none;
            font-weight: 600;
            font-stretch: normal;
            font-style: normal;
            letter-spacing: normal;
            color: #333;
            box-shadow: 0 2px 4px 0 rgba(51, 51, 51, 0.12);
            padding: 11px 12px;
            max-width: 282px;
            white-space: normal;
            background-color: #fff;
            border: 1px solid #eee;
        }

        .content-wrapper .popover-arrow[data-v-e2113d11],
        .content-wrapper .popover-arrow[data-v-e2113d11]::before {
            position: absolute;
            width: 8px;
            height: 8px;
            background: inherit;
        }

        .content-wrapper .popover-arrow[data-v-e2113d11] {
            visibility: hidden;
        }

        .content-wrapper .popover-arrow[data-v-e2113d11]::before {
            visibility: visible;
            content: "";
            transform: rotate(45deg);
        }

        .content-wrapper[data-show][data-v-e2113d11] {
            display: block;
        }

        .content-wrapper[data-popper-placement^=top] > .popover-arrow[data-v-e2113d11] {
            bottom: -4px;
        }

        .content-wrapper[data-popper-placement^=top] > .popover-arrow[data-v-e2113d11]::before {
            border-right: 1px solid #eee;
            border-bottom: 1px solid #eee;
        }

        .content-wrapper[data-popper-placement^=left] > .popover-arrow[data-v-e2113d11]::before {
            border-right: 1px solid #eee;
            border-top: 1px solid #eee;
        }

        .content-wrapper[data-popper-placement^=right] > .popover-arrow[data-v-e2113d11]::before {
            border-left: 1px solid #eee;
            border-bottom: 1px solid #eee;
        }

        .content-wrapper[data-popper-placement^=bottom] > .popover-arrow[data-v-e2113d11] {
            top: -4px;
        }

        .content-wrapper[data-popper-placement^=bottom] > .popover-arrow[data-v-e2113d11]::before {
            border-left: 1px solid #eee;
            border-top: 1px solid #eee;
        }

        .content-wrapper[data-popper-placement^=left] > .popover-arrow[data-v-e2113d11] {
            right: -4px;
        }

        .content-wrapper[data-popper-placement^=right] > .popover-arrow[data-v-e2113d11] {
            left: -4px;
        }

        .tooltip-wrapper-inline[data-v-b905ec3a] {
            display: inline;
        }

        .tooltip-wrapper-flex[data-v-b905ec3a] {
            display: flex;
        }

        .igao-spinner[data-v-9d02a4c1] {
            width: 16px;
            height: 16px;
            border-width: 0.2em;
            display: inline-block;
            vertical-align: text-bottom;
            border: 2px solid currentColor;
            border-right-color: transparent;
            border-radius: 50%;
            animation: spinner-border-9d02a4c1 0.75s linear infinite;
        }

        @keyframes spinner-border-9d02a4c1 {
            to {
                transform: rotate(360deg);
            }
        }

        .modal-popup[data-v-cd0da137] {
            position: fixed;
            z-index: 600;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.48);
        }

        .modal-popup--without-header .modal-popup-body[data-v-cd0da137] {
            padding-top: 24px;
        }

        .modal-popup--animated-hide[data-v-cd0da137] {
            transition: background-color 0.4s linear;
            background-color: rgba(36, 36, 36, 0);
        }

        .modal-popup--animated-hide .modal-popup-content[data-v-cd0da137] {
            animation-name: animatedhide-cd0da137;
            animation-duration: 0.4s;
        }

        .modal-popup--animated-show[data-v-cd0da137] {
            transition: all 0.4s linear;
        }

        .modal-popup--animated-show .modal-popup-content[data-v-cd0da137] {
            animation-name: animatedshow-cd0da137;
            animation-duration: 0.4s;
        }

        .modal-popup .modal-popup-footer[data-v-cd0da137] {
            gap: 12px;
        }

        .modal-popup .modal-popup-footer[data-v-cd0da137] .btn {
            height: 40px;
        }

        .modal-popup .modal-popup-footer[data-v-cd0da137] .btn.btn-secondary {
            color: #333;
        }

        .modal-popup-content[data-v-cd0da137] {
            font-size: 14px;
            line-height: 1.57;
            position: relative;
            margin: auto;
            top: 50%;
            transform: translateY(-50%);
            background-color: #fff;
            max-width: calc(100vw - 32px);
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            letter-spacing: normal;
            color: #4a4a4a;
        }

        @media (min-width: 960px) {
            .modal-popup-content[data-v-cd0da137] {
                max-width: 400px;
            }
        }

        @keyframes animatedshow-cd0da137 {
            from {
                top: 40%;
                opacity: 0;
            }
            to {
                top: 50%;
                opacity: 1;
            }
        }

        @keyframes animatedhide-cd0da137 {
            from {
                top: 50%;
                opacity: 1;
            }
            to {
                top: 40%;
                opacity: 0;
            }
        }

        .modal-popup-close[data-v-cd0da137] {
            display: flex;
            align-items: center;
        }

        .modal-popup-close--alternative[data-v-cd0da137] {
            position: absolute;
            right: 24px;
            top: 18px;
        }

        @media (max-width: 960px) {
            .modal-popup-close--alternative[data-v-cd0da137] {
                right: 16px;
            }
        }

        .modal-popup-close[data-v-cd0da137]:hover,
        .modal-popup-close[data-v-cd0da137]:focus {
            cursor: pointer;
            opacity: 0.8;
        }

        .modal-popup-header[data-v-cd0da137] {
            padding: 16px 24px;
            display: flex;
        }

        @media (max-width: 960px) {
            .modal-popup-header[data-v-cd0da137] {
                padding: 16px;
            }
        }

        .modal-popup-header--primary[data-v-cd0da137] {
            font-size: 20px;
            line-height: 1.5;
            padding-bottom: 0;
            letter-spacing: -0.1px;
        }

        .modal-popup-header--secondary[data-v-cd0da137] {
            font-size: 16px;
            line-height: 1.5;
            letter-spacing: -0.09px;
            border-bottom: 1px solid #eee;
        }

        .modal-popup-header-title[data-v-cd0da137] {
            width: 100%;
            font-weight: 600;
            color: #333;
            padding-right: 22px;
        }

        .modal-popup-body[data-v-cd0da137] {
            padding: 16px 24px 0;
        }

        @media (max-width: 960px) {
            .modal-popup-body[data-v-cd0da137] {
                padding: 16px 16px 0;
            }
        }

        .modal-popup-footer[data-v-cd0da137] {
            display: flex;
            justify-content: flex-end;
            padding: 24px;
        }

        @media (max-width: 960px) {
            .modal-popup-footer[data-v-cd0da137] {
                padding: 16px;
            }
        }

        .account-buttons[data-v-f9a5b2be] .btn.btn {
            font-size: 16px;
            line-height: 1.5;
            border: none;
            font-weight: bold;
            padding: 3px 12px;
            height: auto;
        }

        .account-buttons[data-v-f9a5b2be] .btn.link-style {
            border: 0;
            background: none;
            color: #242424;
            padding-left: 0;
            padding-right: 0;
        }

        .account-buttons[data-v-f9a5b2be] .btn.link-style:hover {
            opacity: 0.7;
            background: none;
        }

        .account-buttons[data-v-f9a5b2be] .btn.link-style.become-coach {
            color: #666666;
        }

        .igao-btn.igao-btn--full-width[data-v-0dc3f13d] {
            width: 100%;
        }

        .igao-btn.igao-btn--l[data-v-0dc3f13d] {
            height: 50px;
            padding: 15px 32px;
        }

        .igao-btn.igao-btn--xl[data-v-0dc3f13d] {
            height: 54px;
            padding: 17px 32px;
        }

        .igao-btn.igao-btn--m[data-v-0dc3f13d] {
            height: 40px;
            padding: 10px 20px;
        }

        .igao-btn.igao-btn--s[data-v-0dc3f13d] {
            height: 32px;
            padding: 6px 16px;
        }

        .igao-spinner[data-v-9d02a4c1] {
            width: 16px;
            height: 16px;
            border-width: 0.2em;
            display: inline-block;
            vertical-align: text-bottom;
            border: 2px solid currentColor;
            border-right-color: transparent;
            border-radius: 50%;
            animation: spinner-border-9d02a4c1 0.75s linear infinite;
        }

        @keyframes spinner-border-9d02a4c1 {
            to {
                transform: rotate(360deg);
            }
        }

        .btn[data-v-55ea226e] {
            font-size: 12px;
            line-height: 1.67;
            border-radius: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #0567a7;
            position: relative;
            padding: 6px 15px;
            font-weight: bold;
            text-transform: uppercase;
            border: none;
            white-space: nowrap;
        }

        .btn.btn-primary[data-v-55ea226e] {
            color: #ffffff;
            background-color: #0567a7;
        }

        .btn.btn-primary[data-v-55ea226e]:hover {
            background-color: #045a8d;
            border-color: #045a8d;
        }

        .btn.btn-primary--green[data-v-55ea226e] {
            background-color: #15835c;
            border-color: #15835c;
        }

        .btn.btn-primary--green[data-v-55ea226e]:hover {
            background-color: #147642;
            border-color: #147642;
        }

        .btn.btn-disabled[data-v-55ea226e] {
            opacity: 0.65;
            pointer-events: none;
        }

        .btn.btn-link[data-v-55ea226e] {
            display: inline-block;
            height: auto;
            background: none;
            border: none;
            color: #0567a7;
            padding: 0;
            font-weight: 600;
        }

        .btn.btn-secondary[data-v-55ea226e] {
            background-color: #ffffff;
            border: solid 2px #eeeeee;
        }

        .btn.btn-secondary--gray[data-v-55ea226e] {
            border-color: #eeeeee;
            background-color: #eeeeee;
            color: #333333;
        }

        .btn.btn-secondary--gray[data-v-55ea226e]:hover {
            background-color: #d5d4d4;
            border-color: #d5d4d4;
        }

        .btn.btn-secondary--black[data-v-55ea226e] {
            border: solid 2px #eeeeee;
            background-color: #fff;
            color: #333;
        }

        .btn.btn-secondary[data-v-55ea226e]:hover {
            background-color: #ececec;
            border-color: #e6e6e6;
            color: #212529;
        }

        .btn.btn-danger[data-v-55ea226e] {
            color: #fff;
            background-color: #e8422f;
        }

        .btn.btn-danger[data-v-55ea226e]:hover {
            color: #fff;
        }

        .btn.btn-no-transform[data-v-55ea226e] {
            text-transform: none;
        }</style>
    <div class="auth-page-title" data-v-3a966b0f="">
        Đăng nhập
    </div>
    <div class="auth-form-container" data-v-94350294="">
        <form class="auth-form"
              wire:submit="login"
              data-v-94350294="">
            @if($loginError)
                <div class="auth-form-error mb-4" data-v-cefafc1c="">
                    {{ $errors->first('login') }}
                </div>
            @endif
            <label data-v-94350294="">Địa chỉ email</label>
            <div class="" data-v-94350294="" data-v-b905ec3a="">
                <div
                    class="igao-text-input igao-text-input--size-l @error('email') auth-form-field-with-error @enderror"
                    data-v-94350294="" data-v-a862fb29="">
                    <div class="igao-text-input-wrapper" data-v-a862fb29="">
                        <input
                            class="@error('email') auth-form-field-with-error @enderror"
                            wire:model="email" name="username" type="email"
                            placeholder="Email"
                            autocomplete="off"
                            data-v-a862fb29>
                    </div>
                    @error('email')
                    <span class="auth-form-field-error" data-v-94350294="" role="alert">{{$message}}</span>
                    @enderror
                </div><!--]-->
            </div>
            <label data-v-94350294="">
                <div class="login-page-password-label">
                    <span>Mật khẩu</span>
                    {{--                    <a href="/en/password-reset">Forgot your password?</a>--}}
                </div>
            </label>
            <div class="" data-v-94350294="" data-v-b905ec3a=""><!--[-->
                <div class="igao-text-input igao-text-input--size-l @error('password') auth-form-field-with-error @enderror"
                     data-v-94350294="" data-v-a862fb29="">
                    <!---->
                    <div class="igao-text-input-wrapper" data-v-a862fb29="">
                        <input wire:model="password" name="password"
                               class="@error('password') auth-form-field-with-error @enderror"
                               type="password"
                               x-ref="password"
                               placeholder="Password"
                               autocomplete="off"
                               data-v-a862fb29="">
                    </div>
                </div><!--]-->
                @error('password')
                    <span class="auth-form-field-error" data-v-94350294="" role="alert">{{$message}}</span>
                @enderror
            </div>
            <button class="btn btn-primary igao-btn igao-btn--m auth-form-submit" size="m" fullwidth="false"
                    data-testid="sign-in-submit" data-v-94350294="" data-v-0dc3f13d="" data-v-55ea226e="">Đăng nhập
            </button>
            <a
                class="auth-form-bottom-link igao-link igao-link--primary auth-form-bottom-link"
                href="{{route('signup')}}" wire:navigate
                data-v-146e71b1="" data-v-bb39e5c0="">Tôi không có tài khoản</a>
        </form>
    </div>

</div>

