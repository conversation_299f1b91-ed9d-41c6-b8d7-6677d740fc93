<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" xmlns:livewire="http://www.w3.org/1999/html">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    {!! SEO::generate() !!}

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

    <style id="hide-flexible-group-elements">
        [data-ab-test-group='FLEXIBLE'] {
            display: none !important;
        }
    </style>
    <style id="hide-original-group-elements">
        [data-ab-test-group='ORIGINAL'] {
            display: none !important;
        }
    </style>
    <style>
        .separator-container[data-astro-cid-tt5b4lgn] {
            margin-top: 48px;
            width: 100%;
            height: 1px
        }

        @media (min-width: 960px) {
            .separator-container[data-astro-cid-tt5b4lgn] {
                margin-top: 80px
            }
        }

        .separator[data-astro-cid-tt5b4lgn] {
            position: absolute;
            left: 0;
            right: 0;
            height: 0;
            border: none;
            border-top: 2px solid #eee;
            margin: 0;
            overflow: visible
        }

        .separator[data-astro-cid-tt5b4lgn]::after {
            content: "";
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            height: 400px;
            background-image: linear-gradient(to top, #fbfbfb, #f8f8f8);
            z-index: -1
        }

        .cta-buttons[data-v-309ef531] {
            margin-top: 48px;
            min-height: 50px;
        }

        @media (min-width: 960px) {
            .cta-buttons[data-v-309ef531] {
                margin-top: 80px;
            }
        }

        @media (max-width: 960px) {
            .cta-buttons[data-v-309ef531] .btn {
                width: 100%;
            }
        }

        .cta-buttons[data-v-309ef531] .btn + .btn {
            margin-top: 12px;
        }

        @media (min-width: 960px) {
            .cta-buttons[data-v-309ef531] .btn + .btn {
                margin-top: 0;
                margin-left: 24px;
            }
        }

        .benefits-section__title[data-v-662a6045] {
            margin-bottom: 32px;
        }

        @media (min-width: 960px) {
            .benefits-section__title[data-v-662a6045] {
                margin-bottom: 64px;
            }
        }

        .benefits-section__steps[data-v-662a6045] {
            margin: 0 auto 32px;
        }

        @media (min-width: 960px) {
            .benefits-section__steps[data-v-662a6045] {
                margin: 0 auto 64px;
                max-width: 720px;
            }
        }

        .benefits-section__step[data-v-662a6045] {
            display: flex;
            justify-content: flex-start;
            align-items: center;
        }

        .benefits-section__step[data-v-662a6045]:not(:last-child) {
            margin-bottom: 40px;
        }

        .benefits-section__step__image[data-v-662a6045] {
            width: 48px;
            height: auto;
        }

        @media (min-width: 960px) {
            .benefits-section__step__image[data-v-662a6045] {
                margin-left: 20px;
            }
        }

        .benefits-section__step__text[data-v-662a6045] {
            font-size: 20px;
            line-height: 1.5;
            text-align: left;
            margin-left: 24px;
            margin-bottom: 0;
            font-weight: 400;
        }

        @media (min-width: 960px) {
            .benefits-section__step__text[data-v-662a6045] {
                letter-spacing: -0.1px;
                color: #333333;
            }
        }

        .with-margin[data-v-b1c503cc] {
            margin-bottom: 16px;
        }

        .cta-buttons[data-v-b1c503cc] {
            margin-top: 26px;
        }

        @media (min-width: 960px) {
            .cta-buttons[data-v-b1c503cc] {
                margin-top: 40px;
            }
        }

        img[data-v-b1c503cc] {
            width: 60px;
            height: 46px;
        }

        h2[data-v-b1c503cc] {
            margin: 24px auto 16px auto;
            max-width: 720px;
        }

        p[data-v-b1c503cc] {
            font-size: 18px;
            line-height: 1.56;
            letter-spacing: -0.1px;
            text-align: center;
            color: #4a4a4a;
            margin: 16px 0 0;
        }

        [data-v-b1c503cc] .btn-container {
            padding-top: 24px;
        }

        @media (min-width: 960px) {
            [data-v-b1c503cc] .btn-container {
                padding-top: 40px;
            }
        }

        div.faq-accordion[data-v-39ed3119] {
            margin-top: 48px;
        }

        @media (min-width: 960px) {
            div.faq-accordion[data-v-39ed3119] {
                margin-top: 80px;
            }
        }

        .header[data-v-e1a11888] {
            letter-spacing: -0.1px;
            text-align: center;
            color: #333333;
            white-space: break-spaces;
        }

        .step[data-v-e1a11888]:not(:last-of-type) {
            margin-bottom: 48px;
        }

        @media (min-width: 960px) {
            .step[data-v-e1a11888]:not(:last-of-type) {
                margin-bottom: 80px;
            }
        }

        .step h3[data-v-e1a11888] {
            max-width: 800px;
            margin: 0 auto 16px;
            font-size: 28px;
            line-height: 1.36;
            letter-spacing: -0.09px;
            text-align: center;
            color: #333333;
        }

        .step p[data-v-e1a11888] {
            max-width: 800px;
            font-size: 18px;
            line-height: 1.56;
            letter-spacing: -0.1px;
            text-align: center;
            color: #4a4a4a;
            margin: 16px auto 32px auto;
        }

        @media (min-width: 960px) {
            .step p[data-v-e1a11888] {
                margin: 16px auto 64px auto;
            }
        }

        .step--first[data-v-e1a11888] {
            margin-top: 48px;
        }

        @media (min-width: 960px) {
            .step--first[data-v-e1a11888] {
                margin-top: 80px;
            }
        }

        .step-image[data-v-e1a11888] {
            max-width: 640px;
            max-height: 400px;
            width: 100%;
            height: auto;
            -o-object-fit: contain;
            object-fit: contain;
            margin: 0;
        }

        .last-step-image-container[data-v-e1a11888] {
            position: relative;
            display: inline-block;
        }

        .last-step-image-container .company-logo[data-v-e1a11888] {
            position: absolute;
            right: 13%;
            top: 13%;
            transform: translate(50%, -50%) rotate(23deg) scale(var(--1799708e));
        }

        footer[data-astro-cid-mtxgg6pp] {
            padding: 48px 0 48px;
            background: #242424;
            box-shadow: 0 50vh 0 50vh #242424;
            color: #ffffff;
            font-size: 16px
        }

        @media (max-width: 960px) {
            footer[data-astro-cid-mtxgg6pp] {
                font-size: 14px;
                padding-bottom: 32px
            }
        }

        footer[data-astro-cid-mtxgg6pp] a[data-astro-cid-mtxgg6pp] {
            color: inherit
        }

        .links-container[data-astro-cid-mtxgg6pp] {
            max-width: 995px;
            padding: 0 20px;
            display: grid;
            grid-template-columns:1fr 1fr auto;
            row-gap: 32px;
            margin: auto
        }

        @media (max-width: 960px) {
            .links-container[data-astro-cid-mtxgg6pp] {
                grid-template-columns:1fr;
                row-gap: 16px
            }
        }

        .footer-group[data-astro-cid-mtxgg6pp] {
            display: flex;
            flex-direction: column
        }

        .footer-heading[data-astro-cid-mtxgg6pp] {
            text-transform: uppercase;
            font-weight: 600;
            margin-bottom: 16px
        }

        @media (max-width: 960px) {
            .footer-heading[data-astro-cid-mtxgg6pp] {
                margin-top: 12px
            }
        }

        .footer-link[data-astro-cid-mtxgg6pp] {
            margin-bottom: 12px;
            line-height: 26px;
            opacity: 0.8
        }

        .footer-link[data-astro-cid-mtxgg6pp]:hover {
            opacity: 0.5
        }

        .footer-copyright[data-astro-cid-mtxgg6pp] {
            margin: auto;
            max-width: 995px;
            padding: 0 20px;
            margin-top: 36px;
            font-size: 12px;
            opacity: 0.8
        }

        @media (max-width: 960px) {
            .footer-copyright[data-astro-cid-mtxgg6pp] {
                margin-top: 20px
            }
        }

        .footer-copyright[data-astro-cid-mtxgg6pp] a[data-astro-cid-mtxgg6pp]:hover {
            opacity: 0.8
        }
    </style>
    <link rel="stylesheet" href="{{asset('css/app/login.tWK6PV8y.css')}}">
    <style>
        span[data-v-fdb2a95c] {
            font-size: 14px;
            line-height: 1.57;
            text-transform: uppercase;
            font-weight: bold;
            position: absolute;
        }

        @font-face {
            font-family: "Open Sans";
            font-style: italic;
            font-weight: 400;
            font-stretch: 100%;
            font-display: swap;
            src: url(fonts/open-sans-400-italic.woff2) format("woff2");
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD
        }

        @font-face {
            font-family: "Open Sans";
            font-style: normal;
            font-weight: 400;
            font-stretch: 100%;
            font-display: swap;
            src: url(fonts/open-sans-400.woff2) format("woff2");
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD
        }

        @font-face {
            font-family: "Open Sans";
            font-style: normal;
            font-weight: 600;
            font-stretch: 100%;
            font-display: swap;
            src: url(fonts/open-sans-600.woff2) format("woff2");
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD
        }

        @font-face {
            font-family: "Open Sans";
            font-style: normal;
            font-weight: 700;
            font-stretch: 100%;
            font-display: swap;
            src: url(fonts/open-sans-700.woff2) format("woff2");
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD
        }

        body {
            font-size: 18px;
            line-height: 1.56;
            font-family: "Open Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            font-weight: 400;
            color: #4a4a4a;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.004)
        }
    </style>
    <link rel="stylesheet" href="{{asset('css/app/index.B-B9bo9f.css')}}">
    <style>
        .social-proof-wrapper {
            font-size: 14px;
            line-height: 1.57;
            font-weight: 600;
            text-align: center;
            max-width: none;
            width: 100%;
            display: grid;
            grid-template-rows: auto auto;
            gap: 16px;
        }

        .social-proof-wrapper span {
            text-decoration: underline;
        }

        .social-proof-wrapper img {
            margin: auto;
        }

        .social-proof-separator {
            margin: 0 4px;
            text-decoration: none !important;
        }

        @media (max-width: 960px) {
            .social-proof-separator {
                width: 0;
                overflow: hidden;
            }
        }

        .social-proof,
        .social-proof-line {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
        }

        .social-proof-tooltip {
            display: flex;
            margin-left: 8px;
        }

        @keyframes fadeInDown-edc9e39b {
            0% {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeOutDown-edc9e39b {
            0% {
                opacity: 1;
                transform: translateY(0);
            }
            to {
                opacity: 0;
                transform: translateY(20px);
            }
        }

        .hero-section-carousel-container {
            position: relative;
            margin-bottom: 16px;
        }

        .hero-section-carousel-item {
            color: #0567a7;
            display: inline-block;
            left: 0;
            margin-left: auto;
            margin-right: auto;
            opacity: 0;
            position: absolute;
            right: 0;
        }

        .hero-section-carousel-item--placeholder {
            position: static;
        }

        .hero-section-carousel-item--fade-in {
            animation: fadeInDown-edc9e39b 0.2s ease-in;
            opacity: 1;
            will-change: opacity;
        }

        .hero-section-carousel-item--fade-out {
            animation: fadeOutDown-edc9e39b 0.2s ease-in;
            opacity: 0;
            will-change: opacity;
        }

        .header {
            position: relative;
            color: #333333;
            margin-bottom: 16px;
            letter-spacing: -0.4px;
            /*white-space: break-spaces;*/
        }

        @media (min-width: 960px) {
            .header {
                margin: 0 auto 16px;
            }
        }

        .new-badge--desktop[data-v-edc9e39b] {
            transform: translateY(-50%);
            display: none;
        }

        @media (min-width: 960px) {
            .new-badge--desktop[data-v-edc9e39b] {
                display: inline-block;
            }
        }

        .new-badge--on-first-line[data-v-edc9e39b] {
            transform: none;
            margin-left: 6px;
        }

        .new-badge--mobile[data-v-edc9e39b] {
            position: static;
            width: 45px;
            margin: auto;
            display: block;
        }

        @media (min-width: 960px) {
            .new-badge--mobile[data-v-edc9e39b] {
                display: none;
            }
        }

        .subheader {
            color: #4a4a4a;
            max-width: 880px;
            font-size: 18px;
            line-height: 1.56;
            letter-spacing: -0.1px;
            margin: auto;
            margin-bottom: 32px;
            white-space: break-spaces;
            position: relative;
        }

        @media (min-width: 960px) {
            .subheader {
                margin-bottom: 40px;
            }
        }

        .account-buttons[data-astro-cid-hmtt24ci] {
            display: flex;
            flex-direction: column;
            width: 100%
        }

        .account-buttons[data-astro-cid-hmtt24ci] a.btn:not(:last-child) {
            margin-bottom: 12px
        }

        .mobile-navigation-container[data-astro-cid-hmtt24ci] {
            display: none;
            position: fixed;
            top: 0;
            width: 100vw;
            left: 0;
            flex-direction: column;
            align-items: start;
            background: #ffffff;
            z-index: 400;
            padding: 86px 24px 156px;
            height: 100vh;
            overflow: auto
        }

        .mobile-navigation-container[data-astro-cid-hmtt24ci].open {
            display: flex
        }

        nav {
            flex-grow: 1;
            display: flex
        }

        .fake-header[data-astro-cid-xbstl6g3] {
            display: flex;
            min-height: 70px;
            width: 100%
        }

        .mobile-sub-navigation[data-astro-cid-xbstl6g3] {
            height: 58px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #fff;
            border-bottom: 2px solid #eeeeee;
            color: #4a4a4a
        }

        @media (min-width: 1200px) {
            .mobile-sub-navigation[data-astro-cid-xbstl6g3] {
                display: none
            }
        }

        .mobile-sub-navigation[data-astro-cid-xbstl6g3] a[data-astro-cid-xbstl6g3].navigation-link {
            position: relative;
            box-sizing: border-box;
            text-decoration: none;
            font-weight: 600;
            cursor: pointer;
            color: #000;
            white-space: nowrap;
            font-size: 14px
        }

        .mobile-sub-navigation[data-astro-cid-xbstl6g3] a[data-astro-cid-xbstl6g3].navigation-link.active {
            color: #0567a7
        }

        .mobile-sub-navigation[data-astro-cid-xbstl6g3] a[data-astro-cid-xbstl6g3].navigation-link:not(:last-child) {
            margin-right: 16px
        }

        header {
            position: fixed;
            width: 100%;
            box-sizing: border-box;
            height: 70px;
            border-bottom: 6px solid #eeeeee;
            background-color: #ffffff;
            padding: 10px 24px;
            display: flex;
            z-index: 500
        }

        @media (min-width: 1200px) {
            header {
                padding: 10px 64px
            }
        }

        header .header-container {
            display: flex;
            width: 100%;
            align-items: center;
            margin: auto
        }

        header .logo {
            display: flex
        }

        header .logo > a {
            display: flex
        }

        .faq-accordion[data-v-7b9c232d] {
            font-size: 16px;
            line-height: 1.5;
            margin-top: 20px;
            width: 100%;
            display: flex;
            flex-direction: column;
            color: #000;
        }

        .accordion-item-head[data-v-7b9c232d],
        .accordion-item-description[data-v-7b9c232d] {
            transition: all 0.2s linear;
        }

        .accordion-item + .accordion-item[data-v-7b9c232d] {
            margin-top: 24px;
        }

        .accordion-item--open .accordion-item-description[data-v-7b9c232d] {
            margin-top: 14px;
        }

        .accordion-item-head[data-v-7b9c232d] {
            font-size: 20px;
            line-height: 1.5;
            padding: 0;
            color: #242424;
            background: none;
            border: none;
            width: 100%;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
            text-align: left;
            cursor: pointer;
        }

        .accordion-item-description[data-v-7b9c232d] {
            font-size: 16px;
            line-height: 1.5;
            overflow: hidden;
            height: 0px;
            text-align: left;
        }

        .accordion-item-description[data-v-7b9c232d] p:first-child,
        .accordion-item-description[data-v-7b9c232d] ul:first-child,
        .accordion-item-description[data-v-7b9c232d] ol:first-child {
            margin-top: 0;
        }

        .accordion-item-description[data-v-7b9c232d] p:last-child,
        .accordion-item-description[data-v-7b9c232d] ul:last-child,
        .accordion-item-description[data-v-7b9c232d] ol:last-child {
            margin-bottom: 0;
        }

        .igao-box[data-v-253e5cdb] {
            border: solid 2px #eeeeee;
            padding: 32px 16px;
            width: 100%;
        }

        @media (min-width: 960px) {
            .igao-box[data-v-253e5cdb] {
                padding: 36px 80px 48px;
            }
        }

        nav[data-astro-cid-d77rco4z].landing-page-sub-navigation {
            width: 100%;
            border-bottom: 2px solid #eeeeee;
            height: 60px;
            padding: 0 64px;
            align-items: center;
            background-color: #fff;
            display: none
        }

        @media (min-width: 1200px) {
            nav[data-astro-cid-d77rco4z].landing-page-sub-navigation {
                display: flex
            }
        }

        nav[data-astro-cid-d77rco4z].landing-page-sub-navigation .nav-item, nav[data-astro-cid-d77rco4z].landing-page-sub-navigation .menu-item {
            font-weight: 600;
            color: #4a4a4a
        }

        nav[data-astro-cid-d77rco4z].landing-page-sub-navigation .nav-item.active, nav[data-astro-cid-d77rco4z].landing-page-sub-navigation .menu-item.active {
            color: #0567a7
        }

        .cta-buttons[data-v-32ab0a0a] {
            margin-top: 26px;
        }

        @media (min-width: 960px) {
            .cta-buttons[data-v-32ab0a0a] {
                margin-top: 40px;
            }
        }

        .step-image[data-v-8ee13508] {
            max-width: 640px;
            max-height: 400px;
            width: 100%;
            height: auto;
            -o-object-fit: contain;
            object-fit: contain;
            margin: 0;
        }

        .igao-btn.igao-btn--full-width[data-v-0dc3f13d] {
            width: 100%;
        }

        .igao-btn.igao-btn--l[data-v-0dc3f13d] {
            height: 50px;
            padding: 15px 32px;
        }

        .igao-btn.igao-btn--xl[data-v-0dc3f13d] {
            height: 54px;
            padding: 17px 32px;
        }

        .igao-btn.igao-btn--m[data-v-0dc3f13d] {
            height: 40px;
            padding: 10px 20px;
        }

        .igao-btn.igao-btn--s[data-v-0dc3f13d] {
            height: 32px;
            padding: 6px 16px;
        }

        h2[data-astro-cid-hc2l2ojz] {
            white-space: break-spaces;
            margin-bottom: 32px
        }

        @media (min-width: 960px) {
            h2[data-astro-cid-hc2l2ojz] {
                margin-bottom: 64px
            }
        }

        .grid-container[data-v-d6c5d653] {
            margin-top: 48px;
            display: grid;
            gap: 16px;
            width: 100%;
            grid-template-columns: repeat(auto-fit, minmax(288px, 338px));
            justify-content: center;
        }

        @media (min-width: 960px) {
            .grid-container[data-v-d6c5d653] {
                margin-top: 80px;
            }
        }

        @media (max-width: 960px) {
            .grid-container[data-v-d6c5d653] {
                grid-template-columns: 1fr;
            }
        }

        .link-wrapper:hover .course-container[data-v-d6c5d653] {
            border: 1px solid #0567a7;
        }

        .cards-container[data-v-d6c5d653] {
            font-size: 12px;
            line-height: 1.67;
            display: flex;
            flex-direction: column;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            letter-spacing: normal;
            padding: 20px;
            box-shadow: 0 1px 2px 0 rgba(51, 51, 51, 0.12);
            border: 1px solid rgba(0, 0, 0, 0);
            background-color: #fff;
            text-align: left;
            height: 100%;
        }

        .card-container__short-title[data-v-d6c5d653] {
            color: #9e6a1b;
            text-transform: uppercase;
        }

        .card-container__title[data-v-d6c5d653] {
            font-size: 18px;
            line-height: 1.56;
            letter-spacing: -0.1px;
            color: #333;
            margin-top: 3px;
            height: 100%;
        }

        .card-container__action[data-v-d6c5d653] {
            margin-top: 20px;
            color: #0567a7;
            text-transform: uppercase;
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }

        .card-container__action > svg[data-v-d6c5d653] {
            margin-left: 8px;
            height: 8px;
            width: 4px;
            fill: #0567a7;
        }

        .section-wrapper {
            max-width: 1080px;
            margin: auto;
            padding: 0 16px;
            text-align: center;
            margin-top: 48px;
        }

        @media (min-width: 960px) {
            .section-wrapper {
                margin-top: 80px;
            }
        }
    </style>
    <link rel="stylesheet" href="{{asset('css/app/index.FRuJeWhK.css')}}">
    <style>
        .btn[data-v-55ea226e] {
            font-size: 12px;
            line-height: 1.67;
            border-radius: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #0567a7;
            position: relative;
            padding: 6px 15px;
            font-weight: bold;
            text-transform: uppercase;
            border: none;
            white-space: nowrap;
        }

        .btn.btn-primary[data-v-55ea226e] {
            color: #ffffff;
            background-color: #0567a7;
        }

        .btn.btn-primary[data-v-55ea226e]:hover {
            background-color: #045a8d;
            border-color: #045a8d;
        }

        .btn.btn-primary--green[data-v-55ea226e] {
            background-color: #15835c;
            border-color: #15835c;
        }

        .btn.btn-primary--green[data-v-55ea226e]:hover {
            background-color: #147642;
            border-color: #147642;
        }

        .btn.btn-disabled[data-v-55ea226e] {
            opacity: 0.65;
            pointer-events: none;
        }

        .btn.btn-link[data-v-55ea226e] {
            display: inline-block;
            height: auto;
            background: none;
            border: none;
            color: #0567a7;
            padding: 0;
            font-weight: 600;
        }

        .btn.btn-secondary[data-v-55ea226e] {
            background-color: #ffffff;
            border: solid 2px #eeeeee;
        }

        .btn.btn-secondary--gray[data-v-55ea226e] {
            border-color: #eeeeee;
            background-color: #eeeeee;
            color: #333333;
        }

        .btn.btn-secondary--gray[data-v-55ea226e]:hover {
            background-color: #d5d4d4;
            border-color: #d5d4d4;
        }

        .btn.btn-secondary--black[data-v-55ea226e] {
            border: solid 2px #eeeeee;
            background-color: #fff;
            color: #333;
        }

        .btn.btn-secondary[data-v-55ea226e]:hover {
            background-color: #ececec;
            border-color: #e6e6e6;
            color: #212529;
        }

        .btn.btn-danger[data-v-55ea226e] {
            color: #fff;
            background-color: #e8422f;
        }

        .btn.btn-danger[data-v-55ea226e]:hover {
            color: #fff;
        }

        .btn.btn-no-transform[data-v-55ea226e] {
            text-transform: none;
        }

        .igao-spinner[data-v-9d02a4c1] {
            width: 16px;
            height: 16px;
            border-width: 0.2em;
            display: inline-block;
            vertical-align: text-bottom;
            border: 2px solid currentColor;
            border-right-color: transparent;
            border-radius: 50%;
            animation: spinner-border-9d02a4c1 0.75s linear infinite;
        }

        @keyframes spinner-border-9d02a4c1 {
            to {
                transform: rotate(360deg);
            }
        }

        .coach-buying-reassurance[data-v-99fd961d] {
            color: #4a4a4a;
            gap: 8px;
            display: inline-flex;
            flex-direction: column;
        }

        .coach-buying-reassurance > div[data-v-99fd961d] {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 8px;
        }

        .grid-container[data-v-1d69195c] {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 32px;
            flex-wrap: wrap;
            font-size: 16px;
            line-height: 1.5;
            margin-top: 48px;
        }

        @media (min-width: 960px) {
            .grid-container[data-v-1d69195c] {
                margin-top: 80px;
            }
        }

        @media (max-width: 960px) {
            .grid-container[data-v-1d69195c] {
                grid-template-columns: 1fr;
            }
        }

        .links-category[data-v-1d69195c] {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            text-align: left;
            font-weight: 600;
        }

        .links-category-title[data-v-1d69195c] {
            margin-bottom: 8px;
            text-transform: uppercase;
            color: #333333;
        }

        .links-category-link[data-v-1d69195c] {
            margin-top: 8px;
            font-weight: 400;
        }

        .links-category-link[data-v-1d69195c]:hover {
            opacity: 0.8;
        }

        .testimonial[data-v-e04ff970] {
            color: #4a4a4a;
            border: 1px solid rgba(158, 106, 27, 0.1882352941);
            font-size: 14px;
            line-height: 22px;
            padding: 16px;
            display: flex;
            gap: 8px;
            align-items: flex-start;
            flex-direction: column;
            text-align: left;
            background: rgba(255, 232, 192, 0.4);
        }

        .testimonial-header[data-v-e04ff970] {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 4px;
            font-weight: 600;
        }

        .testimonial-header img[data-v-e04ff970] {
            border-radius: 50%;
        }

        .testimonial-body[data-v-e04ff970] {
            width: 100%;
            word-break: break-word;
        }

        .testimonial-body[data-v-e04ff970] p {
            margin: 0;
        }

        .testimonial-body[data-v-e04ff970] p + p {
            margin-top: 14px;
        }

        .testimonial-footer[data-v-e04ff970] {
            font-weight: 600;
            color: #666666;
        }

        .testimonial-footer a[data-v-e04ff970] {
            font-style: italic;
        }

        .testimonial-companies[data-v-e04ff970] {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .testimonials-container[data-v-499e8952] {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(288px, 1fr));
            width: 100%;
            gap: 16px;
        }

        .testimonials-load-more-btn[data-v-499e8952] {
            margin-top: 48px;
        }

        @media (min-width: 960px) {
            .testimonials-load-more-btn[data-v-499e8952] {
                margin-top: 80px;
            }
        }

        .company-label[data-v-9f120dbc] {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            padding: 2px 4px;
            gap: 4px;
            background-color: #fff;
        }
    </style>
    <link rel="stylesheet" href="{{asset('css/app/index.DIF_8FKq.css')}}">
    <style>
        .spinner-container[data-v-15ddf44a] {
            text-align: center;
            position: relative;
            min-height: 132px;
            height: 100%;
            padding: 16px 0;
        }

        .spinner-container .loading-coaches-spinner[data-v-15ddf44a] {
            position: absolute;
            top: calc(50% - 50px);
            left: calc(50% - 50px);
            color: #0567a7;
            width: var(--spinner-size, 100px);
            height: var(--spinner-size, 100px);
            border-width: 4px;
        }

        .coach-card-placeholder[data-v-8a4b21a9] {
            padding: 20px;
            box-shadow: 0 1px 2px 0 rgba(51, 51, 51, 0.04);
            border: solid 1px #eeeeee;
            background-color: #fff;
            height: 100%;
            display: flex;
            flex-direction: column;
            text-align: left;
            position: relative;
            overflow: hidden;
        }

        .coach-card-placeholder .coach-card-placeholder-loader[data-v-8a4b21a9] {
            position: absolute;
            top: 0;
            left: -45%;
            height: 100%;
            width: 45%;
            background-image: linear-gradient(to left, rgba(251, 251, 251, 0.05), rgba(251, 251, 251, 0.3), rgba(251, 251, 251, 0.6), rgba(251, 251, 251, 0.3), rgba(251, 251, 251, 0.05));
            animation: loading-8a4b21a9 1s infinite;
            z-index: 45;
        }

        @keyframes loading-8a4b21a9 {
            0% {
                left: -45%;
            }
            100% {
                left: 100%;
            }
        }

        .coach-card-placeholder .coach-card-placeholder-head[data-v-8a4b21a9] {
            display: flex;
            align-items: center;
            margin-bottom: 18px;
        }

        .coach-card-placeholder .coach-card-placeholder-circle[data-v-8a4b21a9] {
            display: flex;
            width: 72px;
            height: 72px;
            border-radius: 50%;
            background-color: #eeeeee;
            margin-right: 16px;
        }

        .coach-card-placeholder .coach-card-placeholder-line[data-v-8a4b21a9] {
            display: flex;
            height: 20px;
            width: 125px;
            background-color: #eeeeee;
            margin-bottom: 10px;
        }

        .coach-card-placeholder .coach-card-placeholder-line[data-v-8a4b21a9]:last-child {
            margin-bottom: 0;
        }

        .coach-card-placeholder .coach-card-placeholder-line.coach-card-placeholder-line--short[data-v-8a4b21a9] {
            width: 85px;
        }

        .coach-card-placeholder .coach-card-placeholder-line.coach-card-placeholder-line--long[data-v-8a4b21a9] {
            width: 150px;
        }

        .coach-card-placeholder .coach-card-placeholder-line.coach-card-placeholder-line--wide[data-v-8a4b21a9] {
            height: 24px;
        }

        .coach-cards-container[data-v-287d27d2] {
            margin-top: 32px;
        }

        .coach-cards-container h4[data-v-287d27d2] {
            margin-bottom: 32px;
        }

        @media (max-width: 960px) {
            .coach-cards-container[data-v-287d27d2] {
                margin-top: 24px;
            }

            .coach-cards-container h4[data-v-287d27d2] {
                margin-bottom: 24px;
            }
        }

        .coach-cards[data-v-287d27d2] {
            display: grid;
            gap: 16px;
            grid-template-columns: repeat(auto-fill, minmax(288px, 1fr));
            width: 100%;
            position: relative;
        }

        .coach-cards[data-v-287d27d2] .link-wrapper {
            transition: opacity 0.2s linear;
        }

        .coach-cards--horizontal[data-v-287d27d2] {
            display: flex;
            flex-direction: column;
        }

        .coach-cards--loading[data-v-287d27d2] .link-wrapper {
            opacity: 0.7;
        }

        .no-coach-message[data-v-287d27d2] {
            font-size: 18px;
            line-height: 1.56;
            font-weight: 600;
            grid-column: 1/-1;
            margin-top: 24px;
        }

        @media (min-width: 960px) {
            .no-coach-message[data-v-287d27d2] {
                margin-top: 48px;
            }
        }

        .spinner-wrapper[data-v-287d27d2] {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
        }

        .spinner-wrapper[data-v-287d27d2] .spinner-container {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
        }

        .browse-all-coaches-btn[data-v-63aeaf57] {
            margin-top: 32px;
        }

        @media (min-width: 960px) {
            .browse-all-coaches-btn[data-v-63aeaf57] {
                margin-top: 48px;
            }
        }

        [data-v-63aeaf57] .coach-count-spinner {
            width: 16px;
            height: 16px;
            border-width: 0.2em;
            display: inline-block;
            border: 2px solid currentColor;
            border-right-color: transparent;
            border-radius: 50%;
            animation: spinner-border-63aeaf57 0.75s linear infinite;
        }

        @keyframes spinner-border-63aeaf57 {
            to {
                transform: rotate(360deg);
            }
        }

        .igao-text-input--size-s input {
            min-height: 32px;
            max-height: 32px;
        }

        .igao-text-input--size-m input {
            min-height: 40px;
            max-height: 40px;
        }

        .igao-text-input--size-l input {
            min-height: 50px;
            max-height: 50px;
        }

        .igao-text-input-label {
            font-size: 14px;
            line-height: 1.57;
            text-transform: uppercase;
            font-weight: bold;
            color: #666666;
            margin-bottom: 8px;
        }

        @media (max-width: 960px) {
            .igao-text-input-label {
                font-size: 12px;
                line-height: 1.67;
            }
        }

        .igao-text-input {
            display: flex;
            flex-direction: column;
            text-align: left;
            position: relative;
        }

        .igao-text-input.igao-text-input--has-errors input {
            border-color: #c63120 !important;
        }

        .igao-text-input.igao-text-input--search input {
            background-color: #fff;
            background-image: url(icons/icon-search.svg);
            background-position: 16px center;
            background-repeat: no-repeat;
            background-size: 14px 14px;
            padding: 0 28px 0 40px;
        }

        .igao-text-input input {
            background-clip: padding-box;
            border: 1px solid #d5d4d4;
            border-radius: 0;
            transition: border 0.2s linear, box-shadow 0.2s linear;
            font-size: 14px;
            line-height: 1.57;
            padding: 8px 15px;
            width: 100%;
            background-color: #fff;
            color: #4a4a4a;
        }

        .igao-text-input input:focus, .igao-text-input input:active:not([disabled]), .igao-text-input input.is-active {
            border-color: #0567a7;
            box-shadow: 0 0 0 2px rgba(0, 123, 202, 0.12);
            outline: 0;
        }

        @media (max-width: 960px) {
            .igao-text-input input {
                font-size: 16px;
                line-height: 1.5;
            }
        }

        .igao-text-input input[disabled] {
            background-color: #fafafa;
            color: #aaa;
        }

        .igao-text-input-wrapper {
            position: relative;
        }

        .igao-text-input-clear {
            background: none;
            -webkit-mask-image: url("icons/remove.svg");
            mask-image: url("icons/remove.svg");
            -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
            -webkit-mask-size: contain;
            mask-size: contain;
            -webkit-mask-position: center;
            mask-position: center;
            background-color: #878787;
            width: 12px;
            height: 12px;
            border: none;
            margin: 0;
            padding: 0;
            opacity: 0.75;
            cursor: pointer;
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            -webkit-mask-size: 12px 12px;
            mask-size: 12px 12px;
            height: 20px;
            width: 20px;
        }

        .igao-text-input-clear[data-v-a862fb29]:hover {
            opacity: 1;
        }

        .availability-filter-dropdown-container[data-v-939ee512] {
            font-size: 14px;
            text-align: left;
        }

        .availability-filter-dropdown-picker[data-v-939ee512] {
            position: absolute;
            width: 100%;
            top: calc(100% + 2px);
            right: 0;
            background: #fff;
            padding: 16px;
            border: 1px solid #eeeeee;
        }

        @media (min-width: 960px) {
            .availability-filter-dropdown-picker[data-v-939ee512] {
                width: 376px;
            }
        }

        .availability-filter-dropdown[data-v-939ee512] {
            position: relative;
            background: #fff;
            border: 1px solid #d5d4d4;
            padding: 6px 16px;
            min-height: 40px;
            display: flex;
            align-items: center;
        }

        .availability-filter-dropdown--open[data-v-939ee512] {
            z-index: 7777;
        }

        .availability-filter-dropdown--open[data-v-939ee512]::after {
            rotate: 180deg;
        }

        .availability-filter-dropdown[data-v-939ee512]:focus {
            outline: 0;
            border: 1px solid #0567a7;
        }

        .availability-filter-dropdown[data-v-939ee512]::after {
            background: url("icons/icon-chevron-down.svg") no-repeat;
            position: absolute;
            content: "";
            height: 7px;
            width: 10px;
            top: 50%;
            right: 16px;
            margin-top: -2.5px;
            pointer-events: none;
            border: none;
        }

        .availability-filter-dropdown-time-period[data-v-939ee512] {
            cursor: pointer;
            background: #eeeeee;
            color: #333333;
            display: flex;
            gap: 8px;
            align-items: center;
            justify-content: center;
        }

        .availability-filter-dropdown-time-period img[data-v-939ee512] {
            filter: invert(13%) sepia(9%) saturate(5%) hue-rotate(7deg) brightness(96%) contrast(81%);
        }

        .availability-filter-dropdown-time-period--selected[data-v-939ee512] {
            background: #0567a7;
            color: #fff;
        }

        .availability-filter-dropdown-time-period--selected img[data-v-939ee512] {
            filter: invert(100%) sepia(0%) saturate(0%) hue-rotate(93deg) brightness(103%) contrast(103%);
        }

        .availability-filter-dropdown-label[data-v-939ee512] {
            font-size: 12px;
            line-height: 1.57;
            text-transform: uppercase;
            font-weight: bold;
            color: #666666;
            margin-bottom: 8px;
        }

        @media (min-width: 960px) {
            .availability-filter-dropdown-label[data-v-939ee512] {
                font-size: 14px;
            }
        }

        .availability-filter-dropdown-value[data-v-939ee512] {
            font-size: 16px;
            color: #4a4a4a;
            font-weight: 600;
            -webkit-user-select: none;
            -moz-user-select: none;
            user-select: none;
            padding-right: 16px;
        }

        @media (min-width: 960px) {
            .availability-filter-dropdown-value[data-v-939ee512] {
                font-size: 14px;
            }
        }

        .availability-filter-dropdown-picker-label[data-v-939ee512] {
            text-transform: uppercase;
            font-weight: bold;
            color: #666666;
            margin-bottom: 8px;
        }

        .availability-filter-dropdown-picker-fallback[data-v-939ee512] {
            height: 368px;
        }

        .availability-filter-dropdown-picker-text[data-v-939ee512] {
            color: #333333;
            margin-bottom: 16px;
        }

        .availability-filter-dropdown-time-periods[data-v-939ee512] {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: 36px 36px;
            gap: 16px;
        }

        .coaches-filters-container {
            display: grid;
            gap: 16px;
            margin-top: 26px;
        }

        @media (min-width: 960px) {
            .coaches-filters-container {
                margin-top: 40px;
            }
        }

        @media (max-width: 960px) {
            .coaches-filters-container {
                gap: 8px;
            }
        }

        .coaches-filters-container .choices__list--dropdown .popular-badge {
            display: inline-flex;
        }

        .coaches-filters-container .popular-badge {
            font-size: 12px;
            line-height: 1.67;
            display: none;
            margin-left: 4px;
            white-space: nowrap;
            align-self: center;
        }

        .coaches-filters-container .coach-filters-row {
            display: grid;
            gap: 16px;
        }

        @media (max-width: 960px) {
            .coaches-filters-container .coach-filters-row {
                grid-template-columns: 1fr !important;
                gap: 8px;
            }
        }

        [data-v-29743a72] .choices__group[role="group"][data-value="All"] {
            display: none;
        }

        [data-v-29743a72] .igao-text-input--focused .igao-text-input-wrapper {
            z-index: 7777;
        }

        .coaches-filter-search-input input {
            font-weight: 600;
        }

        .coaches-filter-dropdown .choices__item {
            font-weight: 600;
        }
    </style>
    <link rel="stylesheet" href="{{asset('css/app/index.CceqDBXt.css')}}">
    <style>
        .coach-profile-card-container[data-v-f24fc346] {
            position: relative;
        }

        .link-wrapper[data-v-f24fc346]:hover,
        .link-wrapper[data-v-f24fc346]:focus,
        .link-wrapper[data-v-f24fc346]:active {
            text-decoration: none;
        }

        .link-wrapper:hover .course-container[data-v-f24fc346],
        .link-wrapper:hover .coach-profile-card[data-v-f24fc346],
        .link-wrapper:focus .course-container[data-v-f24fc346],
        .link-wrapper:focus .coach-profile-card[data-v-f24fc346],
        .link-wrapper:active .course-container[data-v-f24fc346],
        .link-wrapper:active .coach-profile-card[data-v-f24fc346] {
            border: 1px solid #0567a7;
        }

        .link-wrapper--disabled[data-v-f24fc346] {
            pointer-events: none;
            opacity: 0.7;
        }

        .coach-profile-card[data-v-f24fc346] {
            position: relative;
            padding: 20px;
            box-shadow: 0 1px 2px 0 rgba(51, 51, 51, 0.04);
            border: solid 1px #eeeeee;
            background-color: #ffffff;
            height: 100%;
            display: flex;
            flex-direction: column;
            color: #333333;
            text-align: left;
        }

        .coach-profile-card__avatar-container[data-v-f24fc346] {
            position: relative;
        }

        .coach-profile-card__super-coach-title[data-v-f24fc346] {
            font-size: 10px;
            line-height: 1.8;
            font-weight: 700;
            font-stretch: normal;
            font-style: normal;
            letter-spacing: 2px;
            color: #9e6a1b;
            text-transform: uppercase;
        }

        .coach-profile-card__super-coach-badge[data-v-f24fc346] {
            position: absolute;
            top: -2px;
            right: 18px;
        }

        .coach-profile-card__header[data-v-f24fc346] {
            display: flex;
            margin-bottom: 18px;
        }

        .coach-profile-card__avatar[data-v-f24fc346] {
            max-height: 72px;
            max-width: 72px;
            margin-right: 16px;
            border-radius: 50%;
            position: relative;
            overflow: hidden;
            filter: grayscale(1);
        }

        .coach-profile-card__avatar[data-v-f24fc346]::before {
            content: "";
            width: 100%;
            height: 100%;
            background-color: #eeeeee;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .coach-profile-card__badge[data-v-f24fc346] {
            margin: 4px 0 8px;
        }

        .coach-profile-card__name-and-position[data-v-f24fc346] {
            display: flex;
            align-items: flex-start;
            flex-direction: column;
            justify-content: center;
            flex-grow: 1;
        }

        .coach-profile-card__name[data-v-f24fc346] {
            font-weight: 700;
            font-size: 20px;
            line-height: 1.2;
        }

        .coach-profile-card__position[data-v-f24fc346] {
            font-size: 14px;
            font-weight: 600;
            line-height: 1.57;
            color: #4a4a4a;
            margin-top: 4px;
        }

        .coach-profile-card__info-row[data-v-f24fc346] {
            display: flex;
            align-items: center;
            font-size: 14px;
        }

        .coach-profile-card__info-row + .coach-profile-card__info-row[data-v-f24fc346] {
            margin-top: 10px;
        }

        .coach-profile-card__info-row .new-badge[data-v-f24fc346] {
            color: #a67424;
        }

        .coach-profile-card__info-row .badge-tooltip[data-v-f24fc346] {
            display: flex;
        }

        .coach-profile-card__info-row .badge-tooltip img[data-v-f24fc346] {
            margin-right: 0;
        }

        .coach-profile-card__info-row img[data-v-f24fc346] {
            width: 16px;
            height: 16px;
            min-width: 16px;
            margin-right: 12px;
        }

        .coach-profile-card__info-row img[data-v-f24fc346]:not(.keep-color) {
            filter: invert(74%) sepia(3%) saturate(14%) hue-rotate(320deg) brightness(92%) contrast(91%);
        }

        .coach-profile-card--stub[data-v-f24fc346] {
            height: 209px;
            background-repeat: no-repeat;
            background-size: contain;
            position: relative;
        }

        .coach-profile-card__price-row[data-v-f24fc346] {
            font-size: 12px;
            line-height: 1.67;
            display: flex;
            align-items: center;
            flex-grow: 1;
            height: 100%;
            justify-content: flex-end;
            align-items: flex-end;
        }

        .coach-profile-card__price-row > div[data-v-f24fc346] {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .coach-profile-card__price-row > div > span[data-v-f24fc346]:last-child {
            font-size: 18px;
            line-height: 1.56;
            font-weight: 700;
            color: #0567a7;
        }

        .coach-badge-container[data-v-92fb9e4b] {
            display: inline-flex;
            justify-content: flex-start;
            align-items: center;
            gap: 4px;
            flex-wrap: wrap;
        }

        @media (min-width: 960px) {
            .coach-badge-container[data-v-92fb9e4b] {
                gap: 8px;
            }
        }

        .coach-badge[data-v-92fb9e4b] {
            text-transform: uppercase;
            color: #fff;
            text-align: center;
            font-size: 10px;
            font-style: normal;
            font-weight: 700;
            line-height: 14px; /* 140% */
            letter-spacing: -0.1px;
            display: inline-flex;
            padding: 3px 8px;
            justify-content: center;
            align-items: center;
        }

        .coach-badge--bogof[data-v-92fb9e4b] {
            background: #15835c;
        }

        .coach-badge--supercoach[data-v-92fb9e4b] {
            background: #9e6a1b;
        }

        .coach-badge--certified[data-v-92fb9e4b] {
            background: #666666;
        }

        .content-wrapper[data-v-e2113d11] {
            font-size: 12px;
            line-height: 1.67;
            z-index: 500;
            display: none;
            font-weight: 600;
            font-stretch: normal;
            font-style: normal;
            letter-spacing: normal;
            color: #333;
            box-shadow: 0 2px 4px 0 rgba(51, 51, 51, 0.12);
            padding: 11px 12px;
            max-width: 282px;
            white-space: normal;
            background-color: #fff;
            border: 1px solid #eee;
        }

        .content-wrapper .popover-arrow[data-v-e2113d11],
        .content-wrapper .popover-arrow[data-v-e2113d11]::before {
            position: absolute;
            width: 8px;
            height: 8px;
            background: inherit;
        }

        .content-wrapper .popover-arrow[data-v-e2113d11] {
            visibility: hidden;
        }

        .content-wrapper .popover-arrow[data-v-e2113d11]::before {
            visibility: visible;
            content: "";
            transform: rotate(45deg);
        }

        .content-wrapper[data-show][data-v-e2113d11] {
            display: block;
        }

        .content-wrapper[data-popper-placement^=top] > .popover-arrow[data-v-e2113d11] {
            bottom: -4px;
        }

        .content-wrapper[data-popper-placement^=top] > .popover-arrow[data-v-e2113d11]::before {
            border-right: 1px solid #eee;
            border-bottom: 1px solid #eee;
        }

        .content-wrapper[data-popper-placement^=left] > .popover-arrow[data-v-e2113d11]::before {
            border-right: 1px solid #eee;
            border-top: 1px solid #eee;
        }

        .content-wrapper[data-popper-placement^=right] > .popover-arrow[data-v-e2113d11]::before {
            border-left: 1px solid #eee;
            border-bottom: 1px solid #eee;
        }

        .content-wrapper[data-popper-placement^=bottom] > .popover-arrow[data-v-e2113d11] {
            top: -4px;
        }

        .content-wrapper[data-popper-placement^=bottom] > .popover-arrow[data-v-e2113d11]::before {
            border-left: 1px solid #eee;
            border-top: 1px solid #eee;
        }

        .content-wrapper[data-popper-placement^=left] > .popover-arrow[data-v-e2113d11] {
            right: -4px;
        }

        .content-wrapper[data-popper-placement^=right] > .popover-arrow[data-v-e2113d11] {
            left: -4px;
        }

        .tooltip-wrapper-inline[data-v-b905ec3a] {
            display: inline;
        }

        .tooltip-wrapper-flex[data-v-b905ec3a] {
            display: flex;
        }

        .account-buttons[data-v-f9a5b2be] .btn.btn {
            font-size: 16px;
            line-height: 1.5;
            border: none;
            font-weight: bold;
            padding: 3px 12px;
            height: auto;
        }

        .account-buttons[data-v-f9a5b2be] .btn.link-style {
            border: 0;
            background: none;
            color: #242424;
            padding-left: 0;
            padding-right: 0;
        }

        .account-buttons[data-v-f9a5b2be] .btn.link-style:hover {
            opacity: 0.7;
            background: none;
        }

        .account-buttons[data-v-f9a5b2be] .btn.link-style.become-coach {
            color: #666666;
        }

        .header-account-container[data-v-5d2dab64] {
            display: flex;
            align-items: center;
        }

        .account-buttons[data-v-5d2dab64] {
            display: none;
        }

        @media (min-width: 1200px) {
            .account-buttons[data-v-5d2dab64] {
                display: block;
            }
        }

        .account-buttons[data-v-5d2dab64] > a {
            margin-right: 32px;
        }

        .account-buttons[data-v-5d2dab64] > a:last-of-type {
            margin-right: 18px;
        }

        .mobile-menu-icon[data-v-5d2dab64] {
            width: 44px;
            height: 44px;
            align-items: center;
            justify-content: center;
            border: none;
            background: no-repeat;
            display: flex;
        }

        @media (min-width: 1200px) {
            .mobile-menu-icon[data-v-5d2dab64] {
                display: none;
            }
        }

        .mobile-menu-icon.cart-icon[data-v-5d2dab64] {
            display: flex;
        }

        .igao-btn.igao-btn--full-width[data-v-0dc3f13d] {
            width: 100%;
        }

        .igao-btn.igao-btn--l[data-v-0dc3f13d] {
            height: 50px;
            padding: 15px 32px;
        }

        .igao-btn.igao-btn--xl[data-v-0dc3f13d] {
            height: 54px;
            padding: 17px 32px;
        }

        .igao-btn.igao-btn--m[data-v-0dc3f13d] {
            height: 40px;
            padding: 10px 20px;
        }

        .igao-btn.igao-btn--s[data-v-0dc3f13d] {
            height: 32px;
            padding: 6px 16px;
        }

        .igao-spinner[data-v-9d02a4c1] {
            width: 16px;
            height: 16px;
            border-width: 0.2em;
            display: inline-block;
            vertical-align: text-bottom;
            border: 2px solid currentColor;
            border-right-color: transparent;
            border-radius: 50%;
            animation: spinner-border-9d02a4c1 0.75s linear infinite;
        }

        @keyframes spinner-border-9d02a4c1 {
            to {
                transform: rotate(360deg);
            }
        }

        .btn[data-v-55ea226e] {
            font-size: 12px;
            line-height: 1.67;
            border-radius: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #0567a7;
            position: relative;
            padding: 6px 15px;
            font-weight: bold;
            text-transform: uppercase;
            border: none;
            white-space: nowrap;
        }

        .btn.btn-primary[data-v-55ea226e] {
            color: #ffffff;
            background-color: #0567a7;
        }

        .btn.btn-primary[data-v-55ea226e]:hover {
            background-color: #045a8d;
            border-color: #045a8d;
        }

        .btn.btn-primary--green[data-v-55ea226e] {
            background-color: #15835c;
            border-color: #15835c;
        }

        .btn.btn-primary--green[data-v-55ea226e]:hover {
            background-color: #147642;
            border-color: #147642;
        }

        .btn.btn-disabled[data-v-55ea226e] {
            opacity: 0.65;
            pointer-events: none;
        }

        .btn.btn-link[data-v-55ea226e] {
            display: inline-block;
            height: auto;
            background: none;
            border: none;
            color: #0567a7;
            padding: 0;
            font-weight: 600;
        }

        .btn.btn-secondary[data-v-55ea226e] {
            background-color: #ffffff;
            border: solid 2px #eeeeee;
        }

        .btn.btn-secondary--gray[data-v-55ea226e] {
            border-color: #eeeeee;
            background-color: #eeeeee;
            color: #333333;
        }

        .btn.btn-secondary--gray[data-v-55ea226e]:hover {
            background-color: #d5d4d4;
            border-color: #d5d4d4;
        }

        .btn.btn-secondary--black[data-v-55ea226e] {
            border: solid 2px #eeeeee;
            background-color: #fff;
            color: #333;
        }

        .btn.btn-secondary[data-v-55ea226e]:hover {
            background-color: #ececec;
            border-color: #e6e6e6;
            color: #212529;
        }

        .btn.btn-danger[data-v-55ea226e] {
            color: #fff;
            background-color: #e8422f;
        }

        .btn.btn-danger[data-v-55ea226e]:hover {
            color: #fff;
        }

        .btn.btn-no-transform[data-v-55ea226e] {
            text-transform: none;
        }
    </style>
    <style>
        .account-buttons a[data-v-eba5ea3a], .account-buttons button[data-v-eba5ea3a] {
            color: #242424;
            text-transform: uppercase;
            font-size: 16px !important;
            font-weight: 700 !important;
            line-height: 24px !important;
            letter-spacing: -0.1px !important;
        }
        .account-buttons[data-v-eba5ea3a] {
            display: flex
        ;
            gap: 12px;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
    </style>
    @livewireStyles
</head>
<body x-data="{showBuy: false, openMenu: false}">
<div id="body-portal" data-astro-cid-37fxchfa="">
    <div data-v-cd0da137="" x-show="showBuy"
         x-transition x-cloak class="modal-popup modal-popup--without-header" style="opacity: 1;">
        <div data-v-cd0da137="" @click.away="showBuy = false" class="modal-popup-content" style="max-width: 920px;">
            <span data-v-cd0da137="" @click="showBuy = false" class="modal-popup-close modal-popup-close--alternative">
                <img data-v-cd0da137="" height="16" width="16" alt="Close icon"
                     src="{{asset('/icons/close-modal.svg')}}">
            </span>
            <div data-v-cd0da137="" class="modal-popup-body">
                <div data-v-d8bfb7f9="" class="buy-sessions-modal-container">
                    <livewire:components.buy_session/>
                </div>
            </div>
        </div>
    </div>
</div>
<livewire:layout.header/>
@if(!is_desktop())
    <div data-js="mobile-navigation" class="mobile-navigation-container " :class="openMenu == true ? 'open' : ''" data-astro-cid-hmtt24ci="">
        <div data-v-eba5ea3a="" class="account-buttons account-buttons" data-astro-cid-hmtt24ci="true">
            @if(!auth()->guard('customer')->user())
                <a data-v-bb39e5c0="" data-v-eba5ea3a=""
                   class="account-buttons-navigation-button igao-link igao-link--primary account-buttons-navigation-button"
                   href="{{route('login')}}"> Đăng nhập </a>

                <a data-v-bb39e5c0="" data-v-eba5ea3a=""
                   class="account-buttons-navigation-button account-buttons-apply-as-coach igao-link igao-link--primary account-buttons-navigation-button account-buttons-apply-as-coach"
                   href="{{route('filament.public.pages.apply-coach')}}">
                    Ứng tuyển làm huấn luyện viên
                </a>

                @else

                <a data-v-bb39e5c0="" data-v-eba5ea3a=""
                   class="account-buttons-navigation-button igao-link igao-link--primary account-buttons-navigation-button"
                   href="{{route('filament.customer.pages.dashboard')}}"> My Account </a>
            @endif


        </div>
        <div class="mobile-navigation-accordion" data-astro-cid-hmtt24ci="true" data-v-8aee8ac7="">
            <div class="accordion-item" data-v-8aee8ac7="" x-data="{ac: false}" :class="ac == true ? 'accordion-item--open' : ''">
                <a class="accordion-item-head--active accordion-item-head" href="#" data-v-8aee8ac7="" @click="ac = !ac">
                    Coaches
                    <img src="{{asset('icons/icon-chevron-down.svg')}}" width="10" height="10" alt="Icon to expand interview coaching navigation menu" data-v-8aee8ac7="" />
                </a>
                <div class="accordion-item-body" data-v-8aee8ac7="">
                    <div class="accordion-item-description" data-js="igao-accordion-item-description-0" data-v-8aee8ac7="" :style="ac == true ? 'height: 250px;' : ''">
                        <a href="{{route('home')}}" class="nav-item--first nav-item--active nav-item" style="display: block" data-v-8aee8ac7="">
                            Tất cả coaches
                        </a>
                        @php
                            $topServices = \App\Models\Service::query()
                                ->select('services.name', 'services.slug')
                                ->join('coache_service', 'services.id', '=', 'coache_service.service_id')
                                ->join('coaches', 'coache_service.coache_id', '=', 'coaches.id')
                                ->join('coaching_sessions', 'coaches.id', '=', 'coaching_sessions.coach_id')
                                ->where('coaching_sessions.status', '=', \App\Models\CoachingSession::STATUS_COMPLETED)
                                ->groupBy('services.id', 'services.name', 'services.slug')
                                ->orderByRaw('COUNT(coaching_sessions.id) DESC')
                                ->limit(5)
                                ->get();
                        @endphp
                        @foreach($topServices as $service)
                            <a class="menu-item {{request('slug') == $service->slug ? 'active' : ''}}" href="{{$service->slug}}" >
                                {{$service->name}}
                                @if($loop->first)
                                    <span class="new-badge new-badge--small new-badge--child" data-v-19dfa40e=""> Phổ biến </span>
                                @endif
                            </a>
                        @endforeach
                    </div>
                </div>
            </div>
            <div class="accordion-item" data-v-8aee8ac7="" x-data="{bl: false}" :class="bl == true ? 'accordion-item--open' : ''">
                <a class="accordion-item-head" href="#" data-v-8aee8ac7="" @click="bl = !bl">
                    Blog
                    <img src="{{asset('icons/icon-chevron-down.svg')}}" width="10" height="10" alt="Icon to expand interview coaching navigation menu" data-v-8aee8ac7="" />
                </a>
                <div class="accordion-item-body" data-v-8aee8ac7="">
                    <div class="accordion-item-description" data-js="igao-accordion-item-description-0" data-v-8aee8ac7=""
                         :style="bl == true ? 'height: 250px;' : ''">
                        @php
                            $blogCategories = \App\Models\BlogCategory::query()->select('name', 'slug')->orderBy('created_at', 'desc')->get();
                        @endphp
                        @foreach($blogCategories as $item)
                            <a class="menu-item" href="{{route('blog.category',['slug' => $item->slug])}}">
                                {{$item->name}}
                            </a>
                        @endforeach
                    </div>
                </div>
            </div>
            <div class="accordion-item" data-v-8aee8ac7="">
                <a class="accordion-item-head" href="{{route('reviews.index')}}" data-v-8aee8ac7="">
                    Đánh giá
                </a>
                <div class="accordion-item-body" data-v-8aee8ac7="">
                    <div class="accordion-item-description" data-js="igao-accordion-item-description-2" data-v-8aee8ac7="">
                        <!--[--><!--]--></div>
                </div>
            </div>
        </div>
    </div>
@endif
<main>
    @if(is_desktop() && !in_array(request()->route()->getName(),['blog.index','blog.category','blog.show']))
        <nav class="landing-page-sub-navigation" data-astro-cid-d77rco4z="" style="margin-top: 72px;">
            <div class="nav-item {{request()->route()->getName() == 'home' ? 'active' : ''}}" data-astro-cid-d77rco4z="true" data-v-19dfa40e="">
                <a href="{{route('home')}}" wire:navigate data-v-19dfa40e="">Tất cả</a>
            </div>
            @php
                $topServices = \App\Models\Service::query()
                    ->select('services.name', 'services.slug')
                    ->join('coache_service', 'services.id', '=', 'coache_service.service_id')
                    ->join('coaches', 'coache_service.coache_id', '=', 'coaches.id')
                    ->join('coaching_sessions', 'coaches.id', '=', 'coaching_sessions.coach_id')
                    ->where('coaching_sessions.status', '=', \App\Models\CoachingSession::STATUS_COMPLETED)
                    ->groupBy('services.id', 'services.name', 'services.slug')
                    ->orderByRaw('COUNT(coaching_sessions.id) DESC')
                    ->limit(4)
                    ->get();
            @endphp
            @foreach($topServices as $service)
                <div class="nav-item {{request('slug') == $service->slug ? 'active' : ''}}" data-astro-cid-d77rco4z="true" data-v-19dfa40e="">
                    <a href="{{$service->slug}}" wire:navigate data-v-19dfa40e="">{{$service->name}}</a>
                </div>

            @endforeach

        </nav>
    @else
        <nav class="mobile-sub-navigation" data-astro-cid-xbstl6g3="" style="margin-top: 72px;">
            <a class="navigation-link active" href="{{route('home')}}" data-astro-cid-xbstl6g3=""> Coaches </a>
            <a class="navigation-link" href="{{route('blog.index')}}" wire:navigate data-astro-cid-xbstl6g3=""> Blog </a>
            <a class="navigation-link" href="{{route('reviews.index')}}" wire:navigate data-astro-cid-xbstl6g3=""> Đánh giá </a>
        </nav>
    @endif
        {{ $slot }}


</main>
@livewireScripts
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://npmcdn.com/flatpickr/dist/l10n/vn.js"></script>

@include('components.footer')

</body>
</html>
