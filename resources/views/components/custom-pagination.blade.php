@if ($paginator->hasPages())
    <div class="coach-public-reviews-footer" data-v-25bcaaa8>
        <div class="data-v-25bcaaa8"></div>
        <div class="coach-public-reviews-pages" data-v-25bcaaa8>
            @foreach ($elements as $element)
                @if (is_string($element))
                    <span data-v-25bcaaa8 style="cursor: pointer">{{ $element }}</span>
                @endif

                @if (is_array($element))
                    @foreach ($element as $page => $url)
                        @if ($page == $paginator->currentPage())
                            <a class="active" style="cursor: pointer" data-v-25bcaaa8>{{ $page }}</a>
                        @else
                            <a wire:click="gotoPage({{ $page }})" style="cursor: pointer" data-v-25bcaaa8>{{ $page }}</a>
                        @endif
                    @endforeach
                @endif
            @endforeach
        </div>

        <div data-v-25bcaaa8>
            @if ($paginator->hasMorePages())
                <a wire:click="nextPage" data-v-25bcaaa8>Tiếp</a>
            @else
                <span class="disabled" data-v-25bcaaa8>Tiếp</span>
            @endif
        </div>
    </div>
@endif
