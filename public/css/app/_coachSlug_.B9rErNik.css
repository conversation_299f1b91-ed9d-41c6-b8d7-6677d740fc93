.coach-public-reviews-container[data-v-25bcaaa8] {
  box-shadow: 0 1px 2px 0 rgba(51, 51, 51, 0.04);
  background-color: #fff;
  border: solid 1px #eeeeee;
  text-align: left;
  font-size: 14px;
  margin-top: 24px;
}
@media (min-width: 960px) {
.coach-public-reviews-container[data-v-25bcaaa8] {
    margin-top: 32px;
}
}
.coach-public-reviews-container--without-pagination .coach-public-reviews-footer[data-v-25bcaaa8] {
  display: none;
}
.coach-public-reviews-container--without-pagination[data-v-25bcaaa8] .coach-public-reviews-review-container:last-child {
  border-bottom: none;
}
.coach-public-reviews-header[data-v-25bcaaa8] {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  line-height: 1.57;
  color: #333333;
  padding: 10px 16px;
  background-color: #fff;
  border-bottom: solid 1px #eeeeee;
}
@media (min-width: 960px) {
.coach-public-reviews-header[data-v-25bcaaa8] {
    padding: 13px 32px;
}
}
.coach-public-reviews-filters[data-v-25bcaaa8] {
  padding: 8px;
  scroll-margin-top: 70px;
}
@media (min-width: 960px) {
.coach-public-reviews-filters[data-v-25bcaaa8] {
    padding: 16px 24px 0;
}
}
.coach-public-reviews-filters .coach-public-reviews-filters-wrapper[data-v-25bcaaa8] {
  display: grid;
  gap: 16px;
  grid-template-columns: 1fr;
  border-bottom: solid 1px #eeeeee;
  padding: 0 8px 26px;
}
@media (min-width: 960px) {
.coach-public-reviews-filters .coach-public-reviews-filters-wrapper[data-v-25bcaaa8] {
    grid-template-columns: 1fr 1fr 1fr;
}
}
.coach-public-reviews-filters[data-v-25bcaaa8] input,
.coach-public-reviews-filters[data-v-25bcaaa8] .choices__inner {
  background-color: #fbfbfb !important;
}
.coach-public-reviews-filters[data-v-25bcaaa8] input {
  font-weight: 600;
}
.coach-public-reviews-filters[data-v-25bcaaa8] .choices__item {
  font-weight: 600;
}
.coach-public-reviews-filters[data-v-25bcaaa8] .igao-text-input-label,
.coach-public-reviews-filters[data-v-25bcaaa8] .igao-dropdown-label {
  font-weight: 600;
}
.coach-public-reviews-body[data-v-25bcaaa8] {
  padding: 0 8px;
  position: relative;
}
@media (min-width: 960px) {
.coach-public-reviews-body[data-v-25bcaaa8] {
    padding: 0 24px;
}
}
.coach-public-reviews-body--fetching[data-v-25bcaaa8] {
  opacity: 0.8;
}
.coach-public-reviews-spinner[data-v-25bcaaa8] {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 100;
}
.coach-public-reviews-spinner--empty[data-v-25bcaaa8] {
  --spinner-size: 50px;
}
.coach-public-reviews-footer[data-v-25bcaaa8] {
  display: grid;
  padding: 16px;
  grid-template-columns: 60px 1fr 60px;
}
.coach-public-reviews-footer[data-v-25bcaaa8] :first-child {
  text-align: left;
}
.coach-public-reviews-footer[data-v-25bcaaa8] :last-child {
  text-align: right;
}
.coach-public-reviews-footer a[data-v-25bcaaa8] {
  cursor: pointer;
  color: #0567a7;
}
.coach-public-reviews-pages[data-v-25bcaaa8] {
  display: flex;
  justify-content: center;
}
.coach-public-reviews-pages > a[data-v-25bcaaa8],
.coach-public-reviews-pages > span[data-v-25bcaaa8] {
  padding: 0 4px;
}
.coach-public-reviews-pages > a.active[data-v-25bcaaa8],
.coach-public-reviews-pages > span.active[data-v-25bcaaa8] {
  color: #242424;
}
.coach-public-reviews-empty[data-v-25bcaaa8] {
  padding: 16px 16px 32px;
  text-align: center;
  position: relative;
}.coach-specialties-container[data-v-8f2f8ad9] {
  border: 1px solid #eeeeee;
  text-align: left;
  margin-top: 24px;
}
@media (min-width: 960px) {
.coach-specialties-container[data-v-8f2f8ad9] {
    margin-top: 32px;
}
}
.coach-specialties-title[data-v-8f2f8ad9] {
  border-bottom: 1px solid #eeeeee;
  background-color: #fff;
  padding: 12px 16px;
}
@media (min-width: 960px) {
.coach-specialties-title[data-v-8f2f8ad9] {
    padding: 12px 32px;
}
}
.coach-specialties-body[data-v-8f2f8ad9] {
  padding: 0 16px;
}
@media (min-width: 960px) {
.coach-specialties-body[data-v-8f2f8ad9] {
    padding: 0 32px;
}
}
.coach-specialties-group[data-v-8f2f8ad9] {
  padding: 16px 0;
  border-bottom: 1px solid #eeeeee;
}
.coach-specialties-group[data-v-8f2f8ad9]:last-child {
  border-bottom: none;
}
.coach-specialties-group-title[data-v-8f2f8ad9],
.coach-specialties-title[data-v-8f2f8ad9] {
  font-size: 14px;
  line-height: 24px;
  font-weight: 600;
}
.coach-specialties-group-sub-title[data-v-8f2f8ad9] {
  font-size: 12px;
  line-height: 20px;
  font-style: italic;
  margin-top: 8px;
}
.coach-specialties-group-list[data-v-8f2f8ad9] {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 8px;
}
@media (min-width: 960px) {
.coach-specialties-group-list[data-v-8f2f8ad9] {
    margin-top: 16px;
}
}
.coach-specialties-group-sub-title + .coach-specialties-group-list[data-v-8f2f8ad9] {
  margin-top: 8px;
}
.coach-specialties-expand-more[data-v-8f2f8ad9] {
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  color: #878787;
  background: none;
  border: none;
  text-decoration: underline;
}