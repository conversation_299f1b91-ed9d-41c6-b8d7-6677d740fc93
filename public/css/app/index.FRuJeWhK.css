.session-picker-sub-title[data-v-39bff86a] {
    font-size: 16px;
    line-height: 1.5;
    margin-top: 16px;
}
.buy-sessions-modal-social-proof[data-v-39bff86a] {
    font-size: 14px;
    font-weight: 600;
    line-height: 24px;
    letter-spacing: -0.1px;
    gap: 4px;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin-top: 24px;
}
@media (min-width: 960px) {
    .buy-sessions-modal-social-proof[data-v-39bff86a] {
        margin-top: 32px;
    }
}
.session-picker-container[data-v-39bff86a] {
    text-align: center;
    margin-top: 36px;
}
@media (min-width: 960px) {
    .session-picker-container[data-v-39bff86a] {
        margin-top: 64px;
    }
}
.purchase-sessions_item_price_per_hour[data-v-39bff86a] {
    font-size: 12px;
    line-height: 1.67;
}
.purchase-sessions_item_quantity_price[data-v-39bff86a] {
    display: flex;
    flex-direction: column;
    justify-content: center;
}
.purchase-sessions[data-v-39bff86a] {
    margin-top: 24px;
}
@media (min-width: 960px) {
    .purchase-sessions[data-v-39bff86a] {
        padding-top: 50px;
    }
}
.purchase-sessions__header[data-v-39bff86a] {
    font-size: 20px;
    line-height: 1.5;
    font-weight: bold;
    color: #333333;
    margin-bottom: 16px;
}
.purchase-sessions__subheader[data-v-39bff86a] {
    font-size: 18px;
    line-height: 1.56;
    font-weight: bold;
    color: #333333;
    margin-bottom: 8px;
}
.purchase-sessions__default-sessions[data-v-39bff86a] {
    font-size: 18px;
    line-height: 1.56;
    color: #4a4a4a;
    margin-bottom: 32px;
}
.purchase-sessions__total[data-v-39bff86a] {
    font-size: 16px;
    line-height: 1.5;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.8);
}
.purchase-sessions__savings[data-v-39bff86a] {
    font-size: 14px;
    line-height: 1.57;
    font-weight: normal;
    color: #666666;
}
.purchase-sessions__social-proof-separator[data-v-39bff86a] {
    margin: 0 4px;
    font-weight: normal;
    color: #aaaaaa;
}
@media (max-width: 960px) {
    .purchase-sessions__social-proof-separator[data-v-39bff86a] {
        display: none !important;
    }
}
.purchase-sessions__social-proof-exclamation-icon[data-v-39bff86a] {
    width: 12px;
    height: 12px;
    max-width: 12px;
    max-height: 12px;
    margin-left: 8px;
    cursor: pointer;
}
.purchase-sessions__social-proof-image[data-v-39bff86a] {
    margin-top: 16px;
}
.purchase-sessions__social-proof[data-v-39bff86a] {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.57;
    letter-spacing: normal;
    color: #333333;
}
.purchase-sessions__social-proof > span[data-v-39bff86a] {
    display: inline-flex;
    align-items: center;
}
@media (max-width: 960px) {
    .purchase-sessions__social-proof[data-v-39bff86a] {
        flex-direction: column;
    }
}
@media (min-width: 960px) {
    .purchase-sessions .modal-dialog[data-v-39bff86a] {
        margin: 130px auto;
    }
    .purchase-sessions .modal-header[data-v-39bff86a] {
        padding: 16px 16px 10px;
    }
    .purchase-sessions .modal-body[data-v-39bff86a] {
        padding: 0 64px 48px;
    }
    .purchase-sessions__header[data-v-39bff86a] {
        font-size: 24px;
        line-height: 1.42;
        max-width: 620px;
        margin: 0 auto 32px;
    }
    .purchase-sessions__subheader[data-v-39bff86a] {
        font-size: 20px;
        line-height: 1.5;
        max-width: 620px;
        margin-left: auto;
        margin-right: auto;
    }
    .purchase-sessions__default-sessions[data-v-39bff86a] {
        max-width: 620px;
        margin-left: auto;
        margin-right: auto;
    }
}
.purchase-sessions__buttons[data-v-39bff86a] {
    display: flex;
    gap: 16px;
    margin-top: 24px;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}
@media (min-width: 960px) {
    .purchase-sessions__buttons[data-v-39bff86a] {
        flex-direction: row;
    }
}
.purchase-sessions__buttons > a.btn[data-v-39bff86a],
.purchase-sessions__buttons > button[data-v-39bff86a] {
    width: 100%;
}
@media (min-width: 960px) {
    .purchase-sessions__buttons > a.btn[data-v-39bff86a],
    .purchase-sessions__buttons > button[data-v-39bff86a] {
        width: auto;
    }
}
.purchase-sessions__choices-bar[data-v-39bff86a] {
    display: none;
    margin: 0 auto 24px;
}
@media (min-width: 960px) {
    .purchase-sessions__choices-bar[data-v-39bff86a] {
        display: flex;
    }
}
.purchase-sessions__choices-bar .most-popular[data-v-39bff86a] {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    line-height: 22px;
    font-size: 12px;
    position: absolute;
    font-weight: bold;
    height: 30px;
    background: #a67424;
    color: #fff;
    text-transform: uppercase;
    padding: 3px 8px;
    white-space: nowrap;
    left: 50%;
    transform: translateX(-50%);
    top: -50px;
}
.purchase-sessions__choices-bar__item[data-v-39bff86a] {
    font-size: 18px;
    line-height: 1.56;
    display: flex;
    flex-grow: 1;
    align-items: center;
    justify-content: center;
    height: 80px;
    position: relative;
    cursor: pointer;
    font-weight: 600;
    color: #333333;
    border-top: 2px solid #ececec;
    border-bottom: 2px solid #ececec;
    border-left: 2px solid #ececec;
}
.purchase-sessions__choices-bar__item.active + .purchase-sessions__choices-bar__item[data-v-39bff86a] {
    border-left: none;
}
.purchase-sessions__choices-bar__item.active[data-v-39bff86a] {
    font-weight: 600;
    background: #0567a7 !important;
    border: 2px solid #0567a7 !important;
    color: #fff;
}
.purchase-sessions__choices-bar__item.active .price-per-hour[data-v-39bff86a] {
    display: flex;
}
.purchase-sessions__choices-bar__item[data-v-39bff86a]:hover {
    background: #eeeeee;
}
.purchase-sessions__choices-bar__item[data-v-39bff86a]:last-child {
    border-right: 2px solid #ececec;
}
.purchase-sessions__choices-responsive[data-v-39bff86a] {
    display: block;
}
.purchase-sessions__choices-responsive__most-popular[data-v-39bff86a] {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    line-height: 22px;
    font-size: 12px;
    position: absolute;
    font-weight: bold;
    height: 30px;
    background: #a67424;
    color: #fff;
    text-transform: uppercase;
    padding: 3px 8px;
    white-space: nowrap;
    left: 50%;
    transform: translateX(-50%);
    top: -50px;
    top: 0;
    left: 0;
    position: inherit;
    width: 133px;
    margin: 0 auto 20px;
    transform: none;
}
.purchase-sessions__choices-responsive__most-popular--hidden[data-v-39bff86a] {
    visibility: hidden;
}
.purchase-sessions__choices-responsive__dropdown[data-v-39bff86a] {
    -moz-appearance: none;
    -webkit-appearance: none;
    appearance: none;
    margin: 0 0 32px;
    width: 100%;
    height: auto;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    letter-spacing: -0.1px;
    color: #333333;
    box-shadow: 0 2px 4px 0 rgba(51, 51, 51, 0.12);
    border: solid 1px #0567a7;
    padding: 13px 18.5px 13px 16px;
    background-image: linear-gradient(45deg, transparent 50%, #0567a7 50%), linear-gradient(135deg, #0567a7 50%, transparent 50%);
    background-position: calc(100% - 27px) calc(1rem + 10px), calc(100% - 23px) calc(1rem + 10px);
    background-size: 4px 4px, 4px 4px;
    background-repeat: no-repeat;
}
@media (min-width: 960px) {
    .purchase-sessions__choices-responsive[data-v-39bff86a] {
        display: none;
    }
}
