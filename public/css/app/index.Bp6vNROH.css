.coach-price-container[data-v-bb1f7c8b] {
  color: #333333;
  display: flex;
  font-size: 12px;
  line-height: 16px;
  align-items: center;
  gap: 4px;
}
@media (min-width: 960px) {
.coach-price-container[data-v-bb1f7c8b] {
    flex-direction: column;
    font-size: 14px;
    line-height: 22px;
    gap: 0;
}
}
.coach-price-container > div[data-v-bb1f7c8b] {
  display: flex;
  flex-direction: column;
}
.coach-price-main-value[data-v-bb1f7c8b] {
  font-size: 16px;
  line-height: 22px;
  font-style: normal;
  font-weight: 700;
}
@media (min-width: 960px) {
.coach-price-main-value[data-v-bb1f7c8b] {
    font-size: 24px;
    line-height: 34px;
}
}.coach-details-card-container[data-v-27aaf9f5] {
  position: relative;
  box-shadow: 0 1px 2px 0 rgba(51, 51, 51, 0.04);
  border: 1px solid #eeeeee;
  padding: 32px 24px;
  font-size: 14px;
  line-height: 1.57;
  color: #333333;
  word-wrap: break-word;
  background-color: #fff;
  text-align: left;
  display: grid;
  grid-template-columns: auto 1fr;
  color: #333333;
  font-size: 14px;
}
.coach-details-card-picture-cell[data-v-27aaf9f5] {
  position: relative;
  margin-right: 16px;
}
@media (min-width: 960px) {
.coach-details-card-picture-cell[data-v-27aaf9f5] {
    margin-right: 24px;
    grid-row: 1/3;
}
}
.coach-details-card-avatar[data-v-27aaf9f5] {
  filter: grayscale(1);
  border-radius: 50%;
}
.coach-details-card-name[data-v-27aaf9f5] {
  display: inline-flex;
  flex-wrap: wrap;
  max-width: calc(100% - 26px);
  font-size: 24px;
  font-weight: bold;
  line-height: 1.42;
  gap: 4px;
}
@media (min-width: 960px) {
.coach-details-card-name[data-v-27aaf9f5] {
    gap: 20px;
}
}
.coach-details-card-role[data-v-27aaf9f5] {
  font-weight: 600;
  line-height: 1.57;
  color: #4a4a4a;
  max-width: calc(100% - 400px);
  margin-bottom: 8px;
}
@media (max-width: 960px) {
.coach-details-card-role[data-v-27aaf9f5] {
    margin-bottom: 0;
    max-width: 100%;
}
}
.coach-details-card-role.coach-details-card-role--flexible[data-v-27aaf9f5] {
  margin-bottom: 24px;
}
.coach-details-card-role--mobile[data-v-27aaf9f5] {
  display: none;
}
@media (max-width: 960px) {
.coach-details-card-role--mobile[data-v-27aaf9f5] {
    display: block;
    margin-bottom: 24px;
}
}
.coach-details-card-info-line[data-v-27aaf9f5] {
  display: flex;
  align-items: center;
}
@media (max-width: 960px) {
.coach-details-card-info-line[data-v-27aaf9f5] {
    flex-direction: column;
    align-items: flex-start;
}
}
.coach-details-card-info-line img[data-v-27aaf9f5] {
  margin-right: 8px;
}
.coach-details-card-info-line + .coach-details-card-info-line[data-v-27aaf9f5] {
  margin-top: 8px;
}
.coach-details-card-info-field[data-v-27aaf9f5] {
  display: inline-flex;
  align-items: center;
}
.coach-details-card-info-field .new-badge[data-v-27aaf9f5] {
  color: #a67424;
}
@media (max-width: 960px) {
.coach-details-card-info-field[data-v-27aaf9f5] {
    margin-bottom: 8px;
}
.coach-details-card-info-field[data-v-27aaf9f5]:last-child {
    margin-bottom: 0;
}
}
.coach-details-card-info-line-separator[data-v-27aaf9f5] {
  color: #aaaaaa;
  padding: 0 6px;
}
@media (max-width: 960px) {
.coach-details-card-info-line-separator[data-v-27aaf9f5] {
    display: none;
}
}
.coach-details-card-buy-sessions-button-mobile[data-v-27aaf9f5] {
  margin-bottom: 24px;
  width: 100%;
}
@media (min-width: 960px) {
.coach-details-card-buy-sessions-button-mobile[data-v-27aaf9f5] {
    display: none !important;
}
}
.coach-details-card-buy-sessions-button-mobile > img[data-v-27aaf9f5] {
  margin-right: 8px;
}
.coach-details-role-and-properties-cell[data-v-27aaf9f5] {
  margin-top: 10px;
}
@media (max-width: 960px) {
.coach-details-role-and-properties-cell[data-v-27aaf9f5] {
    margin-top: 24px;
    grid-column: 1/3;
}
}
.coach-details-card-reassurances[data-v-27aaf9f5] {
  margin-top: 24px;
}
@media (min-width: 960px) {
.coach-details-card-reassurances--flexible[data-v-27aaf9f5] {
    display: none !important;
}
}
.coach-details-card-info-cell[data-v-27aaf9f5] {
  grid-column: 1/3;
}
.coach-details-card-price[data-v-27aaf9f5] {
  margin-top: 12px;
}
@media (min-width: 960px) {
.coach-details-card-price[data-v-27aaf9f5] {
    display: none;
}
}
.coach-details-card-action-slot[data-v-27aaf9f5] {
  display: flex;
  align-items: center;
  grid-column: 1/3;
  flex-wrap: wrap;
  flex-direction: column-reverse;
}
.coach-details-card-action-slot[data-v-27aaf9f5] > button {
  width: 100%;
}
@media (min-width: 960px) {
.coach-details-card-action-slot[data-v-27aaf9f5] {
    flex-direction: row;
    position: absolute;
    top: 32px;
    right: 0;
}
}
.coach-details-card-heading[data-v-27aaf9f5] {
  font-size: 16px;
  font-weight: bold;
  line-height: 1.5;
  margin: 24px 0 8px;
}
.coach-details-card-bio[data-v-27aaf9f5] {
  white-space: pre-wrap;
}