.choices {
    position: relative;
    overflow: hidden;
    margin-bottom: 24px;
    font-size: 16px
}

.choices:focus {
    outline: 0
}

.choices:last-child {
    margin-bottom: 0
}

.choices.is-open {
    overflow: visible
}

.choices.is-disabled .choices__inner, .choices.is-disabled .choices__input {
    background-color: #eaeaea;
    cursor: not-allowed;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.choices.is-disabled .choices__item {
    cursor: not-allowed
}

.choices [hidden] {
    display: none !important
}

.choices[data-type*=select-one] {
    cursor: pointer
}

.choices[data-type*=select-one] .choices__inner {
    padding-bottom: 7.5px
}

.choices[data-type*=select-one] .choices__input {
    display: block;
    width: 100%;
    padding: 10px;
    border-bottom: 1px solid #ddd;
    background-color: #fff;
    margin: 0
}

.choices[data-type*=select-one] .choices__button {
    background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjMDAwIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yLjU5Mi4wNDRsMTguMzY0IDE4LjM2NC0yLjU0OCAyLjU0OEwuMDQ0IDIuNTkyeiIvPjxwYXRoIGQ9Ik0wIDE4LjM2NEwxOC4zNjQgMGwyLjU0OCAyLjU0OEwyLjU0OCAyMC45MTJ6Ii8+PC9nPjwvc3ZnPg==);
    padding: 0;
    background-size: 8px;
    position: absolute;
    top: 50%;
    right: 0;
    margin-top: -10px;
    margin-right: 25px;
    height: 20px;
    width: 20px;
    border-radius: 10em;
    opacity: .25
}

.choices[data-type*=select-one] .choices__button:focus, .choices[data-type*=select-one] .choices__button:hover {
    opacity: 1
}

.choices[data-type*=select-one] .choices__button:focus {
    box-shadow: 0 0 0 2px #005f75
}

.choices[data-type*=select-one] .choices__item[data-placeholder] .choices__button {
    display: none
}

.choices[data-type*=select-one]::after {
    content: "";
    height: 0;
    width: 0;
    border-style: solid;
    border-color: #333 transparent transparent;
    border-width: 5px;
    position: absolute;
    right: 11.5px;
    top: 50%;
    margin-top: -2.5px;
    pointer-events: none
}

.choices[data-type*=select-one].is-open::after {
    border-color: transparent transparent #333;
    margin-top: -7.5px
}

.choices[data-type*=select-one][dir=rtl]::after {
    left: 11.5px;
    right: auto
}

.choices[data-type*=select-one][dir=rtl] .choices__button {
    right: auto;
    left: 0;
    margin-left: 25px;
    margin-right: 0
}

.choices[data-type*=select-multiple] .choices__inner, .choices[data-type*=text] .choices__inner {
    cursor: text
}

.choices[data-type*=select-multiple] .choices__button, .choices[data-type*=text] .choices__button {
    position: relative;
    display: inline-block;
    margin: 0 -4px 0 8px;
    padding-left: 16px;
    border-left: 1px solid #003642;
    background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjRkZGIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yLjU5Mi4wNDRsMTguMzY0IDE4LjM2NC0yLjU0OCAyLjU0OEwuMDQ0IDIuNTkyeiIvPjxwYXRoIGQ9Ik0wIDE4LjM2NEwxOC4zNjQgMGwyLjU0OCAyLjU0OEwyLjU0OCAyMC45MTJ6Ii8+PC9nPjwvc3ZnPg==);
    background-size: 8px;
    width: 8px;
    line-height: 1;
    opacity: .75;
    border-radius: 0
}

.choices[data-type*=select-multiple] .choices__button:focus, .choices[data-type*=select-multiple] .choices__button:hover, .choices[data-type*=text] .choices__button:focus, .choices[data-type*=text] .choices__button:hover {
    opacity: 1
}

.choices__inner {
    display: inline-block;
    vertical-align: top;
    width: 100%;
    background-color: #f9f9f9;
    padding: 7.5px 7.5px 3.75px;
    border: 1px solid #ddd;
    border-radius: 2.5px;
    font-size: 14px;
    min-height: 44px;
    overflow: hidden
}

.is-focused .choices__inner, .is-open .choices__inner {
    border-color: #b7b7b7
}

.is-open .choices__inner {
    border-radius: 2.5px 2.5px 0 0
}

.is-flipped.is-open .choices__inner {
    border-radius: 0 0 2.5px 2.5px
}

.choices__list {
    margin: 0;
    padding-left: 0;
    list-style: none
}

.choices__list--single {
    display: inline-block;
    padding: 4px 16px 4px 4px;
    width: 100%
}

[dir=rtl] .choices__list--single {
    padding-right: 4px;
    padding-left: 16px
}

.choices__list--single .choices__item {
    width: 100%
}

.choices__list--multiple {
    display: inline
}

.choices__list--multiple .choices__item {
    display: inline-block;
    vertical-align: middle;
    border-radius: 20px;
    padding: 4px 10px;
    font-size: 12px;
    font-weight: 500;
    margin-right: 3.75px;
    margin-bottom: 3.75px;
    background-color: #005f75;
    border: 1px solid #004a5c;
    color: #fff;
    word-break: break-all;
    box-sizing: border-box
}

.choices__list--multiple .choices__item[data-deletable] {
    padding-right: 5px
}

[dir=rtl] .choices__list--multiple .choices__item {
    margin-right: 0;
    margin-left: 3.75px
}

.choices__list--multiple .choices__item.is-highlighted {
    background-color: #004a5c;
    border: 1px solid #003642
}

.is-disabled .choices__list--multiple .choices__item {
    background-color: #aaa;
    border: 1px solid #919191
}

.choices__list--dropdown, .choices__list[aria-expanded] {
    display: none;
    z-index: 1;
    position: absolute;
    width: 100%;
    background-color: #fff;
    border: 1px solid #ddd;
    top: 100%;
    margin-top: -1px;
    border-bottom-left-radius: 2.5px;
    border-bottom-right-radius: 2.5px;
    overflow: hidden;
    word-break: break-all
}

.is-active.choices__list--dropdown, .is-active.choices__list[aria-expanded] {
    display: block
}

.is-open .choices__list--dropdown, .is-open .choices__list[aria-expanded] {
    border-color: #b7b7b7
}

.is-flipped .choices__list--dropdown, .is-flipped .choices__list[aria-expanded] {
    top: auto;
    bottom: 100%;
    margin-top: 0;
    margin-bottom: -1px;
    border-radius: .25rem .25rem 0 0
}

.choices__list--dropdown .choices__list, .choices__list[aria-expanded] .choices__list {
    position: relative;
    max-height: 300px;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
    will-change: scroll-position
}

.choices__list--dropdown .choices__item, .choices__list[aria-expanded] .choices__item {
    position: relative;
    padding: 10px;
    font-size: 14px
}

[dir=rtl] .choices__list--dropdown .choices__item, [dir=rtl] .choices__list[aria-expanded] .choices__item {
    text-align: right
}

@media (min-width: 640px) {
    .choices__list--dropdown .choices__item--selectable[data-select-text], .choices__list[aria-expanded] .choices__item--selectable[data-select-text] {
        padding-right: 100px
    }

    .choices__list--dropdown .choices__item--selectable[data-select-text]::after, .choices__list[aria-expanded] .choices__item--selectable[data-select-text]::after {
        content: attr(data-select-text);
        font-size: 12px;
        opacity: 0;
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%)
    }

    [dir=rtl] .choices__list--dropdown .choices__item--selectable[data-select-text], [dir=rtl] .choices__list[aria-expanded] .choices__item--selectable[data-select-text] {
        text-align: right;
        padding-left: 100px;
        padding-right: 10px
    }

    [dir=rtl] .choices__list--dropdown .choices__item--selectable[data-select-text]::after, [dir=rtl] .choices__list[aria-expanded] .choices__item--selectable[data-select-text]::after {
        right: auto;
        left: 10px
    }
}

.choices__list--dropdown .choices__item--selectable.is-highlighted, .choices__list[aria-expanded] .choices__item--selectable.is-highlighted {
    background-color: #f2f2f2
}

.choices__list--dropdown .choices__item--selectable.is-highlighted::after, .choices__list[aria-expanded] .choices__item--selectable.is-highlighted::after {
    opacity: .5
}

.choices__item {
    cursor: default
}

.choices__item--selectable {
    cursor: pointer
}

.choices__item--disabled {
    cursor: not-allowed;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    opacity: .5
}

.choices__heading {
    font-weight: 600;
    font-size: 12px;
    padding: 10px;
    border-bottom: 1px solid #f7f7f7;
    color: gray
}

.choices__button {
    text-indent: -9999px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: 0;
    background-color: transparent;
    background-repeat: no-repeat;
    background-position: center;
    cursor: pointer
}

.choices__button:focus, .choices__input:focus {
    outline: 0
}

.choices__input {
    display: inline-block;
    vertical-align: baseline;
    background-color: #f9f9f9;
    font-size: 14px;
    margin-bottom: 5px;
    border: 0;
    border-radius: 0;
    max-width: 100%;
    padding: 4px 0 4px 2px
}

.choices__input::-webkit-search-cancel-button, .choices__input::-webkit-search-decoration, .choices__input::-webkit-search-results-button, .choices__input::-webkit-search-results-decoration {
    display: none
}

.choices__input::-ms-clear, .choices__input::-ms-reveal {
    display: none;
    width: 0;
    height: 0
}

[dir=rtl] .choices__input {
    padding-right: 2px;
    padding-left: 0
}

.choices__placeholder {
    opacity: .5
}

.choices-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #666666;
    opacity: 0.4;
    z-index: 600;
}

.igao-dropdown {
    display: flex;
    flex-direction: column;
    text-align: left;
    position: relative;
    width: 100%;
}

.igao-dropdown .choices.is-open {
    z-index: 7777;
}

.igao-dropdown .choices.is-open::after {
    transform: rotate(180deg);
    margin-top: -2.5px;
}

.igao-dropdown .choices::after {
    background: url("../icons/icon-chevron-down.svg") no-repeat;
    position: absolute;
    content: "";
    height: 7px;
    width: 10px;
    top: 50%;
    margin-top: -2.5px;
    pointer-events: none;
    border: none;
}

.igao-dropdown-initializing-spinner[data-v-b5ca3b34] {
    width: 16px;
    height: 16px;
    position: absolute;
    bottom: calc(var(--igao-dropdown-height) / 2 - 8px);
    left: calc(50% - 8px);
    color: #878787;
}

.igao-dropdown.igao-dropdown--size-s[data-v-b5ca3b34] {
    --igao-dropdown-height: 32px;
    --igao-vertical-padding: 3px;
    --igao-horizontal-padding: 16px;
}

.igao-dropdown.igao-dropdown--size-s[data-v-b5ca3b34] .choices .choices__inner .choices__item {
    line-height: 1.2;
}

.igao-dropdown.igao-dropdown--size-m {
    --igao-dropdown-height: 40px;
    --igao-vertical-padding: 6px;
    --igao-horizontal-padding: 16px;
}

.igao-dropdown.igao-dropdown--size-m .choices .choices__inner .choices__item {
    line-height: 1.4;
}

.igao-dropdown--size-l[data-v-b5ca3b34] {
    --igao-dropdown-height: 50px;
    --igao-vertical-padding: 11px;
    --igao-horizontal-padding: 16px;
}

.igao-dropdown.igao-dropdown--tag-style[data-v-b5ca3b34] .choices .choices__inner .choices__item {
    background: #0567a7;
    color: #fff;
    padding: 1px 8px;
    box-shadow: 0 1px 2px 0 rgba(51, 51, 51, 0.12);
}

@keyframes spinner-border-b5ca3b34 {
    to {
        transform: rotate(360deg);
    }
}

.igao-dropdown.igao-dropdown--loading[data-v-b5ca3b34] .choices .choices__inner::after {
    content: "";
    position: absolute;
    right: 30px;
    top: 50%;
    margin-top: -6px;
    width: 12px;
    height: 12px;
    border-width: 0.2em;
    display: inline-block;
    vertical-align: text-bottom;
    border: 2px solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spinner-border-b5ca3b34 0.75s linear infinite;
}

.igao-dropdown.igao-dropdown--with-search[data-v-b5ca3b34] .choices .choices__inner input[type=search] {
    display: inline-block;
    max-height: 23px;
    width: 1ch;
    font-size: 16px;
}

.igao-dropdown.igao-dropdown--with-search[data-v-b5ca3b34] .choices .choices__list input[type=search] {
    padding: 10px 12px;
}

@media (max-width: 960px) {
    .igao-dropdown.igao-dropdown--with-search[data-v-b5ca3b34] .choices .choices__list input[type=search] {
        font-size: 16px;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }
}

.igao-dropdown-label {
    font-size: 14px;
    line-height: 1.57;
    text-transform: uppercase;
    font-weight: bold;
    color: #666666;
    margin-bottom: 8px;
}

@media (max-width: 960px) {
    .igao-dropdown-label {
        font-size: 12px;
        line-height: 1.67;
    }
}

.igao-dropdown-native {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    overflow: hidden;
    outline: none;
    height: var(--igao-dropdown-height);
}

.igao-dropdown-native:active, .igao-dropdown-native:focus {
    box-shadow: inset 0 0 0 1px #0567a7, 0 0 0 2px rgba(0, 123, 202, 0.12), inset 0 0 2px 2px rgba(102, 102, 102, 0.24);
}

.igao-dropdown .igao-dropdown-native,
.igao-dropdown .choices .choices__inner {
    min-height: var(--igao-dropdown-height);
    padding: var(--igao-vertical-padding) var(--igao-horizontal-padding);
    background: #fff;
    border-radius: 0;
    border: 1px solid #d5d4d4;
    font-size: 14px;
    line-height: 24px;
    font-stretch: normal;
    font-style: normal;
    letter-spacing: normal;
    align-items: center;
    transition: border 0.2s linear;
    cursor: pointer;
}

.igao-dropdown .igao-dropdown-native input[type=search],
.igao-dropdown .choices .choices__inner input[type=search] {
    display: none;
    background: transparent;
    margin: 0 0 0 4px;
}

[data-v-b5ca3b34] .choices {
    transition: box-shadow 0.2s linear;
}

[data-v-b5ca3b34] .choices.is-disabled {
    opacity: 0.64;
}

[data-v-b5ca3b34] .choices.is-disabled .choices__inner {
    cursor: not-allowed;
}

[data-v-b5ca3b34] .choices.is-focused .choices__inner {
    border: 1px solid #0567a7;
}

[data-v-b5ca3b34] .choices.is-open {
    box-shadow: inset 0 0 0 1px #0567a7, 0 0 0 2px rgba(0, 123, 202, 0.12), inset 0 0 2px 2px rgba(102, 102, 102, 0.24);
}

[data-v-b5ca3b34] .choices::after {
    right: var(--igao-horizontal-padding) !important;
}

[data-v-b5ca3b34] .choices .choices__inner .choices__list {
    padding: 0;
}

[data-v-b5ca3b34] .choices .choices__inner .choices__list .choices__item {
    font-size: 14px;
    line-height: 1.57;
    width: auto;
    display: inline-flex;
    color: #4a4a4a;
    border: solid 1px transparent;
    border-radius: 0;
    align-items: center;
    padding: 1px 16px 0 0;
}

@media (max-width: 960px) {
    [data-v-b5ca3b34] .choices .choices__inner .choices__list .choices__item {
        font-size: 16px;
        line-height: 1.5;
    }
}

[data-v-b5ca3b34] .choices .choices__inner .choices__list .choices__item[data-value=ALL_VALUE] {
    padding-right: 10px;
}

[data-v-b5ca3b34] .choices .choices__inner .choices__list .choices__item[data-value=ALL_VALUE] .choices__button {
    display: none;
}

[data-v-b5ca3b34] .choices .choices__inner .choices__list .choices__item .choices__button {
    background: none;
    -webkit-mask-image: url("../icons/remove.svg");
    mask-image: url("../icons/remove.svg");
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-size: contain;
    mask-size: contain;
    -webkit-mask-position: center;
    mask-position: center;
    background-color: #878787;
    width: 12px;
    height: 12px;
    border: none;
    margin: 0;
    padding: 0;
    opacity: 0.75;
    cursor: pointer;
    margin-left: 6px;
}

[data-v-b5ca3b34] .choices .choices__inner .choices__list .choices__item .choices__button:hover {
    opacity: 1;
}

[data-v-b5ca3b34] .choices .choices__item {
    color: #333333;
}

[data-v-b5ca3b34] .choices[data-type="select-multiple"] .choices__inner {
    padding: var(--igao-vertical-padding) var(--igao-horizontal-padding) calc(var(--igao-vertical-padding) - 3.75px);
}

[data-v-b5ca3b34] .choices[data-type="select-multiple"] .choices__inner .choices__list .choices__item {
    padding: 1px 8px;
    box-shadow: 0 1px 2px 0 rgba(51, 51, 51, 0.12);
    border: solid 1px rgba(102, 102, 102, 0.24);
    background-image: linear-gradient(to top, #fbfbfb, #fff);
}

[data-v-b5ca3b34] .choices__list[aria-expanded] {
    color: #333333;
    top: calc(100% + 3px);
    background-color: #fff;
    border: none;
    border: 1px solid #eeeeee;
}

[data-v-b5ca3b34] .choices__list[aria-expanded] .choices__group .choices__heading {
    border: none;
    font-size: 12px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.67;
    letter-spacing: normal;
    text-transform: uppercase;
    color: #878787;
    padding: 5px 12px;
}

[data-v-b5ca3b34] .choices__list[aria-expanded] .choices__item {
    display: flex;
    justify-content: space-between;
    box-shadow: none;
    background: none;
    border: none;
    border-top: 1px solid #eeeeee;
    padding: 9px 20px;
    word-break: break-word;
}

[data-v-b5ca3b34] .choices__list[aria-expanded] .choices__item[data-value=ALL_VALUE][role=treeitem] {
    padding-left: 12px;
}

[data-v-b5ca3b34] .choices__list[aria-expanded] .choices__item:first-child {
    border-top: none;
}

[data-v-b5ca3b34] .choices__list[aria-expanded] .choices__item.is-highlighted, [data-v-b5ca3b34] .choices__list[aria-expanded] .choices__item.is-selected {
    background-color: #e9f3fa;
}

[data-v-b5ca3b34] .choices__list[aria-expanded] .choices__item.is-selected {
    position: relative;
    padding-right: 42px;
}

[data-v-b5ca3b34] .choices__list[aria-expanded] .choices__item.is-selected::after {
    content: "";
    position: absolute;
    background: url("../icons/checkmark-dropdown.svg") no-repeat;
    width: 16px;
    height: 16px;
    opacity: 1;
    display: block;
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
}
