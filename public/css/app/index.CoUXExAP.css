.change-base-price-modal-input-label[data-v-830e4eee] {
  font-size: 12px;
  line-height: 1.67;
  color: #333333;
  font-style: normal;
  font-weight: 600;
  margin: 16px 0 4px 0;
}.session-picker-sub-title[data-v-0a9d5463] {
  font-size: 16px;
  line-height: 1.5;
  margin-top: 16px;
}
.session-picker-container[data-v-0a9d5463] {
  text-align: center;
  margin-top: 36px;
}
@media (min-width: 960px) {
.session-picker-container[data-v-0a9d5463] {
    margin-top: 64px;
}
}
.purchase-sessions_item_price_per_hour[data-v-0a9d5463] {
  font-size: 12px;
  line-height: 1.67;
}
.purchase-sessions_item_quantity_price[data-v-0a9d5463] {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.purchase-sessions[data-v-0a9d5463] {
  margin-top: 24px;
}
@media (min-width: 960px) {
.purchase-sessions[data-v-0a9d5463] {
    padding-top: 50px;
}
}
.purchase-sessions__header[data-v-0a9d5463] {
  font-size: 20px;
  line-height: 1.5;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16px;
}
.purchase-sessions__subheader[data-v-0a9d5463] {
  font-size: 18px;
  line-height: 1.56;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8px;
}
.purchase-sessions__default-sessions[data-v-0a9d5463] {
  font-size: 18px;
  line-height: 1.56;
  color: #4a4a4a;
  margin-bottom: 32px;
}
.purchase-sessions__total[data-v-0a9d5463] {
  font-size: 16px;
  line-height: 1.5;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.8);
}
.purchase-sessions__savings[data-v-0a9d5463] {
  font-size: 14px;
  line-height: 1.57;
  font-weight: normal;
  color: #666666;
}
.purchase-sessions__social-proof-separator[data-v-0a9d5463] {
  margin: 0 4px;
  font-weight: normal;
  color: #aaaaaa;
}
@media (max-width: 960px) {
.purchase-sessions__social-proof-separator[data-v-0a9d5463] {
    display: none !important;
}
}
.purchase-sessions__social-proof-exclamation-icon[data-v-0a9d5463] {
  width: 12px;
  height: 12px;
  max-width: 12px;
  max-height: 12px;
  margin-left: 8px;
  cursor: pointer;
}
.purchase-sessions__social-proof-image[data-v-0a9d5463] {
  margin-top: 16px;
}
.purchase-sessions__social-proof[data-v-0a9d5463] {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.57;
  letter-spacing: normal;
  color: #333333;
}
.purchase-sessions__social-proof > span[data-v-0a9d5463] {
  display: inline-flex;
  align-items: center;
}
@media (max-width: 960px) {
.purchase-sessions__social-proof[data-v-0a9d5463] {
    flex-direction: column;
}
}
@media (min-width: 960px) {
.purchase-sessions .modal-dialog[data-v-0a9d5463] {
    margin: 130px auto;
}
.purchase-sessions .modal-header[data-v-0a9d5463] {
    padding: 16px 16px 10px;
}
.purchase-sessions .modal-body[data-v-0a9d5463] {
    padding: 0 64px 48px;
}
.purchase-sessions__header[data-v-0a9d5463] {
    font-size: 24px;
    line-height: 1.42;
    max-width: 620px;
    margin: 0 auto 32px;
}
.purchase-sessions__subheader[data-v-0a9d5463] {
    font-size: 20px;
    line-height: 1.5;
    max-width: 620px;
    margin-left: auto;
    margin-right: auto;
}
.purchase-sessions__default-sessions[data-v-0a9d5463] {
    max-width: 620px;
    margin-left: auto;
    margin-right: auto;
}
}
.purchase-sessions__choices-bar[data-v-0a9d5463] {
  display: none;
  margin: 0 auto 24px;
}
@media (min-width: 960px) {
.purchase-sessions__choices-bar[data-v-0a9d5463] {
    display: flex;
}
}
.purchase-sessions__choices-bar .most-popular[data-v-0a9d5463] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  line-height: 22px;
  font-size: 12px;
  position: absolute;
  font-weight: bold;
  height: 30px;
  background: #a67424;
  color: #fff;
  text-transform: uppercase;
  padding: 3px 8px;
  white-space: nowrap;
  left: 50%;
  transform: translateX(-50%);
  top: -50px;
}
.purchase-sessions__choices-bar__item[data-v-0a9d5463] {
  font-size: 18px;
  line-height: 1.56;
  display: flex;
  flex-grow: 1;
  align-items: center;
  justify-content: center;
  height: 80px;
  position: relative;
  cursor: pointer;
  font-weight: 600;
  color: #333333;
  border-top: 2px solid #ececec;
  border-bottom: 2px solid #ececec;
  border-left: 2px solid #ececec;
}
.purchase-sessions__choices-bar__item.active + .purchase-sessions__choices-bar__item[data-v-0a9d5463] {
  border-left: none;
}
.purchase-sessions__choices-bar__item.active[data-v-0a9d5463] {
  font-weight: 600;
  background: #0567a7 !important;
  border: 2px solid #0567a7 !important;
  color: #fff;
}
.purchase-sessions__choices-bar__item.active .price-per-hour[data-v-0a9d5463] {
  display: flex;
}
.purchase-sessions__choices-bar__item[data-v-0a9d5463]:hover {
  background: #eeeeee;
}
.purchase-sessions__choices-bar__item[data-v-0a9d5463]:last-child {
  border-right: 2px solid #ececec;
}
.purchase-sessions__buttons[data-v-0a9d5463] {
  display: flex;
  gap: 16px;
  margin-top: 24px;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
@media (min-width: 960px) {
.purchase-sessions__buttons[data-v-0a9d5463] {
    flex-direction: row;
}
}
.purchase-sessions__buttons > a.btn[data-v-0a9d5463],
.purchase-sessions__buttons > button[data-v-0a9d5463] {
  width: 100%;
}
@media (min-width: 960px) {
.purchase-sessions__buttons > a.btn[data-v-0a9d5463],
  .purchase-sessions__buttons > button[data-v-0a9d5463] {
    width: auto;
}
}
.purchase-sessions__choices-responsive[data-v-0a9d5463] {
  display: block;
}
.purchase-sessions__choices-responsive__most-popular[data-v-0a9d5463] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  line-height: 22px;
  font-size: 12px;
  position: absolute;
  font-weight: bold;
  height: 30px;
  background: #a67424;
  color: #fff;
  text-transform: uppercase;
  padding: 3px 8px;
  white-space: nowrap;
  left: 50%;
  transform: translateX(-50%);
  top: -50px;
  top: 0;
  left: 0;
  position: inherit;
  width: 133px;
  margin: 0 auto 20px;
  transform: none;
}
.purchase-sessions__choices-responsive__most-popular--hidden[data-v-0a9d5463] {
  visibility: hidden;
}
.purchase-sessions__choices-responsive__dropdown[data-v-0a9d5463] {
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
  margin: 0 0 32px;
  width: 100%;
  height: auto;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  letter-spacing: -0.1px;
  color: #333333;
  box-shadow: 0 2px 4px 0 rgba(51, 51, 51, 0.12);
  border: solid 1px #0567a7;
  padding: 13px 18.5px 13px 16px;
  background-image: linear-gradient(45deg, transparent 50%, #0567a7 50%), linear-gradient(135deg, #0567a7 50%, transparent 50%);
  background-position: calc(100% - 27px) calc(1rem + 10px), calc(100% - 23px) calc(1rem + 10px);
  background-size: 4px 4px, 4px 4px;
  background-repeat: no-repeat;
}
@media (min-width: 960px) {
.purchase-sessions__choices-responsive[data-v-0a9d5463] {
    display: none;
}
}.buy-sessions-modal-heading[data-v-15aee29e] {
  font-size: 24px;
  line-height: 1.42;
  max-width: 620px;
  margin: 26px auto 32px;
  text-align: center;
}
@media (max-width: 960px) {
.buy-sessions-modal-heading[data-v-15aee29e] {
    font-size: 20px;
}
}
.buy-sessions-modal-social-proof[data-v-15aee29e] {
  font-size: 14px;
  font-weight: 600;
  line-height: 24px;
  letter-spacing: -0.1px;
  gap: 4px;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-top: 24px;
}
.buy-sessions-modal-container[data-v-15aee29e] {
  padding-bottom: 48px;
  font-size: 18px;
}
@media (max-width: 960px) {
.buy-sessions-modal-container[data-v-15aee29e] {
    padding-bottom: 32px;
}
}
.buy-sessions-modal-container[data-v-15aee29e] .session-picker-container {
  margin-top: 0;
}
@media (max-width: 960px) {
.buy-sessions-modal-container[data-v-15aee29e] .session-picker-container h4 {
    font-size: 18px;
}
}
@media (max-width: 960px) {
.buy-sessions-modal-container[data-v-15aee29e] .add-to-cart-btn {
    width: 100%;
}
}
.sessions-picker-preview-modal-avatar-container[data-v-15aee29e] {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
}
.sessions-picker-preview-modal-coach-name[data-v-15aee29e] {
  font-size: 16px;
  line-height: 1.5;
  font-weight: 600;
  margin-top: 8px;
}
.sessions-picker-preview-modal-spinner[data-v-15aee29e] {
  height: 610px !important;
}
[data-v-15aee29e] .disable-adding-to-cart .igao-btn {
  pointer-events: none !important;
}.buy-sessions-modal-heading[data-v-c685870f] {
  font-size: 24px;
  line-height: 1.42;
  max-width: 620px;
  margin: 26px auto 32px;
  text-align: center;
}
@media (max-width: 960px) {
.buy-sessions-modal-heading[data-v-c685870f] {
    font-size: 20px;
}
}
.buy-sessions-modal-social-proof[data-v-c685870f] {
  margin-top: 24px;
}
.buy-sessions-modal-container[data-v-c685870f] {
  padding-bottom: 48px;
  font-size: 18px;
}
@media (max-width: 960px) {
.buy-sessions-modal-container[data-v-c685870f] {
    padding-bottom: 32px;
}
}
.buy-sessions-modal-container[data-v-c685870f] .session-picker-container {
  margin-top: 0;
}
@media (max-width: 960px) {
.buy-sessions-modal-container[data-v-c685870f] .session-picker-container h4 {
    font-size: 18px;
}
}
@media (max-width: 960px) {
.buy-sessions-modal-container[data-v-c685870f] .add-to-cart-btn {
    width: 100%;
}
}
.sessions-picker-preview-modal-avatar-container[data-v-c685870f] {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
}
.sessions-picker-preview-modal-coach-name[data-v-c685870f] {
  font-size: 16px;
  line-height: 1.5;
  font-weight: 600;
  margin-top: 8px;
}
.sessions-picker-preview-modal-spinner[data-v-c685870f] {
  height: 610px !important;
}
[data-v-c685870f] .add-to-cart-btn {
  pointer-events: none !important;
}.coach-account-payments-group[data-v-b633c25d] {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
}
.coach-account-payments-group-title[data-v-b633c25d] {
  font-size: 18px;
  line-height: 1.56;
  font-style: normal;
  font-weight: 700;
}
.coach-account-payments-group-label[data-v-b633c25d] {
  font-size: 12px;
  line-height: 1.67;
  font-style: normal;
  font-weight: 600;
  margin-bottom: 4px;
}
.coach-account-payments-group-value[data-v-b633c25d] {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}
.coach-account-payments-group-value--checkboxes[data-v-b633c25d] {
  gap: 16px;
}
.coach-account-payments-group-base-price[data-v-b633c25d] {
  background: #eeeeee;
  color: #666666;
  display: flex;
  height: 40px;
  padding: 0px 16px;
  align-items: center;
}
.coach-account-payments-group-take-home-description ul[data-v-b633c25d] {
  margin: 8px 0 4px;
  padding-left: 20px;
}
.coach-account-payments-group-take-home-description ul li div[data-v-b633c25d] {
  display: inline;
}
.coach-account-payments-group-take-home-description ul li div img[data-v-b633c25d] {
  vertical-align: middle;
  transform: translateY(-2px);
}
.coach-account-payments-group-take-home-description-tooltip[data-v-b633c25d] {
  margin-left: 8px;
  display: flex;
}
.coach-account-payments-take-home-table-cell[data-v-b633c25d] {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.coach-account-payments-take-home-table-cell span + span[data-v-b633c25d] {
  font-size: 12px;
  line-height: 1.67;
  font-style: italic;
}
.coach-account-payments-take-home-table[data-v-b633c25d] {
  margin-top: 16px;
}
.coach-account-payments-take-home-table[data-v-b633c25d] .table-row-title {
  white-space: nowrap;
}
.coach-account-payments-take-home-table[data-v-b633c25d] .table-row--last {
  font-weight: 600;
}
[data-v-b633c25d] .align-flex-start {
  align-items: flex-start;
}
@media (min-width: 960px) {
[data-v-b633c25d] .table-row.table-row--begin,[data-v-b633c25d] .column-title.column-title--first {
    padding-left: 0;
}
}