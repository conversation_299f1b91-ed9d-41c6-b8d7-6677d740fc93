.visited-coaches[data-v-a6ee9153] {
  color: #333333;
  display: flex;
  padding: 24px 20px;
  flex-direction: column;
  align-items: center;
  gap: 32px;
  border: 1px solid #eeeeee;
  background: #fff;
}
@media (min-width: 960px) {
.visited-coaches[data-v-a6ee9153] {
    padding: 48px 40px;
}
}
.visited-coaches-title[data-v-a6ee9153] {
  align-self: flex-start;
  font-size: 18px;
  font-weight: 700;
  line-height: 28px;
}
.visited-coaches-list[data-v-a6ee9153] {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}
.visited-coaches-list-item[data-v-a6ee9153] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16px;
  border: 1px solid #eeeeee;
  background: #fbfbfb;
  padding: 16px;
}
.visited-coaches-list-item-coach[data-v-a6ee9153] {
  font-size: 12px;
  line-height: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
}
.visited-coaches-list-item-coach-name[data-v-a6ee9153] {
  color: #333333;
  font-size: 18px;
  font-weight: 700;
  line-height: 28px;
  letter-spacing: -0.1px;
  margin-bottom: 4px;
}
.visited-coaches-list-item-coach-price[data-v-a6ee9153] {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
}
.visited-coaches-list-item-coach-adding-price[data-v-a6ee9153] {
  color: #0567a7;
  font-size: 14px;
  font-weight: 700;
  line-height: 22px;
}
.visited-coaches-list-item-add[data-v-a6ee9153] {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
  flex-grow: 1;
}
.visited-coaches-list-item-add button[data-v-a6ee9153] {
  width: 100%;
}
@media (min-width: 960px) {
.visited-coaches-list-item-add button[data-v-a6ee9153] {
    width: auto;
}
}
.visited-coaches-list-item-add button > img[data-v-a6ee9153] {
  margin-right: 8px;
}
.visited-coaches-load-more button.btn[data-v-a6ee9153] {
  background-color: #fbfbfb;
}.empty-cart-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 34px auto;
}
@media (min-width: 960px) {
.empty-cart-container {
    margin: 74px auto;
    max-width: 34%;
}
}
.empty-cart-text {
  font-size: 32px;
  line-height: 1.31;
  font-weight: 600;
  color: #242424;
}
.empty-cart-browse-coaches-link {
  padding: 16px;
  width: 100%;
  margin-top: 24px;
}.checkout-cart-mobile-title[data-v-5e3f399b] {
  font-size: 24px;
  line-height: 1.42;
  font-weight: 600;
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}
@media (min-width: 960px) {
.checkout-cart-mobile-title[data-v-5e3f399b] {
    display: none;
}
}
.checkout-cart-container[data-v-5e3f399b] {
  padding-top: 34px;
}
@media (min-width: 960px) {
.checkout-cart-container[data-v-5e3f399b] {
    padding-top: 80px;
}
}
.checkout-cart-table-header[data-v-5e3f399b] {
  display: grid;
  grid-template-columns: 1fr 80px;
  justify-items: center;
  padding-left: 40px;
  padding-right: 40px;
  font-size: 12px;
  line-height: 1.67;
  font-weight: 700;
  margin-bottom: 12px;
}
@media (min-width: 960px) {
.checkout-cart-table-header[data-v-5e3f399b] {
    grid-template-columns: 1fr 80px 160px 80px;
}
}
.checkout-cart-table-header > div[data-v-5e3f399b] {
  display: flex;
}
.checkout-cart-table-header > div[data-v-5e3f399b]:first-child {
  justify-self: flex-start;
}
.checkout-cart-table-header > div[data-v-5e3f399b]:last-child {
  justify-self: flex-end;
}
@media (max-width: 960px) {
.checkout-cart-table-header[data-v-5e3f399b] {
    display: none;
}
}
.checkout-cart-cart-items[data-v-5e3f399b] {
  border: 1px solid #eeeeee;
  background-color: #fff;
}
.checkout-cart-table-item--flexible-session .checkout-cart-item-image[data-v-5e3f399b] {
  border-radius: 50%;
}
.checkout-cart-table-item[data-v-5e3f399b] {
  display: grid;
  grid-template-columns: 1fr 80px;
  justify-items: center;
  padding-left: 40px;
  padding-right: 40px;
  font-size: 14px;
  line-height: 1.57;
  padding: 20px;
}
@media (min-width: 960px) {
.checkout-cart-table-item[data-v-5e3f399b] {
    grid-template-columns: 1fr 80px 160px 80px;
}
}
.checkout-cart-table-item > div[data-v-5e3f399b] {
  display: flex;
}
.checkout-cart-table-item > div[data-v-5e3f399b]:first-child {
  justify-self: flex-start;
}
.checkout-cart-table-item > div[data-v-5e3f399b]:last-child {
  justify-self: flex-end;
}
@media (min-width: 960px) {
.checkout-cart-table-item[data-v-5e3f399b] {
    padding: 40px;
}
}
.checkout-cart-item-product-column[data-v-5e3f399b] {
  grid-row: 1/3;
}
@media (min-width: 960px) {
.checkout-cart-item-product-column[data-v-5e3f399b] {
    grid-row: unset;
}
}
.checkout-cart-item-quantity-column[data-v-5e3f399b] {
  justify-self: flex-end;
}
@media (min-width: 960px) {
.checkout-cart-item-quantity-column[data-v-5e3f399b] {
    justify-self: center;
}
}
.checkout-cart-item-product-info[data-v-5e3f399b] {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-left: 16px;
}
@media (min-width: 960px) {
.checkout-cart-item-product-info[data-v-5e3f399b] {
    padding-left: 32px;
}
}
.checkout-cart-item-name[data-v-5e3f399b] {
  font-size: 14px;
  line-height: 1.57;
  font-weight: 700;
  color: #4a4a4a;
}
@media (min-width: 960px) {
.checkout-cart-item-name[data-v-5e3f399b] {
    font-size: 18px;
    line-height: 1.56;
}
}
.checkout-cart-item-description[data-v-5e3f399b] {
  font-size: 12px;
  line-height: 1.67;
}
.checkout-cart-item-description > span[data-v-5e3f399b]:first-child {
  margin-right: 16px;
}
.checkout-cart-item-discounts[data-v-5e3f399b] {
  display: inline-flex;
  flex-direction: column;
  gap: 4px;
}
.checkout-cart-item-remove[data-v-5e3f399b] {
  font-size: 12px;
  line-height: 1.67;
  text-transform: uppercase;
  font-weight: 700;
}
.checkout-cart-item-discount[data-v-5e3f399b] {
  color: #0567a7;
  display: block;
}
.checkout-cart-item-discount img[data-v-5e3f399b] {
  vertical-align: middle;
}
@media (min-width: 960px) {
.checkout-cart-item-discount[data-v-5e3f399b] {
    display: inline-block;
}
}
.checkout-cart-item-price-column[data-v-5e3f399b] {
  flex-direction: column;
}
@media (max-width: 960px) {
.checkout-cart-item-price-column[data-v-5e3f399b] {
    display: none !important;
}
}
.checkout-cart-item-original-price[data-v-5e3f399b] {
  font-size: 12px;
  line-height: 1.67;
  text-decoration: line-through;
}
.checkout-cart-item-image[data-v-5e3f399b] {
  max-width: 100%;
  height: auto;
}
.checkout-cart-item-quantity-dropdown[data-v-5e3f399b] {
  min-width: 60px;
}
.checkout-cart-item-quantity-dropdown[data-v-5e3f399b] .choices__list[role="listbox"] {
  scrollbar-gutter: stable;
}
.checkout-cart-item-quantity-dropdown[data-v-5e3f399b] .choices__item {
  font-weight: normal !important;
}
.checkout-cart-item-quantity-dropdown[data-v-5e3f399b] .price-description {
  font-size: 12px;
  line-height: 1.67;
  display: none;
  color: #878787;
  padding-left: 6px;
}
.checkout-cart-item-quantity-dropdown[data-v-5e3f399b] .choices__list--dropdown {
  width: auto;
  right: 0;
}
.checkout-cart-item-quantity-dropdown[data-v-5e3f399b] .choices__list--dropdown .choices__item {
  white-space: nowrap !important;
}
.checkout-cart-item-quantity-dropdown[data-v-5e3f399b] .choices__list--dropdown .price-description {
  color: #333333;
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
  flex-grow: 1;
  font-size: 14px;
  line-height: 22px;
  gap: 8px;
}
.checkout-cart-item-quantity-dropdown[data-v-5e3f399b] .choices__list--dropdown .price-description-price {
  font-weight: 600;
}
.checkout-cart-item-quantity-dropdown[data-v-5e3f399b] .choices__list--dropdown .price-description-original-price {
  font-size: 12px;
  text-decoration: line-through;
  color: #878787;
  margin-right: 8px;
}
.checkout-cart-mobile-checkout[data-v-5e3f399b] {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  border-top: 6px solid rgba(0, 0, 0, 0.05);
  background-color: #fff;
  padding: 12px;
}
.checkout-cart-mobile-checkout[data-v-5e3f399b] .btn {
  width: 100%;
}
@media (min-width: 960px) {
.checkout-cart-mobile-checkout[data-v-5e3f399b] {
    display: none;
}
}
.checkout-cart-summary[data-v-5e3f399b] {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  align-items: flex-start;
  gap: 16px;
  margin-top: 12px;
  margin-bottom: 100px;
}
.checkout-cart-summary-visited-coaches[data-v-5e3f399b] {
  flex-grow: 1;
}
.checkout-cart-summary-card[data-v-5e3f399b] {
  border: 1px solid #eeeeee;
  display: flex;
  flex-direction: column;
  padding: 20px;
  background-color: #fff;
  width: 100%;
}
.checkout-cart-summary-card hr[data-v-5e3f399b] {
  margin: 20px 0;
  border: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
@media (min-width: 960px) {
.checkout-cart-summary-card[data-v-5e3f399b] {
    padding: 40px;
    width: 500px;
}
}
.checkout-cart-summary-discount-form[data-v-5e3f399b] {
  position: relative;
}
.checkout-cart-summary-discount-form .checkout-cart-summary-discount-input[data-v-5e3f399b] {
  margin: 0;
}
.checkout-cart-summary-discount-form .checkout-cart-summary-discount-input[data-v-5e3f399b] input {
  padding-right: 90px;
}
.checkout-cart-summary-discount-form .checkout-cart-summary-discount-button[data-v-5e3f399b] {
  position: absolute;
  top: 50%;
  right: 8px;
  transform: translateY(-50%);
}
.checkout-cart-summary-error[data-v-5e3f399b] {
  font-size: 12px;
  line-height: 1.67;
  color: #c63120;
  margin: 4px 0 16px;
}
.checkout-cart-summary-subtotal[data-v-5e3f399b],
.checkout-cart-summary-total[data-v-5e3f399b] {
  display: flex;
  justify-content: space-between;
}
.checkout-cart-summary-subtotal[data-v-5e3f399b] {
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 16px;
}
.checkout-cart-summary-total[data-v-5e3f399b] {
  font-size: 18px;
  line-height: 1.56;
  font-weight: 700;
}
.checkout-cart-summary-tax[data-v-5e3f399b] {
  font-size: 12px;
  line-height: 1.67;
  display: flex;
  justify-content: flex-end;
}
.checkout-cart-summary-total-savings[data-v-5e3f399b] {
  margin-top: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #0567a7;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px;
  text-transform: uppercase;
}
.checkout-cart-summary-reassurance[data-v-5e3f399b] {
  margin-top: 24px;
  font-size: 14px;
  line-height: 22px;
  align-self: flex-start;
}
.checkout-cart-summary-checkout[data-v-5e3f399b] {
  flex-direction: column;
  display: none;
}
@media (min-width: 960px) {
.checkout-cart-summary-checkout[data-v-5e3f399b] {
    display: flex;
}
}
.checkout-cart-summary-applied-discount-code[data-v-5e3f399b] {
  font-size: 14px;
  line-height: 1.57;
  display: flex;
  align-items: center;
  color: #0567a7;
}
.checkout-cart-summary-applied-discount-code img[data-v-5e3f399b] {
  margin-right: 2px;
}
.checkout-cart-summary-applied-discount-code[data-v-5e3f399b] a {
  font-size: 12px;
  line-height: 1.67;
  font-weight: 700;
  margin-left: 4px;
}.content[data-astro-cid-s26vb74q]{max-width:1040px;margin:0 auto;padding:0 16px}