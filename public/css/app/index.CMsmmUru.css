.coach-details-page-availability {
  box-shadow: 0 1px 2px 0 rgba(51, 51, 51, 0.04);
  border: solid 1px #eeeeee;
  min-height: 600px;
  margin-top: 24px;
}
@media (min-width: 960px) {
.coach-details-page-availability {
    margin-top: 32px;
}
}
.availability-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  font-size: 14px;
  font-weight: 600;
  color: #333333;
  padding: 10px 16px;
  text-align: left;
  border-bottom: solid 1px #eee;
  background-color: #fff;
}
.availability-header .availability-header-title {
  margin-right: 6px;
}
.availability-header .availability-header-help-text {
  font-size: 14px;
  font-weight: normal;
  color: #666;
}
#bookingjs {
  box-shadow: none;
  margin: 0;
  border-radius: 0;
  border: none !important;
}
#bookingjs .bookingjs-loading {
  height: calc(100% - 84px);
}
#bookingjs .bookingjs-timezoneicon {
  display: none;
}
#bookingjs .bookingjs-footer {
  border-top: solid 1px #eee;
  padding: 5px 16px 14px;
}
#bookingjs .bookingjs-footer-tz {
  font-size: 12px;
  font-weight: 600;
  color: #878787;
  width: 100%;
}
#bookingjs .bookingjs-footer-tz-picker-arrowdown {
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 1;
  left: -24px;
  top: 0;
}
#bookingjs .bookingjs-footer-tz-picker-arrowdown svg g {
  fill: #333;
}
#bookingjs .fc-toolbar.fc-header-toolbar {
  display: none;
}
#bookingjs .bookingjs-footer-tz-picker {
  margin-top: 2px;
  display: flex;
  width: 100%;
}
#bookingjs .bookingjs-footer-tz-picker-select {
  cursor: pointer;
  border-radius: 0;
  border-color: #d5d4d4;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.57;
  letter-spacing: normal;
  color: #333;
  padding: 4px 12px;
  min-width: 100%;
}
@media (min-width: 960px) {
#bookingjs .bookingjs-footer-tz-picker-select {
    min-width: 280px;
}
}
#bookingjs .bookingjs-error .bookingjs-error-inner {
  top: 25%;
  overflow: inherit;
}
#bookingjs .bookingjs-error .bookingjs-error-text {
  overflow: inherit;
}
#bookingjs .fc-widget-header {
  display: flex;
  justify-content: space-between;
  padding: 24px 0 12px;
  font-size: 16px !important;
  line-height: 1.5 !important;
}
#bookingjs .fc-list-heading-main {
  color: #0567a7;
}
#bookingjs .fc-list-heading-alt {
  color: #aaaaaa;
}
#bookingjs .fc-list-item {
  font-size: 12px;
  font-weight: normal;
  line-height: 1.67;
  color: #333333;
  border-left: 2px solid #c5c5c5;
}
.fc-list-table td.slot-expiration-td {
  width: 100%;
  text-align: right;
  padding-right: 11px;
}
.fc-list-table td.slot-expiration-td img {
  vertical-align: middle;
  margin: 0 0 0 0.25rem;
}
.slot-expiration-text {
  font-size: 12px;
  font-style: italic;
  font-weight: 600;
  line-height: 1.67;
  color: #d69121;
}
.fc-list-item:hover td.slot-expiration-td {
  transform: none;
  transform: translate(0px);
}
.card-icon {
  width: 16px;
  height: 16px;
  margin-right: 0.75rem;
}
@media (max-width: 960px) {
main[role=main].coach-account-availability {
    padding: 0;
}
.main-header.coach-account-availability {
    margin: 0;
}
}
.availability-modal .availability-modal-body {
  padding: 0px !important;
}
.availability-modal #bookingjs .bookingjs-footer {
  background: #fff;
  padding: 7px 24px 8px;
}
@media (max-width: 960px) {
.availability-modal #bookingjs .bookingjs-footer {
    padding: 7px 16px 8px;
}
}
.availability-modal #bookingjs .fc-view-container .fc-view .fc-scroller {
  padding: 0 24px;
}
@media (max-width: 960px) {
.availability-modal #bookingjs .fc-view-container .fc-view .fc-scroller {
    padding: 0 16px;
}
}
.tooltip {
  position: absolute;
  z-index: 7777;
  display: block;
  margin: 0;
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  opacity: 0;
}
.tooltip.show {
  opacity: 1;
}
.tooltip .arrow {
  position: absolute;
  display: block;
  width: 0.8rem;
  height: 0.4rem;
}
.tooltip .tooltip-inner {
  max-width: 300px;
  padding: 0.8125rem 0.75rem;
  text-align: center;
  background-color: #ffffff;
  font-size: 0.75rem;
  font-weight: 600;
  color: #333333;
  box-shadow: 0 2px 4px 0 rgba(51, 51, 51, 0.12) !important;
  border: solid 1px #eeeeee !important;
}
.bs-tooltip-bottom,
.bs-tooltip-auto[x-placement^=bottom] {
  padding: 0.4rem 0;
}
.fade {
  transition: opacity 0.15s linear;
}